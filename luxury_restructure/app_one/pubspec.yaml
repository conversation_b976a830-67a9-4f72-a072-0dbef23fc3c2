name: app_one
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.3.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  presentation:
    path: ../presentation
  presentation_common_ui:
    path: ../presentation_common_ui
  domain:
    path: ../domain
  awesome_dialog: ^3.2.1
  dartz: ^0.10.1
  bottom_navy_bar: ^6.1.0
  cached_network_image: ^3.4.1
  carousel_slider: ^5.0.0
  connectivity_plus: ^4.0.2
  datetime_picker_formfield: ^2.0.1
  device_info_plus: ^11.4.0
  #  firebase_core: ^2.15.1
  #  firebase_crashlytics: ^3.3.5
  #  firebase_dynamic_links: ^5.3.5
  #  firebase_messaging: ^14.6.7
  #  firebase_performance: ^0.9.2+5
  flutter_dotenv: ^5.2.1
  flutter_html: ^3.0.0
  flutter_local_notifications: ^19.2.0
  flutter_signin_button: ^2.0.0
  flutter_spinkit: ^5.2.1
  flutter_svg: ^2.1.0
  get: ^4.7.2
  get_storage: ^2.1.1
  intl: ^0.20.2
  jiffy: ^6.4.3
  json_annotation: ^4.9.0
  livechatt: ^1.5.1
  lottie: ^3.3.1
  package_info_plus: ^8.3.0
  photo_view: ^0.15.0
  sentry_dio: ^8.14.2
  sentry_flutter: ^8.14.2
  sms_autofill: ^2.4.1
  transparent_image: ^2.0.1
  #  firebase_analytics: ^10.4.5
  url_launcher: ^6.3.1
  #  firebase_in_app_messaging: ^0.7.3+5
  provider: ^6.1.5
  shared_preferences: ^2.5.3
  #  firebase_remote_config: ^4.2.5
  base_controller:
    path: ../base_controller

dev_dependencies:
  flutter_driver:
    sdk: flutter
  integration_test:
    sdk: flutter
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - ./env
  #    - assets/images/bag.png
  #    - assets/images/india_flag.png
  #    - assets/images/magnifying_glass.png
  #    - assets/images/menu.png
  #    - assets/images/user.png
  #    - assets/images/brands_labels.png
  #    - assets/images/
  #    - assets/icons/
  #    - assets/icon/
  #    - assets/json/
  ##    - assets/images/header_logo.png
  #    - assets/images/flag.png
  #    - assets/icons/cart.svg
  #    - assets/icons/search.svg
  #    - assets/icons/user1.png
  #    - assets/flags/
  #    - assets/animations/
  #    - assets/svgs/
  #    - assets/images/home/
  #    - assets/images/homeImages/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/Poppins/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/Poppins/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/Poppins/Poppins-ExtraBold.ttf
          weight: 900
    - family: Montserrat
      fonts:
        - asset: assets/fonts/Montserrat/Montserrat-Light.ttf
          weight: 300
        - asset: assets/fonts/Montserrat/Montserrat-Regular.ttf
          weight: 400
        - asset: assets/fonts/Montserrat/Montserrat-Medium.ttf
          weight: 500
        - asset: assets/fonts/Montserrat/Montserrat-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Montserrat/Montserrat-Bold.ttf
          weight: 700
        - asset: assets/fonts/Montserrat/Montserrat-ExtraBold.ttf
          weight: 900
    - family: OpenSans
      fonts:
        - asset: assets/fonts/OpenSans/OpenSans-Light.ttf
          weight: 300
        - asset: assets/fonts/OpenSans/OpenSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/OpenSans/OpenSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/OpenSans/OpenSans-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/OpenSans/OpenSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/OpenSans/OpenSans-ExtraBold.ttf
          weight: 900
    - family: Brasley
      fonts:
        - asset: assets/fonts/Brasley/Brasley-Light.ttf
          weight: 300
        - asset: assets/fonts/Brasley/Brasley-Regular.ttf
          weight: 400
        - asset: assets/fonts/Brasley/Brasley-Medium.ttf
          weight: 500
        - asset: assets/fonts/Brasley/Brasley-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Brasley/Brasley-Bold.ttf
          weight: 700
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
