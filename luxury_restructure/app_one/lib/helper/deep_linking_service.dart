
import 'package:app_one/app_constants/app_constants.dart';
import 'package:app_one/core/config/flavor_config.dart';
import 'package:app_one/core/consts/app_constants.dart';
// import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

class DynamicLinkingService {
  // for building deep link
  static Future<String> buildProductPageDynamicLink(String sku) async {
    // try {
    //   debugPrint("building dyn link for sku : $sku");
    //   final dynamicLinkParameters = DynamicLinkParameters(
    //       link: Uri.parse(
    //           "${FlavorConfig.instance.values.baseUrl}/productDetails/$sku"),
    //       uriPrefix: AppConstants.deeplinkBaseUrl,
    //       androidParameters:
    //           const AndroidParameters(packageName: DEEPLINK_PACKAGE_NAME),
    //       iosParameters: const IOSParameters(
    //           bundleId: DEEPLINK_PACKAGE_NAME, appStoreId: "1645310795"));
    //   final shortLink = await FirebaseDynamicLinks.instance
    //       .buildShortLink(dynamicLinkParameters);
    //   final link = shortLink.shortUrl.toString();
    //   debugPrint("dynamic link : $link");
    //   return link;
    // } catch (e) {
    //   debugPrint("error in dynamic link service ${e.toString()}");
    //   rethrow;
    // }
    return "link";
  }

  // static Future<PendingDynamicLinkData?>? getInitDynamicLinks() async {
  //   final PendingDynamicLinkData? initialLink =
  //       await FirebaseDynamicLinks.instance.getInitialLink();
  //   return initialLink;
  // }

  static void registerDynamicLinkHandler() {
    // FirebaseDynamicLinks.instance.onLink.listen((event) {
    //   final paths = event.link.pathSegments;
    //   debugPrint(paths.toString());
    //   Get.toNamed<void>(event.link.path);
    // });
  }
}
