import 'package:app_one/app_pages/app_routes.dart';
import 'package:app_one/binding/affiliate_program_binding.dart';
import 'package:app_one/binding/alphabetic_brand_list_binding.dart';
import 'package:app_one/binding/app_secure_shopping_screen_binding.dart';
import 'package:app_one/binding/charity_screen_binding.dart';
import 'package:app_one/binding/checkout_binding.dart';
import 'package:app_one/binding/country_pop_up_screen_bindind.dart';
import 'package:app_one/binding/dashboard_screen_binding.dart';
import 'package:app_one/binding/designer_page_binding.dart';
import 'package:app_one/binding/faq_screen_binding.dart';
import 'package:app_one/binding/forgot_password_binding.dart';
import 'package:app_one/binding/home_binding.dart';
import 'package:app_one/binding/my_account_info_binding.dart';
import 'package:app_one/binding/my_tickets_binding.dart';
import 'package:app_one/binding/notification_binding.dart';
import 'package:app_one/binding/search_binding.dart';
import 'package:app_one/binding/select_country_binding.dart';
import 'package:app_one/binding/shipping_binding.dart';
import 'package:app_one/binding/signup_binding.dart';
import 'package:app_one/binding/store_credit_binding.dart';
import 'package:app_one/binding/terms_condition_binding.dart';
import 'package:app_one/binding/track_your_order_by_guest_binding.dart';
import 'package:app_one/screens/alphabatic_brand_list_screen/alphabatic_brand_list_screen.dart';
import 'package:app_one/screens/app_secure_shopping_screen/app_secure_shopping_screen.dart';
import 'package:app_one/screens/brands_and_label_search_screen/brands_and_label_search_screen.dart';
import 'package:app_one/screens/charity_screen/charity_screen.dart';
import 'package:app_one/screens/checkout_order/checkout_order_screen.dart';
import 'package:app_one/screens/count_pop_up_screen/country_pop_up_screen.dart';
import 'package:app_one/screens/designer_screen/designer_screen.dart';
import 'package:app_one/screens/exchange_and_return_screen/exchange_and_return_screen.dart';
import 'package:app_one/screens/faq_screen/faq_screen.dart';
import 'package:app_one/screens/home/<USER>';
import 'package:app_one/screens/login_screen/login_screen.dart';
import 'package:app_one/screens/privacy_policy/privacy_policy.dart';
import 'package:app_one/screens/select_country_screen/select_country_screen.dart';
import 'package:app_one/screens/signup_screen/signup_screen.dart';
import 'package:app_one/screens/special_request/request_received_screen.dart';
import 'package:app_one/screens/special_request/special_reques_binding.dart';
import 'package:app_one/screens/special_request/special_request_screen.dart';
import 'package:app_one/screens/store_credit/store_credit.dart';
import 'package:app_one/screens/terms_condition/terms_condition_screen.dart';
import 'package:app_one/screens/track_your_ticket_mail/trace_your_ticket_mail_screen.dart';

import 'package:presentation_common_ui/common_screens/common_refer_friend_screen/common_refer_friend_screen.dart';
import 'package:presentation_common_ui/common_screens/common_trace_my_ticket_screen/common_trace_my_ticket_screen.dart';
import 'package:presentation_common_ui/common_screens/common_privacy_policy_screen/privacy_policy_binding.dart';
import 'package:presentation_common_ui/common_screens/common_track_your_order_by_guest_screen/common_track_your_order_by_guest_screen.dart';

import 'package:presentation_common_ui/common_screens/common_my_ticket/my_tickets_screen.dart';
import 'package:presentation_common_ui/constants/app_constants.dart';

import '../all_imports.dart';
import '../app_constants/app_constants.dart';
import '../binding/add_address_binding.dart';
import '../binding/address_book_binding.dart';
import '../binding/brand_list_detail_binding.dart';
import '../binding/cart_screen_binding.dart';
import '../binding/contact_us_screen_binding.dart';
import '../binding/country_screen_binding.dart';
import '../binding/influencer_binding.dart';
import '../binding/login_binding.dart';
import '../binding/my_account_screen_binding.dart';
import '../binding/my_coupons_binding.dart';
import '../binding/my_orders_binding.dart';
import '../binding/product_detail_screen_binding.dart';
import '../binding/refer_friend_binding.dart';
import '../binding/returen_and_refund_binding.dart';
import '../binding/splash_binding.dart';
import '../binding/trace_your_ticket_mail_binding.dart';
import '../binding/track_your_order_binding.dart';
import '../binding/wish_list_binding.dart';
import '../core/consts/app_constants.dart';
import '../screens/add_address_screen/add_address_screen.dart';
import '../screens/address_book/address_book.dart';
import '../screens/affiliate_program/affiliate_program_screen.dart';
import '../screens/brand_list_detail_screen/brand_list_detail_screen.dart';
import '../screens/cart_screen/cart_screen.dart';
import '../screens/contact_us/contact_us.dart';
import '../screens/country_screen/country_screen.dart';
import '../screens/dashboard_screen/dashboard_screen.dart';
import '../screens/influencer_registraction/influencer_registration_screen.dart';
import '../screens/my_account_info/my_account_info.dart';
import '../screens/my_account_screen/my_accounts_page.dart';
import '../screens/my_account_screen/theme_change_toggle.dart';
import '../screens/my_coupons/my_coupons_screen.dart';
import '../screens/my_orders/my_orders_screen.dart';
import '../screens/notification/notification_screen.dart';
import '../screens/product_detail_screen/product_detail_screen.dart';
import '../screens/return_and_refund_screen/return_and_refund_screen.dart';
import '../screens/shipping/shipping_screen.dart';
import '../screens/splash_screen.dart';
import '../screens/track_your_order/track_your_order_screen.dart';
import '../screens/wishlist_screen/wishlist_screen.dart';

import 'package:presentation_common_ui/common_screens/common_forgot_password_screen/common_forgot_password_screen.dart';
import 'package:app_one/binding/track_ticket_binding.dart';

class AppPages {
  static final routes = <GetPage>[
    GetPage(
      name: RoutesConstants.loginScreen,
      page: () => const LoginScreen(),
      binding: LoginScreenBinding(),
    ),
    GetPage(
      name: RoutesConstants.splashScreen,
      page: () => const SplashScreen(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: RoutesConstants.dashboardScreen,
      page: () => const DashboardScreen(),
      binding: DashboardBindings(),
    ),
    GetPage(
      name: RoutesConstants.homeScreen,
      page: () => const HomeScreen(),
      binding: HomeBindings(),
    ),
    GetPage(
      name: RoutesConstants.cartScreen,
      page: () => CartScreen(),
      binding: CartScreenBinding(),
    ),
    GetPage(
      name: RoutesConstants.productDetailsScreen,
      page: () => const ProductDetailScreen(),
      binding: ProductDetailScreenBinding(),
    ),
    GetPage(
      name: RoutesConstants.trackTicketScreen,
      page: () => const CommonTrackTicketScreen(
        projectName: APP_NAME,
        /*   appRoute: RoutesConstants.myAccountMenuScreen,*/
      ),
      binding: TrackTicketBinding(),
    ),
    GetPage(
      name: RoutesConstants.brandScreen,
      page: () => const DesignerScreen(),
      binding: DesignerPageBinding(),
    ),
    GetPage(
      name: RoutesConstants.brandDetailsScreen,
      page: () => const BrandListDetailScreen(),
      binding: BrandListDetailBinding(),
    ),
    GetPage(
      name: RoutesConstants.alphabeticBrandList,
      page: () => const AlphabeticBrandListScreen(),
      binding: AlphabeticBrandListBinding(),
    ),
    GetPage(
      name: RoutesConstants.myAccountMenuScreen,
      page: () => const MyAccountsPage(
          /*MyAccountPageNavigation(
              contactUsScreenPath: RoutesConstants.contactUsScreen,
              loginScreenRoute: RoutesConstants.loginScreen,
              signupScreenRoute: RoutesConstants.signUpScreen,
              myWishListScreenRoute: RoutesConstants.wishlistScreen,
              addressBookScreenRoute: '',
              //RoutesConstants.addressBookScreen,
              accountInformationScreenRoute: RoutesConstants.myAccountScreen,
              myStoreCreditScreenRoute: RoutesConstants.storeCreditScreen,
              myCouponsScreenRoute: RoutesConstants.myCouponsScreen,
              trackOrderScreenRoute: RoutesConstants.trackYourOrderScreen,
              notificationScreenRoute: RoutesConstants.notificationScreen,
              secureShoppingScreenRoute: '',
              //RoutesConstants.appSecureShoppingScreen,
              trackYourOrderByGuestScreenRoute:
                  RoutesConstants.guestReturnsScreen,
              hopeScreenRoute: RoutesConstants.charityScreen,
              affiliateProgramScreenRoute:
                  RoutesConstants.affiliateProgramScreen,
              influenceRegistrationScreenRoute:
                  RoutesConstants.influencerRegistrationScreen,
              exchangeAndReturnsScreenRoute: '',
              privacyPolicyScreenRoute: RoutesConstants.privacyPolicyScreen,
              termsAndConditionsScreenRoute:
                  RoutesConstants.termsConditionScreen,
              shippingScreenRoute: RoutesConstants.shippingScreen,
              trackYourTicketByEmailScreenRoute:
                  RoutesConstants.traceYourTicketMail,
              trackTicketScreenRoute: RoutesConstants.trackTicketScreen,
              aboutUsScreenRoute: '',
              referFriendScreenRoute: RoutesConstants.referFriendScreen,
              returnsAndRefundScreenRoute:
                  RoutesConstants.returnsAndRefundsScreen,
              faqScreenRoute: RoutesConstants.faqScreen,
              liveChatScreenRoute: '',
              myOrdersScreen: RoutesConstants.myOrderScreen,
              countryRoute: RoutesConstants.countryScreen,
              myTicketScreen: RoutesConstants.myTicketsScreen,
              advancedsearch: '',
              giftvouchers: '',
              splashScreenRoute: RoutesConstants.dashboardScreen),
          const ThemeChangeToggle(),
          Container()
          // showChatDialog(context)*/
          ),
      binding: MyAccountScreenBinding(),
    ),
    GetPage(
      name: RoutesConstants.contactUsScreen,
      page: () => const ContactUs(),
      binding: ContactUsScreenBinding(),
    ),
    GetPage(
      name: RoutesConstants.myTicketsScreen,
      page: () => const MyTicketScreen(
        projectName: APP_ONE_NAME,
      ),
      binding: MyTicketsBindings(),
    ),
    GetPage(
      name: RoutesConstants.signUpScreen,
      page: () => const SignupScreen(),
      binding: SignupBinding(),
    ),

    GetPage(
        name: RoutesConstants.appSecureShoppingScreen,
        page: () => const AppSecureShoppingScreen(),
        binding: AppSecureShoppingScreenBinding()),
    // GetPage(
    //     name: RoutesConstants.guestReturnsScreen,
    //     page: () => const AppSecureShoppingScreen(),
    //     binding: AppSecureShoppingScreenBinding()),
    GetPage(
        name: RoutesConstants.charityScreen,
        page: () => CharityScreen(),
        binding: CharityScreenBinding()),
    GetPage(
      name: RoutesConstants.selectCountryScreen,
      page: () => const SelectCountryScreen(),
      binding: SelectCountryBinding(),
    ),
    GetPage(
      name: RoutesConstants.myAccountScreen,
      page: () => const MyAccountInfoScreen(),
      binding: MyAccountInfoScreenBinding(),
    ),
    GetPage(
      name: RoutesConstants.countryPopUpScreen,
      page: () => CountryPopUpScreen(),
      binding: CountryPopUpScreenBinding(),
    ),
    GetPage(
      name: RoutesConstants.wishlistScreen,
      page: () => const WishListScreen(),
      binding: WishListBinding(),
    ),
    GetPage(
      name: RoutesConstants.countryScreen,
      page: () => CountryScreen(),
      binding: CountryScreenBinding(),
    ),
    GetPage(
      name: RoutesConstants.storeCreditScreen,
      page: () => const StoreCreditScreen(),
      binding: StoreCreditBindings(),
    ),
    GetPage(
      name: RoutesConstants.myOrderScreen,
      page: () => const MyOrdersScreen(),
      binding: MyOrdersBinding(),
    ),
    GetPage(
      name: RoutesConstants.myCouponsScreen,
      page: () => const MyCouponsScreen(),
      binding: MyCouponsBindings(),
    ),
    GetPage(
      name: RoutesConstants.trackYourOrderScreen,
      page: () => const TrackYourOrderScreen(),
      binding: TrackYourOrderBinding(),
    ),
    GetPage(
      name: RoutesConstants.notificationScreen,
      page: () => const NotificationScreen(),
      binding: NotificationBinding(),
    ),
    GetPage(
      name: RoutesConstants.affiliateProgramScreen,
      page: () => AffiliateProgramScreen(),
      binding: AffiliateProgramBindings(),
    ),
    GetPage(
        name: RoutesConstants.influencerRegistrationScreen,
        page: () => const InfluencerRegistrationScreen(),
        binding: InfluencerRegistrationBindings()),
    GetPage(
      name: RoutesConstants.privacyPolicyScreen,
      page: () => const PrivacyPolicy(),
      binding: PrivacyPolicyBinding(baseUrl: AppConstants.apiEndPointLogin),
    ),
    GetPage(
      name: RoutesConstants.termsConditionScreen,
      page: () => const TermAndConditionScreen(),
      binding: TermAndConditionBinding(),
    ),
    GetPage(
      name: RoutesConstants.shippingScreen,
      page: () => const ShippingScreen(),
      binding: ShippingBinding(),
    ),
    GetPage(
      name: RoutesConstants.traceYourTicketMail,
      page: () => const TraceYourTicketMail(),
      binding: TrackYourTicketEmailBinding(),
    ),
    GetPage(
      name: RoutesConstants.referFriendScreen,
      page: () => const CommonReferFriendScreen(projectName: APP_NAME),
      binding: ReferFriendBindings(),
    ),
    GetPage(
      name: RoutesConstants.returnsAndRefundsScreen,
      page: () => const ReturnAndRefundScreen(),
      binding: ReturnAndReferBinding(),
    ),
    GetPage(
      name: RoutesConstants.faqScreen,
      page: () => const FaqScreen(),
      binding: FaqBinding(),
    ),
    GetPage(
      name: RoutesConstants.searchScreen,
      page: () => const BrandsAndLabelSearchScreen(),
      binding: SearchBinding(),
    ),
    GetPage(
      name: RoutesConstants.specialRequestScreen,
      page: () => const SpecialRequestScreen(),
      binding: SpecialRequestBinding(),
    ),
    GetPage(
      name: RoutesConstants.requestReceivedScreen,
      page: () => const RequestReceivedScreen(),
    ),

    // GetPage(
    //     name: RoutesConstants.addAdressScreen,
    //     page: () => const AddAddressScreen(),
    //     binding: AddAddressBinding()),
    // GetPage(
    //     name: RoutesConstants.addressBookScreen,
    //     page: () => const AddressBookScreen(),
    //     binding: AddressBookBindings()),
    // GetPage(
    //   name: RoutesConstants.emailPasswordInvalidScreen,
    //   page: () => const EmailPasswordInvalidScreen(),
    // ),
    GetPage(
        name: RoutesConstants.guestReturnsScreen,
        page: () => CommonTrackYourOrderByGuestScreen(
              loadingColor: primaryColor,
              projectName: APP_NAME,
              apiEndPoint: AppConstants.apiEndPointLogin,
            ),
        /*CommonTrackYourOrderByGuestScreen(loadingColor: darkBlueColor),*/
        binding: TrackYourOrderBuGuestBinding()),
    GetPage(
      name: RoutesConstants.addAdressScreen,
      page: () => const AddAddressScreen(),
      binding: AddAddressBinding(),
    ),
    GetPage(
      name: RoutesConstants.addressBookScreen,
      page: () => const AddressBookScreen(),
      binding: AddressBookBindings(),
    ),
    GetPage(
      name: RoutesConstants.checkoutOrderScreen,
      page: () => CheckoutOrderScreen(),
      binding: CheckoutOrderBindings(),
    ),
    GetPage(
      name: RoutesConstants.forgotPasswordScreen,
      page: () => CommonForgotPasswordScreen(projectName: APP_NAME),

      /*CommonForgotPasswordScreen(
        // titleColor:darkBlueColor,
        titleColor: Theme.of(Get.context!).textTheme.titleLarge?.color,
        titletext: 'FORGOT YOUR PASSWORD?',
        subtitletext: "Please enter your email address below to receive a password reset link.",
        fontFamily: Constants.AppConstants.fontRoboto,
        emailtextfieldText: '',
        hintText: 'Email',
        textfielddecoration: soloLuxuryTextFieldDecoration,
        backtologintext: 'Remember Password? Login Now',
        backtologinbtndecoration: const BoxDecoration(),
        titleheight: 35,
        backtologinbuttonStyle: OutlinedButton.styleFrom(
          side: const BorderSide(
            color: Colors.transparent,
          ),
        ),
        backtologinbuttonTextStyle:
            // AppTextStyle.textStyleUtils600(color: darkBlueColor, size: 16),
            AppTextStyle.textStyleUtils600(color: Theme.of(Get.context!).textTheme.displayMedium?.color, size: 16),
        resetpasswordtext: 'RESET MY PASSWORD',
        resetpasswordbtndecoration: const BoxDecoration(
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.all(Radius.circular(50)),
          color: darkBlueColor,
        ),
        resetpasswordbuttonTextStyle: AppTextStyle.textStyleUtils600(color: whiteColor, size: 16),
        //  AppTextStyle.textStyleUtils600(size: 16),
        // screenBackgroundColor: whiteColor,
        screenBackgroundColor: Theme.of(Get.context!).scaffoldBackgroundColor,
        customBackToLoginButton: const CustomBackToLoginButton(),
      ),*/
      binding: ForgotpasswordBindings(),
    ),
    GetPage(
      name: RoutesConstants.exchangeAndReturnsScreen,
      page: () => const ExchangeAndReturnScreen(),
    ),
  ];
}
