import 'package:app_one/app_constants/app_constants.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../main/main.common.dart';

enum Environment { DEV, STAGING, PROD }

class AppConstants {

  //***************Common Generic Names***************
  // static const String deeplinkBaseUrl = DEEP_LINK_BASE_URL;
  static const String baseUrlStaging = BASE_URL;
  static const String baseUrlProduction = BASE_URL;
  static String apiEndPointLogin = BASE_URL;
  static String apiEstimatedTime = "$BASE_URL/rest/V1/estimate/date-api/";
  static String productImageUrl = "$apiEndPointLogin/media/catalog/product/";
  static String apiCountryGet = "http://ip-api.com";
  static const String licenceId = '11434003';
  static const String fontBrasley = 'Brasley';
  static const String fontOpenSans= 'OpenSans';
  static const String apiEndPointContactUs= '';
}

AndroidDeviceInfo? deviceInfo;
PackageInfo packageInfo = PackageInfo(
  appName: 'Unknown',
  packageName: 'Unknown',
  version: 'Unknown',
  buildNumber: 'Unknown',
  buildSignature: 'Unknown',
);
