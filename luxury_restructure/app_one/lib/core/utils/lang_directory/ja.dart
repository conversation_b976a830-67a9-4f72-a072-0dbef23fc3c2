const Map<String, String> ja = {
  "language": "英語",
  "appNameText": "ソロラグジュアリー",
  "aboutUsText": "私たちに関しては",
  "referFriendText": "友達を紹介する",
  "returnsRefundsText": "返品と返金",
  "faqText": "よくある質問",
  "contactText": "連絡先",
  "socialText": "社交",
  "companyText": "会社",
  "contactUs": "お問い合わせ",
  "myOrders": "私の注文",
  "myOrderImage": "画像",
  "productName": "商品名",
  "sku": "SKU",
  "price": "価格",
  "quantity": "数量",
  "status": "スターテス",
  "action": "アクション",
  "paymentMethod": " 支払方法",
  "shippingAddress": "お届け先の住所",
  "billingAddress": "請求先住所",
  "continueShopping": "ショッピングを続ける",
  "freuentlyAskedQueText": "よくある質問",
  "shopAndPlaceOrder": "買い物と注文の方法は？",
  "accountToPlaceOrderQues": "注文するにはアカウントが必要ですか？",
  "accountToPlaceOrderAns":
      "いいえ、必要なのはメールアドレスだけです。アカウントに登録してウィッシュリストにピースを追加することをお勧めしますが、ゲストとして注文して追跡し、自分に合った時間にサインアップすることもできます。",
  "registeredCustomersText": "登録済みの客",
  "registeredCustomersDescriptionText": "アカウントをお持ちの場合は、\nメールアドレスでログインしてください。",
  "usernameText": "ユーザー名",
  "passwordText": "パスワード",
  "signInText": "サインイン",
  "forgotYourPasswordText": "パスワードをお忘れですか？",
  "newCustomersText": "新しいお客様",
  "loginDescriptionText":
      "アカウントの作成には多くの利点があります。\nチェックアウトを高速化し、複数の住所を保持し、\n注文を追跡します。",
  "createAccountText": "アカウントを作成する",
  "firstNameText": "ファーストネーム",
  "lastNameText": "苗字",
  "dateOfBirthText": "生年月日",
  "pleaseSelectValidDateText": "有効な日付を選択してください",
  "marriageAnniversaryText": "結婚記念日",
  "emailText": "Eメール",
  "confirmPasswordText": "パスワードを認証する",
  "signUpForNewsletterText": "ニュースレターにサインアップ",
  "forgotYourPasswordDescriptionText":
      "パスワードリセットリンクを受信するには、以下にメールアドレスを入力してください\n",
  "enterYourEmailText": "メールアドレスを入力",
  "resetMyPasswordText": "私のパスワードをリセットする",
  "accessYourAccountDetailsText": "アカウントの詳細と特典にアクセスする",
  "signUpText": "サインアップ",
  "myOrdersText": "私の注文",
  "myWishlistText": "私のウィッシュリスト",
  "addressBookText": "住所録",
  "accountInformationText": "口座情報",
  "myTicketsText": "私のチケット",
  "trackOrderText": "注文を追跡する",
  "companyMyAccountText": "会社",
  "contactUsText": "お問い合わせ",
  "secureShoppingText": "安全なショッピング",
  "advancedSearchText": "高度な検索",
  "testimonialsText": "お客様の声",
  "socialMyAccountText": "社交",
  "hopeText": "望む",
  "affiliateProgramText": "アフィリエイトプログラム",
  "influencerRegistrationText": "インフルエンサー登録",
  "exchangeText": "交換と返品",
  "contactMyAccountText": "連絡先",
  "privacyPolicyText": "プライバシーポリシー",
  "termsConditionsText": "利用規約",
  "shippingText": "運送",
  "aboutText": "だいたい",
  "aboutUsMyAccountText": "私たちに関しては",
  "referFriendMyAccountText": "友達を紹介する",
  "returnsText": "返品と返金",
  "faqMyAccountText": "よくある質問",
  "myAccountText": "マイアカウント",
  "filtersText": "フィルタ",
  "priceText": "価格",
  "colorText": "色",
  "brandText": "ブランド",
  "sizeText": "サイズ",
  "applyText": "申し込み",
  "searchText": "検索..",
  "aboutUsContain1":
      "SOLOは、世界中に1か月あたり500万人の訪問者がいるトップオンラインラグジュアリーファッションデスティネーションです。",
  "aboutUsContain2":
      'このウェブサイトには、一流のデザイナーと若くて新しい才能の最高のセレクションが掲載されています。 Soloは、1930年代初頭にフィレンツェで設立され、Via Romaに同社の名を冠したコンセプトストアをオープンしました。1999年以来、同社は総収益の90％をオンライン販売に割り当てています。現在でも、創業者の孫であるAndrea PanconesiがCEOを務め、娘のAnnagreta Panconesiがクリエイティブディレクターを務める、100％個人経営の家族経営の会社です。',
  "aboutUsContain3":
      'このサイトでは、バレンシアガ、ベテメンツ、オフホワイト、J.W。などの最も有名なデザイナーの男性、女性、子供向けの製品を取り揃え、衣料品、靴、バッグ、アクセサリー、高級住宅のインテリア、美容を幅広く取り揃えています。アンダーソン、バルマン、ドルチェ＆ガッバーナ、グッチ、サンローラン、バレンチノ、ジバンシィ、ロエベ、ジャックムスなど。',
  "aboutUsContain4":
      '現在、15か国から200人が、Soloの制作と保守に取り組んでいます。このサイトは、英語、イタリア語、ドイツ語、中国語、ロシア語、スペイン語、フランス語、韓国語、日本語の9か国語で利用できます。このサイトは完全に社内で作成されており、フィレンツェのオフィスにはグラフィックデザイン、IT、カスタマーサービス、マーケティング、購入の各部門があります。スタイリストとプレスチームは、世界のファッションの中心地であるミラノのソロオフィスで活動しています。',
  "aboutUsContain5":
      'すべての注文は、Solo本社から、シグネチャーシューズバッグからパーソナライズされたリボンまで、細心の注意を払って準備され、最高の素材を使用したボックスで世界中に発送されます。',
  "aboutUsContain6":
      'Soloは2008年に150万人のユニークビジターがサイトにアクセスし、2017年には5300万人のユニークビジターに成長しました。オンライン売上の15％以上が米国からのもので、ドイツ、英国、イタリア、中国、フランスがそれに続きます。',
  "aboutUsContain7":
      'Soloは、Buy It First、Luxury Club、Sneakers Club、LVR Privilege、LVR Editionsなど、一連の独占的なサービスと商標イニシアチブにより、オンラインとオフラインの両方で高級小売業界の限界を押し広げています。',
  "profile": "プロフィール",
  "ContactNoText": "連絡先番号。",
  "websiteUrlText": "ウェブサイトのURL",
  "countryText": "国",
  "postCodeText": "郵便番号",
  "socialLinksText": "「ソーシャルリンク",
  "facebookText": "フェイスブック",
  "instagramText": "インスタグラム",
  "twitterText": "ツイッター",
  "youtubeText": "「Youtube",
  "linkendinText": "「リンケンディン",
  "pinterestText": "「Pinterest",
  "followersText": "「フォロワー",
  "workedOnText": "「プロジェクトは",
  "mr": "氏。",
  "yourEmail1Text": "あなたのEメール",
  "subscribeText": "購読",
  "menuText": "メニュー",
  "accountText": "アカウント",
  "checkOutText": "チェックアウト",
  "purchaseText": "購入を完了するには、以下に詳細を入力してください",
  "shippingAddressText": "お届け先の住所",
  "shippingMethodText": "配送方法",
  "paymentMethodText": "支払方法",
  "orderSummaryText": "注文の概要",
  "alreadyAcText": "あなたはすでに私たちのアカウントを持っています。サインインするか、ゲストとして続行します。",
  "loginText": "ログインする",
  "forgotText": "パスワードをお忘れですか？",
  "stLineText": "番地：1号線",
  "cityText": "市",
  "japanText": "日本",
  "stateText": "州/県",
  "zipPostalText": "郵便番号/郵便番号",
  "phoneNumberText": "電話番号",
  "itemInCartText": "カート内のアイテム",
  "qtyText": "数量",
  "viewDetailsText": "詳細を見る",
  "applyDisCodeText": "割引コードを適用する",
  "enterDisCodeText": "割引コードを入力してください",
  "applyDiscountText": "割引を適用する",
  "orderTotalText": "注文合計",
  "placeOrderText": "注文する",
  "storePaymentText": "保管された支払い方法",
  "newsLetterText": "ニュースレターの購読",
  "enterEmailAddress": "メールアドレスを入力してください",
  "enterPhoneNumber": "電話番号を入力します",
  "enterValidEmailAddress": "有効なメールアドレスを入力してください",
  "phoneNumberIsRequired": "電話番号が必要です",
  "phoneNumberIsNotValid": "電話番号は無効です",
  "emailIsRequired": "メールが必要です",
  "invalidEmail": "無効なメール",
  "passwordIsRequired": "パスワードが必要です",
  "passwordRegExp":
      "パスワードの長さは少なくとも8文字で、大文字と小文字の両方の文字、少なくとも1つの数字と1つの特殊文字（！ @＃？）の両方の混合物が含まれている必要があります。",
  "email": "Eメール",
  "confirmPasswordIsRequired": "パスワードの確認が必要です",
  "mrs": "さん。",
  "tearmsAndCondition": "条件",
  "nylonMessengerBackpackMsg":
      "Nylon Messenger Backpack by Balenziaga、フック付きフロントクロージャー、トップハンドル、調整可能なショルダーストラップ、コントラストロゴ付きのフロントパッチジップポケット、装飾的な要素としてのフック。",
  "ourServices": "当社のサービス",
  "ourWebsite": "当社のウェブサイト",
  "productDetails": "製品概要",
  "composition": "組成",
  "sortBy": "並び替え",
  "enterFirstName": "名を入力します",
  "enterLastName": "姓を入力します",
  "enterStreetAddress1": "Astrest Address 1を入力します",
  "enterStreetAddress2": "Astrest Address 2を入力します",
  "enterStreetAddress3": "Astrest Address 3を入力してください",
  "streetAddress3": "路上住所3",
  "enterCityName": "都市名を入力してください",
  "enterZipPostalCode": "zip郵便コードを入力します",
  "enterStateProvince": "州/州に入ります",
  "emailAddressRequired": "メールアドレスが必要です",
  "viewCouponList": "クーポンリストを表示します",
  "changeLanguageAndCurrency": "言語と通貨を変更します",
  "friendFirstName": "友達の名",
  "friendEmailAddress": "友達のメールアドレス",
  "friendPhoneNumber": "友達の電話番号",
  "register": "登録",
  "surnameIsRequired": "姓が必要です",
  "enterCouponCode": "クーポンコードを入力してください",
  "addProductToYourWishList": "ウィッシュリストに製品を追加します",
  "brandNameIsRequired": "ブランド名が必要です",
  "styleIsRequired": "スタイルが必要です",
  "keywordIsRequired": "キーワードが必要です",
  "remarkIsRequired": "発言が必要です",
  "enteryOurOrderNumberHere": "ここから注文番号を入力してください",
  "enterCity": "都市に入ります",
  "Alert": "アラート",
  "requiredVal": "必要とされている",
  "entervalid": "有効な入力を入力します",
  "mustbemorethan2characters": "2文字以上である必要があります",
  "confirmpasswordshouldbematchwithpassword": "パスワードをパスワードと一致させることを確認してください",
  "lastNameIsRequired": "姓が必要です",
  "imageUrlIsRequired": "画像リンクが必要です",
  "enterValidText": "有効なテキストを入力します",
  "searchForPiesSushiTacos": "パイ、寿司、タコスを検索...",
  "enterStreetAddress": "Astressアドレスを入力してください",
  "enterAddress1": "アドレス1を入力します",
  "enterAddress2": "アドレス2を入力します",
  "cOUPONAPPLIEDSUCCESSFULLYYOUSAVED": "クーポンは正常に適用されました。あなたは保存した",
  "oNTHISORDER": "この注文について",
  "areYouSureYouWantToRemove": "本当に捨ててもいいのかい",
  "ifyouwouldlikeUsSendYouTheCollectionFrom": "あなたが私たちにあなたにコレクションを送ってほしいなら",
  "plsCreateATicketBelow": "pls。以下にチケットを作成します",
  "orderNumberIsRequired": "注文番号が必要です",
  "addressDetails": "住所の詳細",
  "enterZipProvince": "zip/州を入力してください",
  "zipProvince": "zip/州",
  "emailAddress": "電子メールアドレス",
  "passwordsdonotmatch": "パスワードが一致していません",
  "passwordsYouHaveEnteredDoNotMatch": "入力したパスワードは一致しません",
  "invalidPassword": "無効なパスワード",
  "registerWithGoogle": "Googleに登録してください",
  "registerWithFacebook": "Facebookに登録してください",
  "registerwithTwitter": "Twitterに登録します",
  "imageurl": "画像URL",
  "findCountry": "国を探す...",
  "orderReference": "参照を注文します",
  "contactNumberIsRequired": "連絡先番号が必要です",
  "headOffice": "本社",
  "soloLuxuryOLablesLtd": "Solo Luxury、O Labels Ltd、",
  "WenlockRoad": "20-22 Wenlock Road、ロンドン、N1 7GU",
  "youCanViewResponsesOnYourEmail": "メールで回答を表示できます",
  "underMyTickets": "私のチケットの下。",
  "friendFirstNameRequired": "友達の名が必要です",
  "friendEmailAddressIsRequired": "友達のメールアドレスが必要です",
  "friendPhoneNumberIsRequired": "友達の電話番号が必要です",
  "selectProductAnOption": "[製品]を選択します",
  "pleaseEnterYourEmailToTrackYourTicketByEmail":
      "電子メールを入力して、電子メールでチケットを追跡してください。",
  "Pleaseagreefornewsletter": "ニュースレターに同意してください",
  "referAFriend": "友達を紹介します",
  "shipTo": "送り先",
  "brandProductQuery": "ブランド製品のクエリ",
  "categoryProductQuery": "カテゴリ製品クエリ",
  "cartDetails": "カートの詳細",
  "date": "日にち",
  "ConfirmationEmailRequired": "確認メールが必要です",
  "pleaseentermessage": "メッセージを入力してください",
  "typeyourmessagehere": "ここにメッセージを入力してください...",
  "goterrorpleasetryagain": "エラーが発生しました。もう一度やり直してください。",
  "internalservererror": "内部サーバーエラー",
  "oopssomethingwentwrong": "おっと！何かが間違っていた",
  "SigninwithGoogle": "Googleにサインインします",
  "SignnwithApple": "Appleでサインインします",
  "SigninwithTwitter": "Twitterでログイン",
  "SigninwithFacebook": "Facebookにサインインします",
  "Requestcancelled": "キャンセルされたリクエスト",
  "Requesttimeout": "リクエストタイムアウト",
  "Get": "得る",
  "peroff": "10％オフ",
  "onyourfirstpurchaseonAPPUSECode": " アプリでの最初の購入時。コードを使用してください：",
  "SOLOAPP10": "SolOApp10",
  "TnCApply": "  *T＆C適用",
};
