const Map<String, String> ru = {
  "language": "английский",
  "appNameText": "соло роскошь",
  "aboutUsText": "О нас",
  "referFriendText": "Пригласить друга",
  "returnsRefundsText": "Возвраты и возмещения",
  "faqText": "Вопросы-Ответы",
  "contactText": "Контакт",
  "socialText": "Социальное",
  "companyText": "Компания",
  "contactUs": "Связаться с нами",
  "myOrders": "мои заказы",
  "myOrderImage": "Изображение",
  "productName": "наименование товара",
  "sku": "Артикул",
  "price": "Цена",
  "quantity": "Кол-во",
  "status": "Положение дел",
  "action": "Действие",
  "paymentMethod": " Способ оплаты",
  "shippingAddress": "адреса доставки",
  "billingAddress": "Адрес для выставления счета",
  "continueShopping": "Продолжить покупки",
  "freuentlyAskedQueText": "Часто задаваемые вопросы",
  "shopAndPlaceOrder": "Как купить и сделать заказ?",
  "accountToPlaceOrderQues":
      "Нужна ли мне учетная запись для размещения заказа?",
  "accountToPlaceOrderAns":
      "Нет, все, что вам нужно, это адрес электронной почты. Мы рекомендуем вам зарегистрировать учетную запись, чтобы начать добавлять товары в свой список желаний, но вы также можете размещать и отслеживать заказы в качестве гостя и регистрироваться в удобное для вас время.",
  "registeredCustomersText": "Зарегистрированные клиенты",
  "registeredCustomersDescriptionText":
      "Если у вас есть учетная запись, войдите в систему, используя свой\nадрес электронной почты.",
  "usernameText": "Имя пользователя",
  "passwordText": "Пароль",
  "signInText": "Войти",
  "forgotYourPasswordText": "Забыли Ваш пароль?",
  "newCustomersText": "новые клиенты",
  "loginDescriptionText":
      "Создание учетной записи имеет много преимуществ:\nоформляйте покупки быстрее, сохраняйте несколько адресов,\nотслеживайте заказы и многое другое.",
  "createAccountText": "Зарегистрироваться",
  "firstNameText": "Имя",
  "lastNameText": "Фамилия",
  "dateOfBirthText": "Дата рождения",
  "pleaseSelectValidDateText": "Пожалуйста, выберите действительную дату",
  "marriageAnniversaryText": "Годовщина свадьбы",
  "emailText": "Эл. адрес",
  "confirmPasswordText": "Подтвердить Пароль",
  "signUpForNewsletterText": "Подписаться на рассылку",
  "forgotYourPasswordDescriptionText":
      "Пожалуйста, введите свой адрес электронной почты ниже, чтобы получить\nссылку для сброса пароля",
  "enterYourEmailText": "Введите адрес электронной почты",
  "resetMyPasswordText": "Сбросить пароль",
  "accessYourAccountDetailsText":
      "Доступ к сведениям об учетной записи и вознаграждениям",
  "signUpText": "ЗАРЕГИСТРИРОВАТЬСЯ",
  "myOrdersText": "мои заказы",
  "myWishlistText": "Мой список пожеланий",
  "addressBookText": "Адресная книга",
  "accountInformationText": "Информация об аккаунте",
  "myTicketsText": "Мои билеты",
  "trackOrderText": "Отследить заказ",
  "companyMyAccountText": "Компания",
  "contactUsText": "Связаться с нами",
  "secureShoppingText": "Безопасные покупки",
  "advancedSearchText": "Расширенный поиск",
  "testimonialsText": "Отзывы",
  "socialMyAccountText": "Социальное",
  "hopeText": "Надеяться",
  "affiliateProgramText": "Партнерская программа",
  "influencerRegistrationText": "Регистрация влиятельного лица",
  "exchangeText": "Обмен и возврат",
  "contactMyAccountText": "Контакты",
  "privacyPolicyText": "Политика конфиденциальности",
  "termsConditionsText": "Условия",
  "shippingText": "Перевозки",
  "aboutText": "О",
  "aboutUsMyAccountText": "О нас",
  "referFriendMyAccountText": "Пригласить друга",
  "returnsText": "Возвраты и возмещения",
  "faqMyAccountText": "Вопросы-Ответы",
  "myAccountText": "Мой счет",
  "filtersText": "Фильтры",
  "priceText": "Цена",
  "colorText": "Цвет",
  "brandText": "Бренд",
  "sizeText": "Размер",
  "applyText": "Применять",
  "searchText": "Поиск..",
  "aboutUsContain1":
      "SOLO — ЛУЧШИЙ ОНЛАЙН-НАПРАВЛЕНИЕ LUXURYFASHION С 5 МИЛЛИОНАМИ ПОСЕТИТЕЛЕЙ В МЕСЯЦ. ДОСТАВКА ПО ВСЕМУ МИРУ.",
  "aboutUsContain2":
      'На веб-сайте представлен лучший выбор самых известных дизайнеров, а также молодых талантов. Компания Solo была основана во Флоренции в начале 1930-х годов, когда был открыт одноименный концептуальный магазин компании на Виа Рома. С 1999 года в сети Интернет компания приписывает 90% общего дохода онлайн-продажам. По сей день это остается на 100% частным семейным бизнесом, в котором внук основателя Андреа Панконези выступает в качестве генерального директора, а его дочь Аннагрета Панконези — в качестве креативного директора.',
  "aboutUsContain3":
      'Сайт предлагает широкий выбор одежды, обуви, сумок, аксессуаров, роскошных предметов интерьера и косметики для мужчин, женщин и детей от самых престижных дизайнеров, таких как Balenciaga, Vetements, Off-White, J.W. Anderson, Balmain, Dolce & Gabbana, Gucci, Saint Laurent, Valentino, Givenchy, Loewe, Jacquemus и другие.',
  "aboutUsContain4":
      'В настоящее время над созданием и поддержкой Solo работают 200 человек из 15 разных стран. Сайт доступен на 9 языках: английском, итальянском, немецком, китайском, русском, испанском, французском, корейском и японском. Сайт создан полностью собственными силами, в офисе во Флоренции находятся отделы графического дизайна, информационных технологий, обслуживания клиентов, маркетинга и закупок. Стилисты и пресс-службы работают из офиса Solo в столице мира моды – Милане.',
  "aboutUsContain5":
      'Все заказы отправляются по всему миру из штаб-квартиры Solo в коробках, которые готовятся с максимальной тщательностью и с использованием лучших материалов, от фирменных сумок для обуви до персонализированной ленты.',
  "aboutUsContain6":
      'В 2008 году на сайт Solo заходили 1,5 миллиона уникальных посетителей, а в 2017 году число уникальных посетителей выросло до 53 миллионов. Более 15% онлайн-продаж приходится на США, за которыми следуют Германия, Великобритания, Италия, Китай и Франция.',
  "aboutUsContain7":
      'Solo раздвигает границы розничной торговли предметами роскоши как в онлайне, так и в офлайне, предлагая множество эксклюзивных услуг и торговых марок, включая Buy It First, Luxury Club, Sneakers Club, LVR Privilege и LVR Editions.',
  "profile": "Профиль",
  "ContactNoText": "Контактный номер",
  "websiteUrlText": "Ссылка на сайт",
  "countryText": "Страна",
  "postCodeText": "Почтовый индекс",
  "socialLinksText": "Социальные ссылки",
  "facebookText": "Фейсбук",
  "instagramText": "Инстаграм",
  "twitterText": "Твиттер",
  "youtubeText": "YouTube",
  "linkendinText": "Линкендин",
  "pinterestText": "Пинтерест",
  "followersText": "Последователи",
  "workedOnText": "Проекты, над которыми работали",
  "mr": "Г-н.",
  "yourEmail1Text": "Ваш адрес электронной почты",
  "subscribeText": "ПОДПИСЫВАТЬСЯ",
  "menuText": "Меню",
  "accountText": "Счет",
  "checkOutText": "Проверить",
  "purchaseText":
      "Пожалуйста, введите свои данные ниже, чтобы завершить покупку",
  "shippingAddressText": "адреса доставки",
  "shippingMethodText": "способ доставки",
  "paymentMethodText": "Способ оплаты",
  "orderSummaryText": "итог заказа",
  "alreadyAcText":
      "У вас уже есть аккаунт у нас. Войдите или продолжите как гость.",
  "loginText": "АВТОРИЗОВАТЬСЯ",
  "forgotText": "Забыли Ваш пароль?",
  "stLineText": "Адрес (строка 1",
  "cityText": "Город",
  "japanText": "Япония",
  "stateText": "Штат/провинция",
  "zipPostalText": "Почтовый индекс",
  "phoneNumberText": "Телефонный номер",
  "itemInCartText": "Товар в корзине",
  "qtyText": "Кол-во",
  "viewDetailsText": "Посмотреть детали",
  "applyDisCodeText": "Применить код скидки",
  "enterDisCodeText": "Введите код скидки",
  "applyDiscountText": "Применить скидку",
  "orderTotalText": "Весь заказ",
  "placeOrderText": "РАЗМЕСТИТЬ ЗАКАЗ",
  "storePaymentText": "СОХРАНЕННЫЕ СПОСОБЫ ОПЛАТЫ",
  "newsLetterText": "ПОДПИСКА НА РАССЫЛКУ",
  "enterEmailAddress": "Введите адрес электронной почты",
  "enterPhoneNumber": "Введите номер телефона",
  "enterValidEmailAddress": "Введите действительный адрес электронной почты",
  "phoneNumberIsRequired": "Требуется номер телефона",
  "phoneNumberIsNotValid": "Номер телефона не действителен",
  "emailIsRequired": "Электронная почта требуется",
  "invalidEmail": "Неверный адрес электронной почты",
  "passwordIsRequired": "Требуется пароль",
  "passwordRegExp":
      "Пароль должен быть не менее 8 символов в длину и содержать смесь как прописных, так и строчных букв, по крайней мере одного числа и одного специального символа (например,! @ #?).",
  "email": "Эл. адрес",
  "confirmPasswordIsRequired": "ПодтверждениеПароль обязательный",
  "mrs": "Госпожа.",
  "tearmsAndCondition": "УСЛОВИЯ И ПОЛОЖЕНИЯ",
  "nylonMessengerBackpackMsg":
      "Рюкзак Nylon Messenger от Balenziaga, переднее закрытие с крючком, верхняя ручка, регулируемые плечевые ремни, передний патч -карман с логотипом контрастного, крючки в качестве декоративных элементов.",
  "ourServices": "Наши услуги",
  "ourWebsite": "Наш веб-сайт",
  "productDetails": "Сведения о продукте",
  "composition": "Состав",
  "sortBy": "Сортировать по",
  "enterFirstName": "Введите имя",
  "enterLastName": "Введите фамилию",
  "enterStreetAddress1": "Введите адрес улицы 1",
  "enterStreetAddress2": "Введите адрес улицы 2",
  "enterStreetAddress3": "Введите адрес улицы 3",
  "streetAddress3": "Уличный адрес 3",
  "enterCityName": "Введите название города",
  "enterZipPostalCode": "Введите почтовый код ZIP",
  "enterStateProvince": "Войти в государство/провинцию",
  "emailAddressRequired": "Требуется адрес электронной почты",
  "viewCouponList": "Просмотреть список купонов",
  "changeLanguageAndCurrency": "Изменение языка и валюты",
  "friendFirstName": "Друг Имя",
  "friendEmailAddress": "Друг адрес электронной почты",
  "friendPhoneNumber": "Друг номер телефона",
  "register": "регистр",
  "surnameIsRequired": "Фамилия требуется",
  "enterCouponCode": "Введите код купона",
  "addProductToYourWishList": "Добавьте продукт в свой список желаний",
  "brandNameIsRequired": "Требуется торговая марка",
  "styleIsRequired": "Стиль требуется",
  "keywordIsRequired": "Требуется ключевое слово",
  "remarkIsRequired": "Замечание требуется",
  "enteryOurOrderNumberHere": "Введите номер свой заказ здесь",
  "enterCity": "Войти в город",
  "Alert": "Тревога",
  "requiredVal": "требуется для",
  "entervalid": "Введите действительный",
  "mustbemorethan2characters": "должно быть более 2 символов",
  "confirmpasswordshouldbematchwithpassword":
      "Подтвердите пароль должен соответствовать паролю",
  "lastNameIsRequired": "Фамилия требуется",
  "imageUrlIsRequired": "Ссылка на изображение требуется",
  "enterValidText": "Введите допустимый текст",
  "searchForPiesSushiTacos": "Ищите пироги, суши, тако ...",
  "enterStreetAddress": "Введите адрес улицы",
  "enterAddress1": "Введите адрес 1",
  "enterAddress2": "Введите адрес 2",
  "cOUPONAPPLIEDSUCCESSFULLYYOUSAVED": "Купон применяется успешно. Вы спасли",
  "oNTHISORDER": "В этом порядке",
  "areYouSureYouWantToRemove": "Вы уверены, что хотите удалить",
  "ifyouwouldlikeUsSendYouTheCollectionFrom":
      "Если вы хотите, чтобы мы отправили вам коллекцию из",
  "plsCreateATicketBelow": "плс. Создайте билет ниже",
  "orderNumberIsRequired": "Требуется номер заказа",
  "addressDetails": "подробности адреса",
  "enterZipProvince": "Введите Zip/Province",
  "zipProvince": "Zip/Province",
  "emailAddress": "Адрес электронной почты",
  "passwordsdonotmatch": "Пароли не совпадают",
  "passwordsYouHaveEnteredDoNotMatch": "Пароли, которые вы ввели, не совпадают",
  "invalidPassword": "Неверный пароль",
  "registerWithGoogle": "Зарегистрируйтесь в Google",
  "registerWithFacebook": "Зарегистрируйтесь в Facebook",
  "registerwithTwitter": "Зарегистрируйтесь в Twitter",
  "imageurl": "URL изображения",
  "findCountry": "Найдите страну ...",
  "orderReference": "Код заказа",
  "contactNumberIsRequired": "Требуется контактный номер",
  "headOffice": "Головной офис",
  "soloLuxuryOLablesLtd": "Solo Luxury, o Labels Ltd,",
  "WenlockRoad": "20-22 Wenlock Road, Лондон, N1 7GU",
  "youCanViewResponsesOnYourEmail":
      "Вы можете просмотреть ответы на своем электронном письме",
  "underMyTickets": "Под моими билетами.",
  "friendFirstNameRequired": "Требуется имя друга имени",
  "friendEmailAddressIsRequired": "Требуется адрес электронной почты друга",
  "friendPhoneNumberIsRequired": "Требуется номер телефона друга",
  "selectProductAnOption": "Выберите продукт опцию",
  "pleaseEnterYourEmailToTrackYourTicketByEmail":
      "Пожалуйста, введите свой адрес электронной почты, чтобы отслеживать свой билет по электронной почте.",
  "Pleaseagreefornewsletter":
      "Пожалуйста, согласны с информационным бюллетенем",
  "referAFriend": "Приведи друга",
  "shipTo": "Доставить",
  "brandProductQuery": "Запрос продукта бренда",
  "categoryProductQuery": "Категория Продукт запрос",
  "cartDetails": "Детали тележки",
  "date": "Свидание",
  "ConfirmationEmailRequired": "Требуется подтверждение электронное письмо",
  "pleaseentermessage": "Пожалуйста, введите сообщение",
  "typeyourmessagehere": "Пишите ваше сообщение здесь...",
  "goterrorpleasetryagain": "Получил ошибку, попробуйте еще раз.",
  "internalservererror": "Внутренняя ошибка сервера",
  "oopssomethingwentwrong": "Упс! Что-то пошло не так",
  "SigninwithGoogle": "Войдите с Google",
  "SignnwithApple": "Войти в Apple",
  "SigninwithTwitter": "Войдите в Twitter",
  "SigninwithFacebook": "Авторизоваться с помощью Фэйсбука",
  "Requestcancelled": "Запрос отменен",
  "Requesttimeout": "Запросить тайм -аут",
  "Get": "Получить",
  "peroff": "Скидка 10",
  "onyourfirstpurchaseonAPPUSECode":
      " При первой покупке в приложении. Используйте код:",
  "SOLOAPP10": "SoloApp10",
  "TnCApply": "  *T & C.",
};
