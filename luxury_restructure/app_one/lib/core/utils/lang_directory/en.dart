const Map<String, String> en = {
  "language": "English",
  "appNameText": "Solo Luxury",
  "registerwithApple": "Register With Apple",
  "details": "Details",
  "orderConfirm": "Thank you for your purchase!",
  "orderIdTitle": '''Your order # is:''',
  "expectedDate": 'Expected Shipment Date',
  "orderDate": 'Order Date',
  "qty": 'QTY',
  "orderDetails": "Order Details",
  "tax": "TAX",
  "faqTitle": "Frequently Asked Questions",
  "promoCode": "PROMO CODE",
  "enterCode": "Enter promo code",
  "updateCart": "UPDATE CART",
  "cart": "CART",
  "cartContain1": "Support a charity with your donation",
  "cartContain2":
      "15% of the order value will be donated by\nSolo Luxury for donations.",
  "cartContain3": "Please click here to donate now.",
  "cartContain4": "I Want To Donate",
  "cartContain5": "Check Out with Multiple Addresses",
  "charityTitle": "Hope",
  "charityContain1":
      "When you buy from us you help support a dream A dream to give A secure future A happy environment, and A healthy life.",
  "charityContain2":
      "HOPE is our project to give hope and opportunities to those who need it the most. Every dollar you spend on our site - helps improve a life somewhere, from Syrian refugees to educating Children in Africa or providing clean water, we wish and hope to make the world a more equal place.",
  "charityContain3":
      "2% of every sale made on our website goes to help those most in need. You can also choose to contribute any further amount which we will match in addition to our 2% - unto a maximum of 5% of the order value.",
  "charityContain4":
      "What's more? You get updated by us about the donation in every details possible and you know where the money was spent and how it helped changed a life.",
  "description": "Description",
  "estimatedDelivery": "Estimated Delivery",
  "grandTotalToBeCharge": "Grand Total to be Charged:",
  "guaranteedDelivery": "Guaranteed Delivery : Nov 04 - Nov 11",
  "sizeChart": "Size Chart",
  "size": "Size",
  "subTotal": "Sub Total",
  "addTOCart": "Add To Cart",
  "soldOut": "Sold Out",
  "estimatedTotal": "Estimated Total",
  "addToWishlist": "Add To Wishlist",
  "whyVeraLusso": "WHY VERALUSSO",
  "knowMore": "Know More",
  "recommendation": "Recommendation",
  "recentlyViewed": "Recently Viewed",
  "returnsRefundsTitle": "Returns & Refunds",
  "returnPolicyTitle": "RETURNS POLICY",
  "refundTitle": "REFUNDS",
  "returnProcessTitle": "RETURNS PROCESS",
  "customisedTitle": "CUSTOMISED, PRE-OWNED & VINTAGE ITEMS",
  "howToReturnTitle": "HOW TO RETURN",
  "returnPolicyContain1":
      "Returned items must comply with our returns policy:\n",
  "returnPolicyContain2":
      '1. Items must be returned unworn, undamaged and unused, with all tags attached and the original packaging included.\n',
  "returnPolicyContain3":
      '2. Footwear and accessories must be returned with the original branded boxes and dust bags, where provided, and placed inside a protective outer box for shipping.\n',
  "returnPolicyContain4":
      '3. When trying on footwear, please take care not to mark the soles or damage the shoe box.\n',
  "returnPolicyContain5":
      '4. If an item has a Avoirchic security tag or brand tag attached, it must be returned with the tag in its original.\n',
  "returnPolicyContain6":
      '5. Beauty and cosmetic products must be returned unopened and unused, with the seals of any packaging still intact.\n',
  "returnPolicyContain7":
      '6. Hosiery, lingerie and swimwear items must be returned with the hygiene seals attached and in unopened and undamaged product packaging, where applicable.\n',
  "returnPolicyContain8":
      '7. Lingerie and swimwear must only be tried on over your own undergarments. We will not accept any returns that have been worn or are soiled.\n',
  "returnPolicyContain9":
      '8. Face masks must be returned unworn in their original, unopened and undamaged product packaging.\n',
  "returnPolicyContain12":
      "9. Jewellery must be returned in the same condition it arrived in, including all branded packaging and documents provided with it.",
  "returnPolicyContain10":
      "Please take care when trying on your purchases and return them in the same condition you received them. Any returns that do not meet our policy will not be accepted.\n",
  "returnPolicyContain11":
      "If you placed an order as a guest, you can start your return here. To return or cancel an item that has been made to your specifications, please view our ‘ Customised Items’ section below for further information.",
  "forgetPassword": "Forgot Your Password?",
  "backToSignInScreen": "Back to sign in screen",
  "forgetPasswordContain":
      "We have sent you a Password Reset Link\n This Link is valid for 2 hours \n if you did not make this request\n you can ignore this email \n Please check your spam folder\n if you do not receive the email \n For any assistance please",
  "forgotPasswordWrongEmailStart": "There is no Account Associated",
  "with": "with",
  "WriteAt": "Write to us at",
  "forgotPasswordWrongEmailEnd": "you need any further assistance please",
  "accountDoesNotExist": "Account Does Not Exist",
  "pleaseCheckYourInbox": "Please Check Your Inbox",
  "aboutUsText": "About us",
  "referFriendText": "Refer Friend",
  "aboutUsContain1":
      "SOLO IS A TOP ONLINE LUXURYFASHION DESTINATION WITH 5 MILLION VISITORS PER MONTHAND WORLDWIDE SHIPPING.",
  "aboutUsContain2":
      'The website features the best selection of thetop established designers as wellas young emerging talents. Solo was founded in Florence in the early 1930s, with the opening of the company’s eponymous concept store on Via Roma.Online since 1999, the company attributes 90% of total revenue to online sales. To this day it remains a 100% privately owned family business, with the founder’s grandson, Andrea Panconesi acting as CEO and his daughter, Annagreta Panconesi as Creative Director.',
  "aboutUsContain3":
      'The site offers a wide selection of clothing, shoes, bags, accessories, luxuryhome interiors and beauty, with products available for men, women and kids from the most prestigious designers such as Balenciaga, Vetements, Off-White,J.W. Anderson, Balmain, Dolce & Gabbana, Gucci, Saint Laurent, Valentino, Givenchy, Loewe, Jacquemus and more.',
  "aboutUsContain4":
      'There are currently 200 people from 15 different countries that work to produce and maintain Solo. The site is available in 9 languages: English, Italian, German, Chinese, Russian, Spanish, French,Korean and Japanese. The site is created entirely in-house, with the Florence office housing graphic design, IT,customer service, marketing and buying departments. The stylists and press teams operate from the Solo office in the fashion capital of the world – Milan.',
  "aboutUsContain5":
      'All orders are shipped worldwide from the Solo headquarters in boxes that are prepared with maximum care and use the finest materials, from the signature shoe bags to the personalized ribbon.',
  "aboutUsContain6":
      'Solo had 1.5 million unique visitors access the site in 2008 and has grown to 53 million unique visitors in 2017. More than 15% of online sales are generated from the United States, followed by Germany, UK, Italy, China and France.',
  "aboutUsContain7":
      'Solo pushes the boundaries of the luxury retail landscape both on and offline with an array of exclusive services and trademark initiatives including Buy It First, Luxury Club, Sneakers Club, LVR Privilege and LVR Editions.',
  "profile": "Profile",
  "firstNameText": "First Name",
  "lastNameText": "Last Name",
  "emailText": "Email",
  "ContactNoText": "Contact no.",
  "websiteUrlText": "Website URL",
  "cityText": "City",
  "countryText": "Country",
  "postCodeText": "Post Code",
  "socialLinksText": "Social links",
  "facebookText": "Facebook",
  "instagramText": "Instagram",
  "twitterText": "Twitter",
  "youtubeText": "Youtube",
  "linkendinText": "Linkendin",
  "pinterestText": "Pinterest",
  "followersText": "Followers",
  "workedOnText": "Projects worked on",
  "mr": "Mr.",
  "returnsRefundsText": "Returns & Refunds",
  "faqText": "FAQ",
  "contactText": "Contact",
  "socialText": "Social",
  "companyText": "Company",
  "contactUs": "Contact Us",
  "myOrders": "My Orders",
  "myOrderImage": "Image",
  "productName": "Product Name",
  "sku": "SKU",
  "price": "Price",
  "quantity": "Qty",
  "status": "Status",
  "action": "Action",
  "backText": "Back to login",
  "paymentMethod": " Payment Method",
  "shippingAddress": "Shipping Address",
  "billingAddress": "Billing Address",
  "continueShopping": "Continue Shopping",
  "freuentlyAskedQueText": "Frequently Asked Questions",
  "shopAndPlaceOrder": "How to shop & place an order?",
  "accountToPlaceOrderQues": "Do I need an account to place an order?",
  "accountToPlaceOrderAns":
      "No, all you need is an email address. We recommend that you register for an account to start adding pieces to your Wishlist, but, you can also place and track orders as a guest and signup at a time that suits you.",
  "registeredCustomersText": "Registered Customers",
  "registeredCustomersDescriptionText":
      "If You Have An Account, Sign In With Your\nEmail Address.",
  "usernameText": "Username",
  "passwordText": "Password",
  "signInText": "Sign in",
  "forgotYourPasswordText": "Forgot Your Password?",
  "newCustomersText": "New Customers",
  "loginDescriptionText":
      "Creating an account has many benefits: check\nout faster, keep more than one address\ntrack orders and more.",
  "createAccountText": "Create Account",
  "dateOfBirthText": "Date of Birth",
  "pleaseSelectValidDateText": "Please select valid date",
  "marriageAnniversaryText": "Marriage Anniversary",
  "confirmPasswordText": "Confirm Password",
  "signUpForNewsletterText": "Sign Up for Newsletter",
  "forgotYourPasswordDescriptionText":
      "Please Enter Your Email Address \n Below To \n Receive your Reset Link",
  "enterYourEmailText": "Enter your email",
  "resetMyPasswordText": "Reset My Password",
  "accessYourAccountDetailsText": "Access Your Account Details And Rewards",
  "signUpText": "Sign Up",
  "myOrdersText": "My Orders",
  "myWishlistText": "My Wish List",
  "addressBookText": "Address Book",
  "accountInformationText": "Account Information",
  "myTicketsText": "My Tickets",
  "myCouponsText": "My Coupons",
  "trackOrderText": "Track Order",
  "companyMyAccountText": "Company",
  "contactUsText": "Contact Us",
  "secureShoppingText": "Secure Shopping",
  "advancedSearchText": "Advanced Search",
  "testimonialsText": "Testimonials",
  "socialMyAccountText": "Social",
  "hopeText": "Hope",
  "affiliateProgramText": "Affiliate Program",
  "influencerRegistrationText": "Influencer Registration",
  "exchangeText": "Exchange and Returns",
  "contactMyAccountText": "Contacts",
  "privacyPolicyText": "Privacy Policy",
  "termsConditionsText": "Terms & Conditions",
  "shippingText": "Shipping",
  "track_your_order": "Track Your Order",
  "shipHereText": "Ship Here",
  "saveInAddressBookText": "Save In AddressBook",
  "aboutText": "About",
  "aboutUsMyAccountText": "About Us",
  "referFriendMyAccountText": "Refer Friend",
  "returnsText": "Returns & Refunds",
  "faqMyAccountText": "FAQ",
  "myAccountText": "My Account",
  "filtersText": "Filters",
  "priceText": "Price",
  "colorText": "Color",
  "brandText": "Brand",
  "sizeText": "Size",
  "applyText": "Apply",
  "searchText": "Search..",
  "yourEmail1Text": "Your E-Mail",
  "subscribeText": "SUBSCRIBE",
  "menuText": "Menu",
  "accountText": "Account",
  "checkOutText": "Checkout",
  "purchaseText": "Please Enter Your Details Below To Complete Your Purchase",
  "shippingAddressText": "Shipping Address",
  "shippingMethodText": "Shipping Method",
  "paymentMethodText": "Payment Method",
  "orderSummaryText": "Order Summary",
  "alreadyAcText":
      "You Already have an account with us. Sign in or continue as guest.",
  "loginText": "Login",
  "forgotText": "Forgot Your Password?",
  "stLineText": "Street Address: Line 1",
  "japanText": "Japan",
  "stateText": "State/Province",
  "zipPostalText": "Zip/Postal Code",
  "phoneNumberText": "Phone Number",
  "itemInCartText": "Item in cart",
  "qtyText": "Qty",
  "viewDetailsText": "View Details",
  "applyDisCodeText": "Apply Discount Code",
  "enterDisCodeText": "Enter Discount Code",
  "applyDiscountText": "Apply Discount",
  "orderTotalText": "Order Total",
  "placeOrderText": "PLACE ORDER",
  "affiliateProgramTitleText": "SOLO LUXURY AFFILIATE PROGRAM",
  "whoAreWeLookingTitleText": "Who are we looking for?",
  "whoAreWeLookingDescriptionOneText":
      "Do you have a fashion-forward, luxury oriented website, blog, social media profile that is trend savvy with an international outlook? Is your focus fashion, lifestyle, beauty and design? If so, we are looking for you!\n\nJoin our global Affiliate Program and you will have the opportunity to earn a commission every time your visitors purchase from SOLO.",
  "whatCanYouExpectTitleText": "What can you expect?",
  "expectRuleOneText": "Commission on all approved net sales",
  "expectRuleTwoText": "45 days cookie window",
  "expectRuleThreeText": "Data feed updated multiple times per day",
  "expectRuleFourText":
      "Access to customized banners, textlinks, deeplinks and our datafeed in 9 languages (English, Italian, French, German, Korean, Spanish, Chinese,Russian & Japanese)",
  "expectRuleFiveText":
      "The possibility to customize feeds into all world currencies",
  "promoteSoloQuestionText": "How can you promote solo (as an affiliate)?",
  "promoteSoloAnswerOneText":
      "Creating high-quality and engaging content that mirrors the style of SOLO",
  "promoteSoloAnswerTwoText":
      "Featuring our content, banners and links for multiple categories and events on your website and social media channels",
  "promoteSoloAnswerThreeText":
      "Promoting the benefits that our customers love",
  "promoteSoloAnswerFourText":
      "A selection of continuously updated articles from the best brands",
  "promoteSoloAnswerFiveText":
      "Free returns with no added taxes for the US, Canada, the UK and ",
  "howApplyTitleText": "How to apply?",
  "howApplyAnswerOneText":
      "Email us the name and URL of your website, blog or social media profile and your main markets. We’ll review your site and get back to you with further information on how to join the affiliate program.\n\nPlease note that all applications are manually evaluated and processing may take up to 10 days.\n\nIf you feel your application has been wrongfully declined <NAME_EMAIL> and a member of the team will address your concerns.",
  "visitorMonthHintText": "Select Unique Visitors/Month",
  "viewsMonthHintText": "Select Page Views/Month",
  "cityHintText": "City",
  "countryHintText": "Country",
  "addressOneText": "Street Address 1",
  "addressTwoText": "Street Address 2",
  "addressText": "Address",
  "letsGoText": "Let's Go",
  "newsLetters": "Newsletter",
  "manageAddresses": "Manage Addresses",
  "defaultBillingAddress": "Default Billing Address",
  "editAddress": "Edit Address",
  "defaultShippingAddress": "Default Shipping Address",
  "defaultShippingContain":
      "sdsdsd sdsdsd sds ds sdsd kl skldjlsdjklsdjljsl jaipur, Delhi, 302019 India",
  "newsLettersContain": "You Aren't Subscribed To\nOur Newsletter.",
  "edit": "Edit",
  "liveChatText": "Live Chat",
  "startChatText": "Start The Chat",
  "chooseYourCountryText": "Choose your country",
  "billingText": "Default\nBilling",
  "yourAppExpericanceText": "Your app experience is set to",
  "indiaText": "INDIA (IND) - ENGLISH (IND)",
  "englishIsAlsoText":
      "ENGLISH is also available in your\nlocation. Would you like to change?",
  "noThanksText": "No, Thanks",
  "changeText": "CHANGE",
  "myWidhListText": "My Wish List",
  "accountInfoText": "Account Information",
  "newsletterSubText": "Newsletter Subscriptions",
  "youaddCartText": "You added",
  "youaddCartEndText": "To Your Shopping Cart.",
  "addedYourCart": "Added To Your Cart",
  "viewCartText": "View Cart",
  "continueShoppingText": "Continue Shopping",
  "checkYourOrderText": "Check your order",
  "enterYourOrderText":
      "Enter your order reference and email address below to see your order details. From there you can check its shipping status,cancel items or make a return.",
  "howDoIFindText": "How do i find my",
  "orderRefernceText": "Order reference?",
  "submitText": "Submit",
  "menText": "Men",
  "womenText": "Women",
  "kidsText": "Kids",
  "homeText": "Home",
  "search1Text": "Search",
  "designersText": "Designers",
  "wishListText": "Wish List",
  "storePaymentText": "Stored Payment Methods",
  "storeCreditText": "My Store Credit",
  "newsLetterText": "NEWSLETTER SUBSCRIPTIONS",
  "selectLangCurrText": "Select Language & Currency",
  "selectLangCurrDescText":
      "Please choose from the following Language and currency options available in your country Language",
  "saveText": "Save",
  "cancelText": "Cancel",
  "deliveryText": "Delivery",
  "ordersText": "Order",
  "paymentText": "Payment",
  "productText": "Product",
  "promotionText": "Promotion",
  "returnsContactText": "Returns",
  "technicalText": "Technical",
  "typeOfEnquiryText": "Type Of Enquiry",
  "surNameText": "Surname",
  "confirmEmailText": "Confirm Email",
  "subjectText": "Subject",
  "whatsonyourmindText": "What's On Your Mind?",
  "languageText": "Language",
  "currencyText": "Currency",
  "itemOrder": "Items Ordered",
  "nameChatText": "Name",
  "idText": "ID",
  "actionText": "Action",
  "welcometoChatText": "Welcome to our LiveChat!",
  "fillTheFormText": "Please fill in the form below before starting the chat.",
  "dateOfMarriageText": "Date of Marriage",
  "findBrandsText": "Find Brands...",
  "sizeChartText": "Size Chart",
  "specialSizeRequestsText": "Special Size Requests",
  "notifyMe": "Notify Me When Available",
  "specialSizeEnterEmailText": "Enter Email",
  "specialSizeSelectSizeText": "Select Size",
  "specialSizeSubmitText": "Submit",
  "youaddWishEndText": "to your wishlist.",
  "viewWishListText": "View WishList",
  "logOutText": "Logout",
  "policyHeadingText": "Solo Luxury Privacy Policy",
  "termAndConditionHeadingText": "Terms And Conditions",
  "reasonReturnText": "Reason to return",
  "reasonCancelText": "Reason to cancel",
  "returnItemText": "Return item",
  "cancelItemText": "cancel item",
  "returnRequestText": "Return Request Received",
  "teamSoloLuxuryText": "Team Solo Luxury.",
  "wehaveRecivedText":
      "We have received your request and your request Id is - Please Check the status of your request at @@@@. contact us at care@app_three.com - for any query with regards to request.",
  "newAddressText": "New Address",
  "myBillingShipAddressSameText":
      "My Billing and Shipping Address are the same",
  "textDonationText": "Test Donation Two",
  "chooseYourownText": "Choose your own Amount GBP\n(minimum £1)",
  "theSelectamountText":
      "The selected amount will be added to your\nshopping cart.",
  "iwanttoDonateText": "i want to donate this amount",
  "gbpText": "GBP",
  "e5Text": "£ 5",
  "e10Text": "£ 10",
  "e15Text": "£ 15",
  "e20Text": "£ 20",
  "e25Text": "£ 25",
  "searchCountry": "Search country",
  "trackYourTicketByEmail": "Track Your Ticket By Email",
  "trackYourRequests": "Track Your Requests",
  "thankYou": "Thank You.",
  "notifyMeWhenThisProductIsInStock":
      "We Will Inform You Once The Product Is In Stock. You Can Also View The Status Of Your Request On",
  "myTicket": "My Tickets.",
  "productAddedTowishList": "Product added to wishList",
  "error": "Error",
  "somethingWentWrongPleaseTryAgain": "Something went wrong. Please try again",
  "authorised": "Authorised",
  "thePaymentWasSuccessful": "The payment was successful.",
  "okay": "Okay",
  "cancel": "Cancel",
  "enterValidEmailAddress": "Enter Valid Email Address",
  "enterEmailAddressNotSame": "Enter Email Address Not Same",
  "forgotPasswordRequestSendedOnYourMailPleaseCheck":
      "Forgot password request sended on your mail please check",
  "pleaseSelectReturnReasonItem": "Please Select Return Reason Item",
  "orderCancelRequest": "Order Cancel Request",
  "orderReturnRequest": "Order Return Request",
  "orderReturnRequestSuccessfullySentYouWillGetUpdatesSoon":
      "Order return request successfully sent, you will get updates soon.",
  "urlOfImage": "URL Of Image",
  "enterURLOfImage": "Enter URL Of Image",
  "enterRemarks": "Enter Remarks",
  "createTicket": "Create Ticket",
  "orderSuccessfullyUpdatedYouWillGetYourTicketIDshortly":
      "Order successfully updated and a Request Ticket has been created, You will get your Ticket ID shortly",
  "someThingWentWrongPleaseTryAgainLater":
      "SomeThing went wrong please try again later",
  "weCannotFindAnyOrdersAssociatedWithThisEmail":
      "We Cannot Find Any Orders Associated \nWith This Email.",
  "pleaseTryWithAnotherEmailAddress":
      "Please Try With Another Email Address \nOr Write To Us At care@app_three.com For Further Assistance.",
  "weCannotFindAnyRequestAssociatedWithThisEmail":
      "We Cannot Find Any Request Associated \nWith This Email.",
  "itSeemsWeHaveNothingToShowFor": "It Seems We Have Nothing to Show for",
  "ifYouWouldLikeToHaveMoreInformationAbout":
      "If You Would Like To Have More Information About",
  "thenPleaseCreateTicket": "Then Please Create A Ticket",
  "pleaseCheckYourInternetConnection": "Please check your internet connection!",
  "country": "Country",
  "saveAddress": "Save Address",
  "updateAddress": "Update Address",
  "addNew": "Add New",
  "addAddress": "Add Address",
  "defaultShipping": "Default\nShipping",
  "noAddressFound": "No address found",
  "noDataFound": "No data found",
  "shopWithoutWorry": "Shop without Worry",
  "fillYourCartText":
      "Fill your cart bursting that all our product are genuine and will be delivered at the time promised. We go further to Secure your shopping with full Insurance",
  "weAimToSatisfyText":
      "We aim to satisfy our clients 100% , however Online shopping is fraught with risks specially when it comes to International Shipping .To give you the peace of mind when you shop from us we have partnered PayPal and Checkout.com , to give you complete security and safety on your purchases.",
  "ifYouPayWithPayPal":
      "If you pay with PayPal you are covered automatically covered under PayPal Buyer Protection you are covered for the full purchase price plus the original shipping charges if the product is delayed or arrives not as what was described on our website.",
  "inAdditionAllOtherPaymentsAreProcessed":
      "In addition all other payments are processed thru Checkout.com - the worlds largest and most reputed payment acquiring platform and any disputes can be raised through your bank for immediate resolution.",
  "noBrandFound": "No Brand found",
  "andMore": "& More...",
  "youHaveNoItemsInYourShoppingCart":
      "You have no items in your shopping cart.",
  "couponCode": "Coupon Code",
  "removeCoupon": "Remove \nCoupon",
  "availableCouponList": "Available Coupon List",
  "goToCheckOut": "Go To CheckOut",
  "allCouponsList": "All Coupons List",
  "yes": "Yes",
  "no": "No",
  "success": "Success",
  "warning": "Warning",
  "chooseAnOption": "Choose An Option...",
  "youHaveNoProductsInYourWishList": "You Have No Products In Your Wish List.",
  "pleaseEnterYourEmailToTrackYourTicketByEmail":
      "Please Enter Your Email To Track Your Ticket By Email.",
  "areYouSureWantToRemoveThisProductFromCart":
      "Are you sure want to remove this product from cart?",
  "removeProduct": "Remove Product",
  "onceYouAddThisProductToWishlistText":
      "Once you add this product to wishlist then it will be remove from cart",
  "howDoIFindMyOrderReference": "How Do I Find My Order Reference?",
  "pleaseEnterOrderNumberToTrackYourOrder":
      "Please Enter Order Number To Track Your Order.",
  "guestUsers": "Guest Users",
  "ifYouHaveAnAccountSignInWithYourEmailAddress":
      "If you have an account, sign in with your email address.",
  "remarks": "Remarks",
  "balance": "Balance",
  "amount": "Amount",
  "sort": "Sort",
  "youCanViewYourRequestsAtMyTickets":
      "You Can View Your Requests at My Tickets",
  "yourRequestHasBeenReceived": "Your Request Has Been Received.",
  "noProductsFound": "No Products Found",
  "itSeemsWeHaveNothingToShowForThisCategory":
      "It Seems Like We Have Nothing To Show For This Category",
  "filter": "Filter",
  "newestFirst": "Newest First",
  "itSeemsLikeWeHaveNothingToShowForThisBrand":
      "It Seems Like We Have Nothing To Show For This Brand",
  "buyNow": "Buy now",
  "selectSizeFirst": "Select Size First",
  "countryWhereYouWantItShipped": "Country where you want it shipped",
  "priceMatchTicket": "Price Match Ticket",
  "ok": "OK",
  "cancelCoupon": "Cancel Coupon",
  "whateverYourQueryText":
      "Whatever Your Query. Use The Contact Form\nBelow To Get In Touch-OurTeam\nIs Ready To Help 24*7",
  "weWillGetInTouchSoon": "We’ll Get in Touch Soon.",
  "weHaveReceivedYourRequest": "We have received your Request",
  "and": "&",
  "noLanguageCurrencyFound": "No Language/Currency Found",
  "firstNameIsRequired": "First Name is required",
  "thankYouForYourInterestText":
      "Thank you for your Interest in working with Solo Luxury",
  "oneOfOurTeamMembersWillBeInTouchSoon":
      "One of our team members will be in touch soon",
  "forAnyAdditionalQueriesPleaseWriteTo":
      "For any additional queries please write to ",
  "incorrectPassword": "Incorrect Password",
  "thePasswordYouEnteredIsIncorrectText":
      "The password you entered is incorrect.\nPlease try again",
  "pleaseEnterYourEmailAddressText":
      "Please enter your email address below to receive a password re copy",
  "backToLogin": "Back to login",
  "youHaveBeenSuccessfullyLoggedOut": "You Have Been Successfully Logged Out",
  "contactInformation": "Contact Information",
  "changePassword": "Change Password",
  "areYouSureToLogOut": "Are You Sure To Log Out",
  "nameIsRequired": "Name Is Required",
  "emailIsRequired": "Email is Required",
  "couponId": "Coupon Id",
  "couponName": "Coupon Name",
  "expireAt": "Expire At",
  "noCouponsFound": "No Coupons Found!",
  "youHaveNoOrders": "You Have No Orders",
  "toBeUpdated": "To be updated",
  "youHaveNoTickets": "You have no Tickets",
  "createNewTicket": "Create New Ticket",
  "message": "Message",
  "communicationPreference": "Communication Preference",
  "subscribeToBeTheFirstText":
      "Subscribe to be the first with our new arrivals,\nexclusive collections, offers and more.",
  "subscribeToNewsletters": "Subscribe to newsletters..",
  "priceMatch": "Price Match",
  "bestPricePromise": "Best Price Promise",
  "yourBestPriceAlways": "Your Best Price - Always.",
  "ifYourPreferredItemIsLowerPricedText":
      "If your preferred item is lower priced at any other store at the current time we will match the price and offer you 10% additional store credit for your next purchase.",
  "simplyLetUsKnowByClickingHere": "Simply let us know by clicking here.",
  "alreadyMadeYourPurchase": "Already Made your purchase.",
  "dontWorryLetUsKnowByClickingText":
      "Dont worry let us know by clicking here and we our customer support team will refund you the difference if the purchase was made within the last 30 days.",
  "pleaseSeeFAQsByClickingHere": "Please see FAQs by clicking here",
  "attention": "Attention",
  "requiredFields": "All fields are required",
  "referFriendSendCouponCode":
      "Enter Details Below To Send A 10% Coupon Code To Your Friend",
  "enterEmailAddress": "Enter Email Address",
  "enterPhoneNumber": "Enter Phone Number",
  "phoneNumberIsRequired": "Phone number is Required",
  "phoneNumberIsNotValid": "Phone number is not valid",
  "invalidEmail": "Invalid Email",
  "passwordIsRequired": "Password is Required",
  "passwordRegExp":
      "The password must be at least 8 characters long and contain a mixture of both uppercase and lowercase letters, at least one number and one special character (e.g.,! @ # ?).",
  "email": "Email",
  "confirmPasswordIsRequired": "Confirm Password Is Required",
  "mrs": "Mrs.",
  "tearmsAndCondition": "TERMS AND CONDITION",
  "nylonMessengerBackpackMsg":
      "Nylon Messenger Backpack By Balenziaga, Front Closure With Hook, Top Handle, Adjustable Shoulder Straps, Front Patch Zip Pocket With Contrast Logo, Hooks As Decorative Elements.",
  "ourServices": "Our Services",
  "ourWebsite": "Our Website",
  "productDetails": "Product Details",
  "composition": "Composition",
  "sortBy": "Sort by",
  "enterFirstName": "Enter First Name",
  "enterLastName": "Enter Last Name",
  "enterStreetAddress1": "Enter Street Address 1",
  "enterStreetAddress2": "Enter Street Address 2",
  "enterStreetAddress3": "Enter Street Address 3",
  "streetAddress3": "Street Address 3",
  "enterCityName": "Enter City Name",
  "enterZipPostalCode": "Enter Zip Postal Code",
  "enterStateProvince": "Enter State/Province",
  "emailAddressRequired": "Email Address required",
  "viewCouponList": "View Coupon List",
  "changeLanguageAndCurrency": "Change language and Currency",
  "friendFirstName": "Friend First Name",
  "friendEmailAddress": "Friend Email Address",
  "friendPhoneNumber": "Friend Phone Number",
  "register": "Register",
  "surnameIsRequired": "Surname Is required",
  "enterCouponCode": "Enter Coupon Code",
  "addProductToYourWishList": "Add product to your wishList",
  "brandNameIsRequired": "Brand Name is Required",
  "styleIsRequired": "Style is Required",
  "keywordIsRequired": "Keyword is Required",
  "remarkIsRequired": "Remark is Required",
  "enteryOurOrderNumberHere": "Enter your order number here",
  "enterCity": "Enter City",
  "Alert": "Alert",
  "requiredVal": "is Required",
  "entervalid": "Enter valid",
  "mustbemorethan2characters": "must be more than 2 characters",
  "confirmpasswordshouldbematchwithpassword":
      "Confirm password should be match with password",
  "lastNameIsRequired": "Last Name is Required",
  "imageUrlIsRequired": "Image link is Required",
  "enterValidText": "Enter Valid Text",
  "searchForPiesSushiTacos": "Search for Pies, Sushi, Tacos...",
  "enterStreetAddress": "Enter Street Address",
  "enterAddress1": "Enter Address 1",
  "enterAddress2": "Enter Address 2",
  "cOUPONAPPLIEDSUCCESSFULLYYOUSAVED": "COUPON APPLIED SUCCESSFULLY. YOU SAVED",
  "oNTHISORDER": "ON THIS ORDER",
  "areYouSureYouWantToRemove": "Are you sure you want to remove",
  "ifyouwouldlikeUsSendYouTheCollectionFrom":
      "If you would like us send you the collection from",
  "plsCreateATicketBelow": "pls. Create a ticket below",
  "orderNumberIsRequired": "Order Number Is Required",
  "addressDetails": "Address Details",
  "enterZipProvince": "Enter Zip/Province",
  "zipProvince": "Zip/Province",
  "emailAddress": "Email Address",
  "passwordsdonotmatch": "Passwords do not match",
  "passwordsYouHaveEnteredDoNotMatch":
      "Passwords you have entered do not match",
  "invalidPassword": "Invalid Password",
  "registerWithGoogle": "Register with Google",
  "registerWithFacebook": "Register with Facebook",
  "registerwithTwitter": "Register with Twitter",
  "imageurl": "Image Url",
  "findCountry": "Find Country...",
  "orderReference": "Order reference",
  "contactNumberIsRequired": "Contact Number is required",
  "headOffice": "Head Office",
  "soloLuxuryOLablesLtd": "SOLO LUXURY, O LABELS LTD,",
  "WenlockRoad": "20-22 WENLOCK ROAD, LONDON, N1 7GU",
  "youCanViewResponsesOnYourEmail": "You can view responses on your email",
  "underMyTickets": "under My Tickets.",
  "friendFirstNameRequired": "Friend First Name Required",
  "friendEmailAddressIsRequired": "Friend Email Address Is Required",
  "friendPhoneNumberIsRequired": "Friend Phone Number Is Required",
  "selectProductAnOption": "Select Product An Option",
  "Pleaseagreefornewsletter": "Please agree for newsletter",
  "referAFriend": "Refer a friend",
  "shipTo": "Ship To",
  "brandProductQuery": "Brand Product Query",
  "categoryProductQuery": "Category Product Query",
  "cartDetails": "Cart Details",
  "date": "Date",
  "ConfirmationEmailRequired": "Confirmation Email is Required",
  "pleaseentermessage": "Please enter message",
  "typeyourmessagehere": "Type your message here...",
  "goterrorpleasetryagain": "Got error, please try again.",
  "internalservererror": "Internal server error",
  "oopssomethingwentwrong": "Oops something went wrong",
  "SigninwithGoogle": "Sign in with Google",
  "SignnwithApple": "Sign in with Apple",
  "SigninwithTwitter": "Sign in with Twitter",
  "SigninwithFacebook": "Sign in with Facebook",
  "Requestcancelled": "Request cancelled",
  "Requesttimeout": "Request timeout",
  "Get": "Get",
  "peroff": "10% off ",
  "onyourfirstpurchaseonAPPUSECode":
      " on your first purchase on APP. USE Code:",
  "SOLOAPP10": "SOLOAPP10",
  "TnCApply": "  *T&C Apply ",
};
