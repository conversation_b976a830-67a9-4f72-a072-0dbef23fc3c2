const Map<String, String> cn = {
  "language": "英语",
  "appNameText": "独享奢华",
  "aboutUsText": "关于我们",
  "referFriendText": "推荐朋友",
  "returnsRefundsText": "退货和退款",
  "faqText": "常问问题",
  "contactText": "接触",
  "socialText": "社会的",
  "companyText": "公司",
  "contactUs": "联系我们",
  "myOrders": "我的订单",
  "myOrderImage": "图片",
  "productName": "产品名称",
  "sku": "库存单位",
  "price": "价钱",
  "quantity": "数量",
  "status": "状态",
  "action": "行动",
  "paymentMethod": " 付款方法",
  "shippingAddress": "送货地址",
  "billingAddress": "帐单地址",
  "continueShopping": "继续购物",
  "freuentlyAskedQueText": "经常问的问题",
  "shopAndPlaceOrder": "如何购物和下订单？",
  "accountToPlaceOrderQues": "我需要一个帐户才能下订单吗？",
  "accountToPlaceOrderAns":
      "不，您只需要一个电子邮件地址。我们建议您注册一个帐户以开始将作品添加到您的愿望清单，但是，您也可以以访客身份下订单和跟踪订单，并在适合您的时间注册。",
  "registeredCustomersText": "注册客户",
  "registeredCustomersDescriptionText": "如果您有帐户，请使用您的\n电子邮件地址登录。",
  "usernameText": "用户名",
  "passwordText": "密码",
  "signInText": "登入",
  "forgotYourPasswordText": "忘记密码了吗？",
  "newCustomersText": "新客户",
  "loginDescriptionText": "创建帐户有很多好处：更快地结帐\n、保留多个地址\n跟踪订单等等。",
  "createAccountText": "创建帐号",
  "firstNameText": "名",
  "lastNameText": "姓",
  "dateOfBirthText": "出生日期",
  "pleaseSelectValidDateText": "请选择有效日期",
  "marriageAnniversaryText": "结婚周年",
  "emailText": "电子邮件",
  "confirmPasswordText": "确认密码",
  "signUpForNewsletterText": "订阅时事通讯",
  "forgotYourPasswordDescriptionText": "请在下方输入您的电子邮件地址以接收\n密码重置链接",
  "enterYourEmailText": "输入你的电子邮箱",
  "resetMyPasswordText": "重置我的密码",
  "accessYourAccountDetailsText": "访问您的帐户详细信息和奖励",
  "signUpText": "报名",
  "myOrdersText": "我的订单",
  "myWishlistText": "我的收藏",
  "addressBookText": "地址簿",
  "accountInformationText": "帐户信息",
  "myTicketsText": "我的门票",
  "trackOrderText": "跟踪订单",
  "companyMyAccountText": "公司",
  "contactUsText": "联系我们",
  "secureShoppingText": "安全购物",
  "advancedSearchText": "高级搜索",
  "testimonialsText": "感言",
  "socialMyAccountText": "社会的",
  "hopeText": "希望",
  "affiliateProgramText": "会员计划",
  "influencerRegistrationText": "影响者注册",
  "exchangeText": "换货和退货",
  "contactMyAccountText": "联系人",
  "privacyPolicyText": "隐私政策",
  "termsConditionsText": "条款和条件",
  "shippingText": "船运",
  "aboutText": "关于",
  "aboutUsMyAccountText": "关于我们",
  "referFriendMyAccountText": "推荐朋友",
  "returnsText": "退货和退款",
  "faqMyAccountText": "常问问题",
  "myAccountText": "我的账户",
  "filtersText": "过滤器",
  "priceText": "价钱",
  "colorText": "颜色",
  "brandText": "品牌",
  "sizeText": "尺寸",
  "applyText": "申请",
  "searchText": "搜索..",
  "aboutUsContain1": "SOLO 是一个顶级的在线奢侈品时尚目的地，全球每月有 500 万游客。",
  "aboutUsContain2":
      '该网站精选了顶级设计师和年轻的新兴人才。 Solo 于 1930 年代初在佛罗伦萨成立，公司的同名概念店在 Via Roma 开业。自 1999 年以来，该公司将 90% 的总收入归功于在线销售。直到今天，它仍然是一个 100% 私有的家族企业，创始人的孙子 Andrea Panconesi 担任首席执行官，他的女儿 Annagreta Panconesi 担任创意总监。',
  "aboutUsContain3":
      '该网站提供各种服装、鞋子、包袋、配饰、豪华家居内饰和美容产品，其中包括来自 Balenciaga、Vetements、Off-White、J.W. 等最负盛名设计师的男士、女士和儿童产品。 Anderson、Balmain、Dolce & Gabbana、Gucci、Saint Laurent、Valentino、Givenchy、Loewe、Jacquemus 等等。',
  "aboutUsContain4":
      '目前有来自 15 个不同国家的 200 人致力于制作和维护 Solo。该网站提供 9 种语言版本：英语、意大利语、德语、中文、俄语、西班牙语、法语、韩语和日语。该网站完全由内部创建，佛罗伦萨办公室设有平面设计、IT、客户服务、营销和采购部门。造型师和新闻团队在世界时尚之都米兰的 Solo 办公室工作。',
  "aboutUsContain5":
      '所有订单都从 Solo 总部装在经过精心准备并使用最优质材料的盒子中运送到全球，从标志性鞋包到个性化丝带，应有尽有。',
  "aboutUsContain6":
      'Solo 在 2008 年有 150 万独立访问者访问该网站，到 2017 年已增长到 5300 万独立访问者。超过 15% 的在线销售来自美国，其次是德国、英国、意大利、中国和法国。',
  "aboutUsContain7":
      'Solo 通过一系列独家服务和商标计划，包括 Buy It First、Luxury Club、Sneakers Club、LVR Privilege 和 LVR Editions，在线上和线下推动奢侈品零售领域的界限。',
  "profile": "轮廓",
  "ContactNoText": "联系方式。",
  "websiteUrlText": "网址",
  "countryText": "国家",
  "postCodeText": "邮政编码",
  "socialLinksText": "社交链接",
  "facebookText": "Facebook",
  "instagramText": "Instagram",
  "twitterText": "推特",
  "youtubeText": "YouTube",
  "linkendinText": "林肯",
  "pinterestText": "Pinterest",
  "followersText": "追随者",
  "workedOnText": "从事的项目",
  "mr": "先生。",
  "yourEmail1Text": "你的邮件",
  "subscribeText": "订阅",
  "menuText": "菜单",
  "accountText": "帐户",
  "checkOutText": "退房",
  "purchaseText": "请在下方输入您的详细信息以完成购买",
  "shippingAddressText": "送货地址",
  "shippingMethodText": "邮寄方式",
  "paymentMethodText": "付款方法",
  "orderSummaryText": "订单摘要",
  "alreadyAcText": "您已经有我们的帐户。登录或以访客身份继续。",
  "loginText": "登录",
  "forgotText": "忘记密码了吗？",
  "stLineText": "街道地址：1号线",
  "cityText": "城市",
  "japanText": "日本",
  "stateText": "州/省",
  "zipPostalText": "邮编/邮政编码",
  "phoneNumberText": "电话号码",
  "itemInCartText": "购物车中的物品",
  "qtyText": "数量",
  "viewDetailsText": "查看详情",
  "applyDisCodeText": "应用折扣码",
  "enterDisCodeText": "输入折扣码",
  "applyDiscountText": "申请折扣",
  "orderTotalText": "合计订单",
  "placeOrderText": "下订单",
  "storePaymentText": "存储的付款方式",
  "newsLetterText": "通讯订阅",
  "enterEmailAddress": "请输入电邮地址",
  "enterPhoneNumber": "输入电话号码",
  "enterValidEmailAddress": "输入有效的电子邮件地址",
  "phoneNumberIsRequired": "需要电话号码",
  "phoneNumberIsNotValid": "电话号码无效",
  "emailIsRequired": "需要电子邮件",
  "invalidEmail": "不合规电邮",
  "passwordIsRequired": "密码是必需的",
  "passwordRegExp": "密码必须至少8个字符，并且包含大写字母和小写字母的混合物，至少一个数字和一个特殊字符（例如，！ @＃？）。",
  "email": "电子邮件",
  "confirmPasswordIsRequired": "确认密码为必填项",
  "mrs": "太太。",
  "tearmsAndCondition": "条款与条件",
  "nylonMessengerBackpackMsg":
      "Balenziaga的尼龙信使背包，带有钩子，顶部手柄，可调节的肩带，带鲜明对比徽标的前贴片拉链口袋，挂钩作为装饰元件的前贴片拉链。",
  "ourServices": "我们的服务",
  "ourWebsite": "我们的网站",
  "productDetails": "产品详情",
  "composition": "组成",
  "sortBy": "排序方式",
  "enterFirstName": "输入名字",
  "enterLastName": "输入姓氏",
  "enterStreetAddress1": "输入街道地址1",
  "enterStreetAddress2": "输入街道地址2",
  "enterStreetAddress3": "输入街道地址3",
  "streetAddress3": "街道地址3",
  "enterCityName": "输入城市名称",
  "enterZipPostalCode": "输入邮政编码",
  "enterStateProvince": "进入州/省",
  "emailAddressRequired": "需要电子邮件地址",
  "viewCouponList": "查看优惠券列表",
  "changeLanguageAndCurrency": "改变语言和货币",
  "friendFirstName": "朋友名字",
  "friendEmailAddress": "朋友电子邮件地址",
  "friendPhoneNumber": "朋友电话号码",
  "register": "登记",
  "surnameIsRequired": "需要姓氏",
  "enterCouponCode": "输入优惠券代码",
  "addProductToYourWishList": "将产品添加到您的愿望清单",
  "brandNameIsRequired": "需要品牌名称",
  "styleIsRequired": "需要样式",
  "keywordIsRequired": "需要关键字",
  "remarkIsRequired": "需要备注",
  "enteryOurOrderNumberHere": "在此处输入您的订单号",
  "enterCity": "进入城市",
  "Alert": "警报",
  "requiredVal": "是必须的",
  "entervalid": "输入有效",
  "mustbemorethan2characters": "必须超过2个字符",
  "confirmpasswordshouldbematchwithpassword": "确认密码应与密码匹配",
  "lastNameIsRequired": "需要姓氏",
  "imageUrlIsRequired": "需要图像链接",
  "enterValidText": "输入有效文本",
  "searchForPiesSushiTacos": "搜索馅饼，寿司，炸玉米饼...",
  "enterStreetAddress": "输入街道地址",
  "enterAddress1": "输入地址1",
  "enterAddress2": "输入地址2",
  "cOUPONAPPLIEDSUCCESSFULLYYOUSAVED": "优惠券成功应用。你救了",
  "oNTHISORDER": "在此订单上",
  "areYouSureYouWantToRemove": "你确定要删除吗",
  "ifyouwouldlikeUsSendYouTheCollectionFrom": "如果您希望我们向您发送收藏",
  "plsCreateATicketBelow": "请在下面创建票",
  "orderNumberIsRequired": "需要订单号",
  "addressDetails": "详细地址",
  "enterZipProvince": "输入Zip/省",
  "zipProvince": "拉链/省",
  "emailAddress": "电子邮件地址",
  "passwordsdonotmatch": "密码不匹配",
  "passwordsYouHaveEnteredDoNotMatch": "您输入的密码不匹配",
  "invalidPassword": "无效的密码",
  "registerWithGoogle": "向Google注册",
  "registerWithFacebook": "向Facebook注册",
  "registerwithTwitter": "向Twitter注册",
  "imageurl": "图像URL",
  "findCountry": "找到国家...",
  "orderReference": "订单参考",
  "contactNumberIsRequired": "需要联系电话",
  "headOffice": "总公司",
  "soloLuxuryOLablesLtd": "Solo Luxury，O Labels Ltd，",
  "WenlockRoad": "伦敦Wenlock Road 20-22，N1 7GU",
  "youCanViewResponsesOnYourEmail": "您可以在电子邮件中查看回复",
  "underMyTickets": "在我的票下。",
  "friendFirstNameRequired": "朋友需要的名字",
  "friendEmailAddressIsRequired": "朋友电子邮件地址是必需的",
  "friendPhoneNumberIsRequired": "朋友电话号码是需要的",
  "selectProductAnOption": "选择产品选项",
  "pleaseEnterYourEmailToTrackYourTicketByEmail": "请输入您的电子邮件以通过电子邮件跟踪您的机票。",
  "Pleaseagreefornewsletter": "请同意新闻通讯",
  "referAFriend": "介绍个朋友",
  "shipTo": "运送到",
  "brandProductQuery": "品牌产品查询",
  "categoryProductQuery": "类别产品查询",
  "cartDetails": "购物车细节",
  "date": "日期",
  "ConfirmationEmailRequired": "需要确认电子邮件",
  "pleaseentermessage": "请输入消息",
  "typeyourmessagehere": "在这里输入你的消息...",
  "goterrorpleasetryagain": "有错误，请重试。",
  "internalservererror": "内部服务器错误",
  "oopssomethingwentwrong": "哎呀！出事了",
  "SigninwithGoogle": "与Google登录",
  "SignnwithApple": "与苹果登录",
  "SigninwithTwitter": "与Twitter登录",
  "SigninwithFacebook": "与Facebook登录",
  "Requestcancelled": "取消请求",
  "Requesttimeout": "请求超时",
  "Get": "得到",
  "peroff": "10％的折扣",
  "onyourfirstpurchaseonAPPUSECode": " 在您在应用程序上首次购买。使用代码：",
  "SOLOAPP10": "SoloApp10",
  "TnCApply": "  *T＆C申请",
};
