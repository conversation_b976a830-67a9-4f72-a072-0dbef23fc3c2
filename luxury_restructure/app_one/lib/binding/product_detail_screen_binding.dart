import 'package:base_controller/const/key_value_constants.dart';
import 'package:base_controller/controllers/country_controllers.dart';
import 'package:app_one/core/consts/app_constants.dart';
import 'package:domain/usecases/address_api_usecase/get_country_list_usecase.dart';
import 'package:domain/usecases/cart_get_data_usecase/add_coupons_to_cart_usecase.dart';
import 'package:domain/usecases/cart_get_data_usecase/delete_applied_coupons_usecase.dart';
import 'package:domain/usecases/cart_get_data_usecase/delete_cart_product_data_usecase.dart';
import 'package:domain/usecases/cart_get_data_usecase/delete_cart_qty_usecase.dart';
import 'package:domain/usecases/cart_get_data_usecase/get_cart_data_usecase.dart';
import 'package:domain/usecases/cart_get_data_usecase/get_coupons_usecase.dart';
import 'package:domain/usecases/cart_get_data_usecase/get_donation_list_usecase.dart';
import 'package:domain/usecases/cart_get_data_usecase/get_total_usecase.dart';
import 'package:domain/usecases/dashboard_api_usecase/get_logo_usecase.dart';
import 'package:domain/usecases/generate_cart_usecase/add_to_cart_usecase.dart';
import 'package:domain/usecases/menu_usecase/get_menu_api_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/post_create_my_ticket_usecase.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_choose_in_size_list.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_create_cart_api_response.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_estimated_time.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_generate_cart_api_response.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_product_detail_api.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_recommended_product_response.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_size_list_api.dart';
import 'package:domain/usecases/recommended_products_api_usecase/guest_post_add_to_cart_product_response.dart';
import 'package:domain/usecases/recommended_products_api_usecase/guest_update_product_qty.dart';
import 'package:domain/usecases/recommended_products_api_usecase/post_add_to_cart_product_response.dart';
import 'package:domain/usecases/recommended_products_api_usecase/post_notify_me_req.dart';
import 'package:domain/usecases/recommended_products_api_usecase/post_special_size_response.dart';
import 'package:domain/usecases/recommended_products_api_usecase/update_product_qty.dart';
import 'package:domain/usecases/wish_list_api_usecase/add_to_wish_list.dart';
import 'package:domain/usecases/wish_list_api_usecase/delete_wish_list.dart';
import 'package:domain/usecases/wish_list_api_usecase/get_wish_list_api_response_usecase.dart';
import 'package:get/get.dart';
import 'package:meta_package/api/api_endpoint/cart_api.dart';
import 'package:meta_package/api/api_endpoint/recommended_product_api.dart';
import 'package:meta_package/api/services/add_address_service.dart';
import 'package:meta_package/api/services/cart_service.dart';
import 'package:meta_package/api/services/local_store_service.dart';
import 'package:presentation/navigation/navigation.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/common_drawer_controller.dart';
import 'package:base_controller/controllers/cart_controller.dart';
import 'package:base_controller/controllers/dashboard_controller.dart';
import 'package:base_controller/controllers/product_detail_controller.dart';
import 'package:base_controller/controllers/wishlist_controller.dart';
import 'package:domain/usecases/product_list_api_usecase/get_sorted_product_list_api_response.dart';

class ProductDetailScreenBinding extends Bindings {
  @override
  void dependencies() {
    String baseUrl = AppConstants.apiEndPointLogin;

    Get.lazyPut(() => GetProductDetailApi(baseUrl: baseUrl));
    Get.lazyPut(() => GetRecommendedProductResponse(baseUrl: baseUrl));
    Get.lazyPut(() => GetSizeListApi(baseUrl: baseUrl));
    Get.lazyPut(() => GetChooseInSizeList(baseUrl: baseUrl));
    Get.lazyPut(() => GetCreateCartApiResponse(baseUrl: baseUrl));
    Get.lazyPut(() => PostAddToCartProductResponse(baseUrl: baseUrl));
    Get.lazyPut(() => GuestPostAddToCartProductResponse(baseUrl: baseUrl));
    Get.lazyPut(() => PostNotifyMeReq(baseUrl: baseUrl));
    Get.lazyPut(() => PostSpecialSizeResponse(baseUrl: baseUrl));
    Get.lazyPut(() => GetEstimatedTime(baseUrl: baseUrl));
    Get.lazyPut(() => GetSortedProductListApiResponse(baseUrl: baseUrl));

    Get.lazyPut(() => LocalStore());

    Get.lazyPut(() => GetCountryList(addAddressService: AddAddressService(baseUrl)));

    Get.lazyPut(() => AddToWishList(baseUrl: baseUrl));

    Get.lazyPut(() => PostCreateMyTicket(baseUrl: baseUrl));

    Get.lazyPut(() => RecommendedProductApi(baseUrl: baseUrl));
    Get.lazyPut(() => CartApi(baseUrl: baseUrl));

    Get.lazyPut(() => AddToCart(cartService: CartService(Get.find(), Get.find())));

    Get.lazyPut(() => GetCoupons(
        baseUrl: AppConstants.apiEndPointLogin,
        recommendedProductApi: RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));

    Get.lazyPut(() => AddCouponsToCart(
        baseUrl: AppConstants.apiEndPointLogin,
        recommendedProductApi: RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));

    Get.lazyPut(() => DeleteAppliedCoupons(
        baseUrl: AppConstants.apiEndPointLogin,
        recommendedProductApi: RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));

    Get.lazyPut(() => GetTotal(
        baseUrl: AppConstants.apiEndPointLogin,
        recommendedProductApi: RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));

    Get.lazyPut(() => GetDonationList(
        baseUrl: AppConstants.apiEndPointLogin,
        recommendedProductApi: RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));

    Get.lazyPut(() => GetCartData(
        baseUrl: AppConstants.apiEndPointLogin,
        recommendedProductApi: RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));

    Get.lazyPut(() => DeleteCartQTY(
        baseUrl: AppConstants.apiEndPointLogin,
        recommendedProductApi: RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));

    Get.lazyPut(() => DeleteCartProductData(
        baseUrl: AppConstants.apiEndPointLogin,
        recommendedProductApi: RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));

    Get.lazyPut(() => LocalStore());

    Get.lazyPut(() => GetGenerateCartApiResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => UpdateProductQty(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => GuestUpdateProductQty(baseUrl: AppConstants.apiEndPointLogin));

    Get.lazyPut(() => AddToWishList(baseUrl: AppConstants.apiEndPointLogin));

    Get.lazyPut(() => GetMenuApiResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => GetLogo(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => DashboardController(
        getMenuApiResponse: Get.find(),
        localStore: Get.find(),
        getCurrencyUseCase: Get.find(),
        getLogoUseCase: Get.find()));
    Get.lazyPut(() => CommonDrawerController(getMenuApiResponseUseCase: Get.find()));
    Get.lazyPut(() => GetMenuApiResponse(baseUrl: AppConstants.apiEndPointLogin));

    Get.lazyPut(() => CartScreenController(
        addToWishListUseCase: Get.find(),
        localStore: Get.find(),
        guestUpdateProductQty: Get.find(),
        updateProductQty: Get.find(),
        deleteCartProductDataUseCase: Get.find(),
        deleteCartQTYUseCase: Get.find(),
        getCartDataUseCase: Get.find(),
        getDonationListUseCase: Get.find(),
        getTotalUseCase: Get.find(),
        deleteAppliedCouponsUseCase: Get.find(),
        addCouponsToCartUseCase: Get.find(),
        getCouponsUseCase: Get.find(),
        getGenerateCartApiResponseUseCase: Get.find(),
        urlWithCode: ''));

    Get.lazyPut(() => GetChooseInSizeList(baseUrl: baseUrl));
    Get.lazyPut(() => PostAddToCartProductResponse(baseUrl: baseUrl));
    Get.lazyPut(() => GuestPostAddToCartProductResponse(baseUrl: baseUrl));
    Get.lazyPut(() => GetWishListApiResponse(baseUrl: baseUrl));
    Get.lazyPut(() => DeleteWishList(baseUrl: baseUrl));
    Get.lazyPut(() => WishListController(
        cartScreenController: CartScreenController(
            addToWishListUseCase: Get.find(),
            localStore: Get.find(),
            guestUpdateProductQty: Get.find(),
            updateProductQty: Get.find(),
            deleteCartProductDataUseCase: Get.find(),
            deleteCartQTYUseCase: Get.find(),
            getCartDataUseCase: Get.find(),
            getDonationListUseCase: Get.find(),
            getTotalUseCase: Get.find(),
            deleteAppliedCouponsUseCase: Get.find(),
            addCouponsToCartUseCase: Get.find(),
            getCouponsUseCase: Get.find(),
            getGenerateCartApiResponseUseCase: Get.find(),
            urlWithCode: ''),
        getChooseInSizeListUseCase: Get.find(),
        getGenerateCartApiResponseUseCase: Get.find(),
        postAddToCartProductResponseUseCase: Get.find(),
        guestPostAddToCartProductResponseUseCase: Get.find(),
        getWishListApiResponseUseCase: Get.find(),
        deleteWishListUseCase: Get.find(),
        localStore: Get.find()));

    Get.lazyPut(() => ProductDetailController(
        getEstimatedTimeUseCase: Get.find(),
        getProductDetailApiUseCase: Get.find(),
        getRecommendedProductResponseUseCase: Get.find(),
        getSizeListApiUseCase: Get.find(),
        getChooseInSizeListUseCase: Get.find(),
        getCreateCartApiResponseUseCase: Get.find(),
        postAddToCartProductResponseUseCase: Get.find(),
        guestPostAddToCartProductResponseUseCase: Get.find(),
        postNotifyMeReqUseCase: Get.find(),
        postSpecialSizeResponseUseCase: Get.find(),
        getCountryListUseCase: Get.find(),
        addToWishListUseCase: Get.find(),
        postCreateMyTicketUseCase: Get.find(),
        localStore: Get.find(),
        addToCartUseCase: Get.find(),
        getSortedProductListApiResponseUseCase: Get.find(),
        cartScreenController: CartScreenController(
            addToWishListUseCase: Get.find(),
            localStore: Get.find(),
            guestUpdateProductQty: Get.find(),
            updateProductQty: Get.find(),
            deleteCartProductDataUseCase: Get.find(),
            deleteCartQTYUseCase: Get.find(),
            getCartDataUseCase: Get.find(),
            getDonationListUseCase: Get.find(),
            getTotalUseCase: Get.find(),
            deleteAppliedCouponsUseCase: Get.find(),
            addCouponsToCartUseCase: Get.find(),
            getCouponsUseCase: Get.find(),
            getGenerateCartApiResponseUseCase: Get.find(),
            urlWithCode: ''),
        wishListController: WishListController(
            cartScreenController: CartScreenController(
                addToWishListUseCase: Get.find(),
                localStore: Get.find(),
                guestUpdateProductQty: Get.find(),
                updateProductQty: Get.find(),
                deleteCartProductDataUseCase: Get.find(),
                deleteCartQTYUseCase: Get.find(),
                getCartDataUseCase: Get.find(),
                getDonationListUseCase: Get.find(),
                getTotalUseCase: Get.find(),
                deleteAppliedCouponsUseCase: Get.find(),
                addCouponsToCartUseCase: Get.find(),
                getCouponsUseCase: Get.find(),
                getGenerateCartApiResponseUseCase: Get.find(),
                urlWithCode: ''),
            getChooseInSizeListUseCase: Get.find(),
            getGenerateCartApiResponseUseCase: Get.find(),
            postAddToCartProductResponseUseCase: Get.find(),
            guestPostAddToCartProductResponseUseCase: Get.find(),
            getWishListApiResponseUseCase: Get.find(),
            deleteWishListUseCase: Get.find(),
            localStore: Get.find())));

    Get.lazyPut(() => Navigation());


  }
}
