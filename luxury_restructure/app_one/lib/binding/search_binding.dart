import 'package:app_one/core/consts/app_constants.dart';
import 'package:domain/usecases/menu_usecase/get_menu_api_search_response_usecase.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_choose_in_size_list.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_generate_cart_api_response.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_product_detail_api.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_recommended_product_response.dart';
import 'package:domain/usecases/recommended_products_api_usecase/guest_post_add_to_cart_product_response.dart';
import 'package:domain/usecases/recommended_products_api_usecase/post_add_to_cart_product_response.dart';
import 'package:domain/usecases/search_api_usecase/get_search_product_api_response.dart';
import 'package:domain/usecases/search_api_usecase/get_searchapi_response.dart';
import 'package:domain/usecases/wish_list_api_usecase/add_to_wish_list.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:meta_package/api/services/local_store_service.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/search_controller.dart';

class SearchBinding extends Bindings {
  @override
  void dependencies() {
    debugPrint("SEARCH Binding ======");
    const baseUrl = AppConstants.baseUrlStaging;
    Get.lazyPut(
      () => MySearchController(
        localStore: LocalStore(),
        getChooseInSizeListUseCase: GetChooseInSizeList(baseUrl: baseUrl),
        getMenuApiSearchResponseUseCase:
            GetMenuApiSearchResponse(baseUrl: baseUrl),
        getProductDetailApiUseCase: GetProductDetailApi(baseUrl: baseUrl),
        getGenerateCartApiResponseUseCase:
            GetGenerateCartApiResponse(baseUrl: baseUrl),
        getRecommendedProductResponseUseCase:
            GetRecommendedProductResponse(baseUrl: baseUrl),
        postAddToCartProductResponseUseCase:
            PostAddToCartProductResponse(baseUrl: baseUrl),
        guestPostAddToCartProductResponseUseCase:
            GuestPostAddToCartProductResponse(baseUrl: baseUrl),
        getSearchApiResponseUseCase: GetSearchApiResponse(baseUrl: baseUrl),
        addToWishListUseCase: AddToWishList(baseUrl: baseUrl),
        getSearchProductApiResponseUseCase:
            GetSearchProductApiResponse(baseUrl: baseUrl),
      ),
    );
    debugPrint("SEARCH Binding ======");
  }
}
