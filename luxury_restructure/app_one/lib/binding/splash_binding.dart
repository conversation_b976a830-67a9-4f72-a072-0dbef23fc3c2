import 'package:app_one/core/consts/constants.dart';
import 'package:domain/usecases/splash_api_usecase/get_splash_api_response.dart';
import 'package:domain/usecases/store_api_usecase/get_store_configs_api_response.dart';
import 'package:domain/usecases/store_api_usecase/get_store_websites_api_response.dart';
import 'package:domain/usecases/update_lang_api_usecase/post_device_info_api_response.dart';
import 'package:get/get.dart';
import 'package:base_controller/controllers/splash_controllers/app_one_splash_controller.dart';

class SplashBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SplashController(
        GetSplashAPIResponse(baseUrl: AppConstants.apiCountryGet),
        PostDeviceInfoApiResponse(baseUrl: AppConstants.apiEndPointLogin),
        GetStoreConfigsApiResponse(baseUrl: AppConstants.apiEndPointLogin),
        GetStoreWebsitesApiResponse(baseUrl: AppConstants.apiEndPointLogin)));
  }
}
