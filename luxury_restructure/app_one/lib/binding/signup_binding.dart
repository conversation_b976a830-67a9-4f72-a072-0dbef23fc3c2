import 'package:base_controller/controllers/signup_controller.dart';
import 'package:app_one/core/consts/constants.dart';
import 'package:domain/usecases/auth_api_usecase/email_signup_usecase.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_apple_usecase.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_facebook_usecase.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_google_usecase.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_twitter_usecase.dart';
import 'package:domain/usecases/my_account_usecase/update_user_usecase.dart';
import 'package:get/get.dart';
import 'package:meta_package/api/services/local_store_service.dart';

class SignupBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => EmailSignup(
          baseUrl: AppConstants.apiEndPointLogin,
        ));
    Get.lazyPut(() => LoginWithGoogle(
          baseUrl: AppConstants.apiEndPointLogin,
        ));
    Get.lazyPut(() => LoginWithApple(
          baseUrl: AppConstants.apiEndPointLogin,
        ));
    Get.lazyPut(() => LoginWithFacebook(
          baseUrl: AppConstants.apiEndPointLogin,
        ));
    Get.lazyPut(() => LoginWithTwitter(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => UpdateUser(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => LocalStore());

    Get.lazyPut(() => SignupScreenController(
        emailSignupUseCase: Get.find(),
        updateUserUseCase: Get.find(),
        loginWithAppleUseCase: Get.find(),
        loginWithFacebookUseCase: Get.find(),
        loginWithGoogleUseCase: Get.find(),
        localStore: Get.find(),
        loginWithTwitterUseCase: Get.find()));
  }
}
