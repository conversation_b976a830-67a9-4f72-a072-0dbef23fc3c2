import 'package:app_one/core/consts/constants.dart';
//import 'package:base_controller/controllers/refer_friend_controller.dart';
import 'package:domain/usecases/about_api_usecase/get_refer_friend_usecase.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/refer_friend_controller.dart';
import 'package:get/get.dart';

class ReferFriendBindings extends Bindings {
  @override
  void dependencies() {
    // Get.lazyPut(
    //   () => ReferFriendController(
    //     getReferFriendUseCase:
    //         GetReferFriend(baseUrl: AppConstants.apiEndPointLogin),
    //   ),
    // );
    Get.put(
       ReferFriendController(
        getReferFriendUseCase:
            GetReferFriend(baseUrl: AppConstants.apiEndPointLogin),
      ),
    );
  }
}
