import 'package:base_controller/controllers/track_ticket_controller.dart';
import 'package:domain/usecases/my_ticket_usecase/post_my_ticket_response_provider.dart';
import 'package:get/get.dart';
import 'package:meta_package/api/api_repository/my_ticket_api_repository.dart';
import 'package:meta_package/api/services/ticket_service.dart';
import 'package:app_one/core/consts/app_constants.dart';

class TrackTicketBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TrackTicketController(
        postMyTicketResponseProvider: PostMyTicketResponseProvider(
            myTicketAPIRepository: MyTicketAPIRepository(
                ticketService: TicketService(AppConstants.baseUrlStaging)),
            baseUrl: AppConstants.apiEndPointLogin,
            ticketService: TicketService(AppConstants.baseUrlStaging)), baseUrl: AppConstants.baseUrlStaging));
  }
}
