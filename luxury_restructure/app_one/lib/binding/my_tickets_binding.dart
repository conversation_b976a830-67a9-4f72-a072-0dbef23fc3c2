import 'package:domain/usecases/my_ticket_usecase/post_my_ticket_response_provider.dart';
import 'package:flutter/material.dart';
import 'package:meta_package/api/services/ticket_service.dart';
import 'package:meta_package/api/api_repository/my_ticket_api_repository.dart';
import 'package:domain/usecases/my_ticket_usecase/get_my_tickets.dart';
import 'package:get/get.dart';
import 'package:base_controller/controllers/my_tickets_controller.dart';
import 'package:app_one/core/consts/app_constants.dart';

class MyTicketsBindings extends Bindings {
  @override
  void dependencies() {
    debugPrint('myTicketBindingCalled');
    Get.lazyPut(() => GetMyTickets(
        myTicketAPIRepository: MyTicketAPIRepository(
            ticketService: TicketService(AppConstants.apiEndPointLogin)),
        baseUrl: AppConstants.apiEndPointLogin,
        ticketService: TicketService(AppConstants.apiEndPointLogin)));

    Get.lazyPut(() => PostMyTicketResponseProvider(
        myTicketAPIRepository: MyTicketAPIRepository(
            ticketService: TicketService(AppConstants.apiEndPointLogin)),
        baseUrl: AppConstants.apiEndPointLogin,
        ticketService: TicketService(AppConstants.apiEndPointLogin)));

    Get.lazyPut(() => MyTicketsController(Get.find(), Get.find()));
  }
}
