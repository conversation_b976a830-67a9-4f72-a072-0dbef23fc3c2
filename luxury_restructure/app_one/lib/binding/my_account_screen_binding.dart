import 'package:app_one/core/consts/app_constants.dart';
import 'package:domain/usecases/my_account_usecase/get_account_detail_response_usecase.dart';
import 'package:get/get.dart';
import 'package:presentation/navigation/navigation.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/my_account_controller.dart';

class MyAccountScreenBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => Navigation(), fenix: true);
    Get.lazyPut(
        () => GetAccountDetailResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(
        () => MyAccountController(getAccountDetailResponse: Get.find()));
  }
}
