import 'package:domain/usecases/cart_get_data_usecase/get_coupons_usecase.dart';
import 'package:meta_package/api/api_endpoint/cart_api.dart';
import 'package:meta_package/api/api_endpoint/recommended_product_api.dart';

import '../all_imports.dart';
import 'package:base_controller/controllers/my_coupons_controller.dart';
import '../core/consts/app_constants.dart';

class MyCouponsBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => GetCoupons(
        baseUrl: AppConstants.apiEndPointLogin,
        recommendedProductApi:
            RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));

    Get.put(MyCouponsController(getCouponsUsecase: Get.find()));
  }
}
