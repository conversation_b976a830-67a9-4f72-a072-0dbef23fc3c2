import 'package:app_one/core/consts/constants.dart';
import 'package:domain/usecases/newdletter_api_usecase/subscribe_news_letter.dart';
import 'package:domain/usecases/track_your_order_usecase/get_guest_track_your_order_api_response.dart';
import 'package:get/get.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/common_track_your_order_by_guest_controller.dart';

class CommonTrackOrderByGuestBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() =>
        GetGuestTrackOrderApiResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(
        () => SubscribeNewsLetter(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => TrackYourOrderByGuestController(
        subscribeNewsLetterUseCase: Get.find(),
        getGuestTrackOrderApiResponseUseCase: Get.find()));
  }
}
