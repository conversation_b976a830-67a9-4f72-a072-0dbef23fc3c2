import 'package:domain/usecases/address_api_usecase/add_address_usecase.dart';
import 'package:domain/usecases/address_api_usecase/billing_address_usecase.dart';
import 'package:domain/usecases/address_api_usecase/delete_address_usecase.dart';
import 'package:domain/usecases/address_api_usecase/get_address_list_usecase.dart';
import 'package:domain/usecases/address_api_usecase/update_address_usecase.dart';
import 'package:meta_package/api/services/add_address_service.dart';

import '../all_imports.dart';
import 'package:base_controller/controllers/address_book_controller.dart';
import '../core/consts/app_constants.dart';

class AddressBookBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => AddressBookController(
        getAddressListUseCase: GetAddressList(
            addAddressService:
                AddAddressService(AppConstants.apiEndPointLogin)),
        addAddressUsecase: AddAddress(
            addAddressService:
                AddAddressService(AppConstants.apiEndPointLogin)),
         deleteAddressUsecase: DeleteAddress(
            addAddressService:
                AddAddressService(AppConstants.apiEndPointLogin)), 
          updateAddressUsecase: UpdateAddress(
            addAddressService:
                AddAddressService(AppConstants.apiEndPointLogin)),
          billingAddressUsecase: BillingAddress(
            addAddressService:
                AddAddressService(AppConstants.apiEndPointLogin)),      
                ));
  }
}
