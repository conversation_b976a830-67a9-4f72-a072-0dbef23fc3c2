import 'package:base_controller/controllers/returns_and_refunds_controller.dart';
import 'package:app_one/core/consts/constants.dart';
import 'package:domain/usecases/return_api_usecase/get_returns_refunds_api_response.dart';
import 'package:get/get.dart';

class ReturnAndReferBinding extends Bindings {
  @override
  void dependencies() {
    // Get.lazyPut(
    //   () => ReturnsAndRefundsController(
    //       getReturnsRefundsApiResponse: GetReturnsRefundsApiResponse(baseUrl: AppConstants.apiEndPointLogin)),
    // );
    Get.put(
     ReturnsAndRefundsController(
          getReturnsRefundsApiResponse: GetReturnsRefundsApiResponse(baseUrl: AppConstants.apiEndPointLogin)),
    );
  }
}
