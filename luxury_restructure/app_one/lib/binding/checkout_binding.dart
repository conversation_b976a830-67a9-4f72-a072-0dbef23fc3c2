import 'package:app_one/core/consts/constants.dart';
import 'package:base_controller/controllers/checkout_order_controller.dart';
import 'package:domain/usecases/address_api_usecase/get_address_list_usecase.dart';
import 'package:domain/usecases/address_api_usecase/get_country_list_usecase.dart';
import 'package:domain/usecases/address_api_usecase/get_multiple_address_usecase.dart';
import 'package:domain/usecases/address_api_usecase/post_address_usecase.dart';
import 'package:domain/usecases/cart_get_data_usecase/add_coupons_to_cart_usecase.dart';
import 'package:domain/usecases/cart_get_data_usecase/delete_applied_coupons_usecase.dart';
import 'package:domain/usecases/cart_get_data_usecase/get_coupons_usecase.dart';
import 'package:domain/usecases/cart_get_data_usecase/get_total_usecase.dart';
import 'package:domain/usecases/check_order_usecase/post_check_email_usecase.dart';
import 'package:domain/usecases/check_order_usecase/post_estimate_api_response_usecase.dart';
import 'package:domain/usecases/check_order_usecase/post_estimate_usecase.dart';
import 'package:domain/usecases/check_order_usecase/post_guest_create_order_usecase.dart';
import 'package:domain/usecases/check_order_usecase/post_guest_estimate_usecase.dart';
import 'package:domain/usecases/check_order_usecase/post_guest_shipping_information_usecase.dart';
import 'package:domain/usecases/check_order_usecase/post_payment_information_usecase.dart';
import 'package:domain/usecases/check_order_usecase/post_shipping_information_usecase.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:meta_package/api/api_endpoint/cart_api.dart';
import 'package:meta_package/api/api_endpoint/check_order_api.dart';
import 'package:meta_package/api/api_endpoint/recommended_product_api.dart';
import 'package:meta_package/api/services/add_address_service.dart';
import 'package:meta_package/api/services/checkout_service.dart';
import 'package:meta_package/api/services/local_store_service.dart';

class CheckoutOrderBindings extends Bindings {
  @override
  void dependencies() {
    debugPrint("CHECKOUT Binding ======");
    String baseUrl = AppConstants.apiEndPointLogin;
    Get.lazyPut(
      () => PostEstimateApiResponse(
        baseUrl: baseUrl,
        checkoutService: CheckoutService(
          checkoutOrderApi:
              CheckOrderApi(baseUrl: AppConstants.apiEndPointLogin),
        ),
      ),
    );

    Get.lazyPut(
          () => PostCheckEmail(
        baseUrl: baseUrl,
        checkoutService: CheckoutService(
          checkoutOrderApi:
          CheckOrderApi(baseUrl: AppConstants.apiEndPointLogin),
        ),
      ),
    );

    Get.lazyPut(
      () => PostGuestEstimate(
        baseUrl: baseUrl,
        checkoutService: CheckoutService(
          checkoutOrderApi:
              CheckOrderApi(baseUrl: AppConstants.apiEndPointLogin),
        ),
      ),
    );
    Get.lazyPut(() => PostGuestShippingInformation(
        baseUrl: baseUrl,
        checkoutService: CheckoutService(
            checkoutOrderApi:
                CheckOrderApi(baseUrl: AppConstants.apiEndPointLogin))));
    Get.lazyPut(() => GetMultipleAddress(
        addAddressService: AddAddressService(AppConstants.apiEndPointLogin)));
    Get.lazyPut(() => PostEstimate(
        baseUrl: baseUrl,
        checkoutService: CheckoutService(
            checkoutOrderApi:
                CheckOrderApi(baseUrl: AppConstants.apiEndPointLogin))));
    Get.lazyPut(() => PostShippingInformation(
        baseUrl: baseUrl,
        checkoutService: CheckoutService(
            checkoutOrderApi:
                CheckOrderApi(baseUrl: AppConstants.apiEndPointLogin))));
    Get.lazyPut(() => PostPaymentInformation(
        baseUrl: baseUrl,
        checkoutService: CheckoutService(
            checkoutOrderApi:
                CheckOrderApi(baseUrl: AppConstants.apiEndPointLogin))));
    Get.lazyPut(() => PostGuestCreateOrder(
        baseUrl: baseUrl,
        checkoutService: CheckoutService(
            checkoutOrderApi:
                CheckOrderApi(baseUrl: AppConstants.apiEndPointLogin))));
    Get.lazyPut(() => GetCountryList(
        addAddressService: AddAddressService(AppConstants.apiEndPointLogin)));
    Get.lazyPut(() => GetAddressList(
        addAddressService: AddAddressService(AppConstants.apiEndPointLogin)));
    Get.lazyPut(() => GetCoupons(
        baseUrl: baseUrl,
        recommendedProductApi:
            RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));
    Get.lazyPut(() => GetTotal(
        baseUrl: baseUrl,
        recommendedProductApi:
            RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));
    Get.lazyPut(() => AddCouponsToCart(
        baseUrl: baseUrl,
        recommendedProductApi:
            RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));
    Get.lazyPut(() => DeleteAppliedCoupons(
        baseUrl: baseUrl,
        recommendedProductApi:
            RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
        cartApi: CartApi(
          baseUrl: AppConstants.apiEndPointLogin,
        )));
    Get.lazyPut(() => PostAddress(
        addAddressService: AddAddressService(AppConstants.apiEndPointLogin)));
    Get.lazyPut(() => LocalStore());

    Get.lazyPut(() => CheckoutOrderController(
          postGuestShippingInformationUseCase: Get.find(),
          postEstimateApiResponseUseCase: Get.find(),
          getMultipalAddressUseCase: Get.find(),
          postEstimateUseCase: Get.find(),
          postShippingInformationUseCase: Get.find(),
          postPaymentInformationUseCase: Get.find(),
          postGuestCreateOrderUseCase: Get.find(),
          getCountryListUseCase: Get.find(),
          getAddressListUseCase: Get.find(),
          getCouponsUseCase: Get.find(),
          getTotalUseCase: Get.find(),
          addCouponsToCartUseCase: Get.find(),
          deleteAppliedCouponsUseCase: Get.find(),
          postAddressUseCase: Get.find(),
          localStore: Get.find(),
          postGuestEstimate: Get.find(),
          postCheckEmail: Get.find(),
        ));
  }
}
