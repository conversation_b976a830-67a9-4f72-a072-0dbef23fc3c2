// TODO Implement this library.
import '../all_imports.dart';
import 'package:base_controller/controllers/track_your_ticket_mail_controller.dart';
import 'package:app_one/core/consts/app_constants.dart';

import 'package:base_controller/controllers/login_controller.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_apple_usecase.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_email_usecase.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_facebook_usecase.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_google_usecase.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_twitter_usecase.dart';
import 'package:meta_package/api/services/local_store_service.dart';
class TrackYourTicketEmailBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TrackYourTicketEmailController(AppConstants.apiEndPointLogin));

    Get.put(LoginWithGoogle(baseUrl: AppConstants.baseUrlStaging));
    Get.put(LoginWithApple(baseUrl: AppConstants.baseUrlStaging));
    Get.put(LoginWithTwitter(baseUrl: AppConstants.baseUrlStaging));
    Get.put(LoginWithFacebook(baseUrl: AppConstants.baseUrlStaging));
    Get.put(LoginWithEmail(baseUrl: AppConstants.apiEndPointLogin));
    Get.put(LocalStore());

    Get.lazyPut(() => LoginController(
        loginWithGoogleUseCase: Get.find(),
        loginWithAppleUseCase: Get.find(),
        loginWithTwitterUseCase: Get.find(),
        loginWithFacebookUseCase: Get.find(),
        loginWithEmailUseCase: Get.find(),
        localStore: Get.find()),fenix: true);
  }
}
