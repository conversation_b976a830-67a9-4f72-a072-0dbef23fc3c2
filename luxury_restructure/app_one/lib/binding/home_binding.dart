import 'package:app_one/core/consts/app_constants.dart';
import 'package:app_one/main/main.common.dart';
import 'package:domain/usecases/menu_usecase/get_menu_api_search_response_usecase.dart';
import 'package:domain/usecases/product_list_api_usecase/get_product_list_api_response.dart';
import 'package:get/get.dart';
import 'package:base_controller/controllers/home_controller.dart';
import 'package:presentation_common_ui/theme/project_theme_config.dart';

class HomeBindings extends Bindings {
  @override
  void dependencies() {
    // home binding
    Get.lazyPut(() => HomeScreenController(
          localStore: localStore,
          getMenuApiSearchResponseUseCase: Get.find(),
          getProductListApiResponseUseCase: Get.find(),
        ),);

    Get.lazyPut(() =>
        GetProductListApiResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(
        () => GetMenuApiSearchResponse(baseUrl: AppConstants.apiEndPointLogin));

    // Get.lazyPut(() => CartController(Get.find(),Get.find(),Get.find()), fenix: true);
    // Get.lazyPut(() => CartGetDataAPIRepository(
    //     baseUrl: AppConstants.apiEndPointLogin,
    //     cartService: CartService(
    //         RecommendedProductApi(baseUrl: AppConstants.apiEndPointLogin),
    //         CartApi(
    //           baseUrl: AppConstants.apiEndPointLogin,
    //         ))));
    // Get.lazyPut(() => RecommendedProductsAPIRepository(
    //     baseUrl: AppConstants.apiEndPointLogin));
    // Get.lazyPut(
    //     () => WishListAPIRepository(baseUrl: AppConstants.apiEndPointLogin));
    // Get.lazyPut(
    //     () => HomeAPIRepository(baseUrl: AppConstants.apiEndPointLogin));
  }
}
