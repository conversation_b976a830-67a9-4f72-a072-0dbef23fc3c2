import 'package:base_controller/controllers/tems_condition_controller.dart';
import 'package:app_one/core/consts/app_constants.dart';
import 'package:domain/usecases/about_api_usecase/terms_condition_usecase.dart';

import '../all_imports.dart';

class TermAndConditionBinding extends Bindings {
  String baseUrl = AppConstants.apiEndPointLogin;
  @override
  void dependencies() {
    Get.lazyPut(() => TermAndConditionController(
        termsCondition: TermsCondition(baseUrl: baseUrl)));
  }
}
