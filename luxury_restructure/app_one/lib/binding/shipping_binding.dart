import 'package:domain/usecases/shipping_usecase/get_shipping_api_response.dart';
import '../all_imports.dart';
import 'package:base_controller/controllers/shipping_controller.dart';
import '../core/consts/app_constants.dart';

class ShippingBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ShippingController(
        getShippingApiResponseUseCase:
            GetShippingApiResponse(baseUrl: AppConstants.apiEndPointLogin)));
  }
}
