import 'package:base_controller/controllers/affiliate_program_controller.dart';
import 'package:app_one/core/consts/app_constants.dart';
import 'package:domain/usecases/affiliate_api_usecase/get_affiliate_usecase.dart';
import 'package:get/get.dart';

class AffiliateProgramBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => AffiliateProgramController(
        getAffiliateAPIResponse:
            GetAffiliate(baseUrl: AppConstants.apiEndPointLogin)));
  }
}
