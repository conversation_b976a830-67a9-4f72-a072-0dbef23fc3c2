import 'package:base_controller/controllers/order_details_controller.dart';
import 'package:domain/usecases/my_orders_usecase/get_cancel_reason_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/get_return_reason_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/post_cancellation_reason_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/post_return_order_usecase.dart';
import 'package:domain/usecases/my_ticket_usecase/post_create_my_tickets.dart';
import 'package:domain/usecases/order_confirmation_api_usecase/get_order_tracking_response.dart';
import 'package:get/get.dart';
import 'package:meta_package/api/api_repository/my_ticket_api_repository.dart';
import 'package:meta_package/api/services/ticket_service.dart';

import '../core/consts/app_constants.dart';

class OrderDetailsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => OrderDetailsController(
      getCancelReasonResponse:
      GetCancelReasonResponse(baseUrl: AppConstants.apiEndPointLogin),
      getReturnReasonResponse: GetReturnReasonResponse(baseUrl: AppConstants.apiEndPointLogin),
      postReturnOrderApi: PostReturnOrder(baseUrl: AppConstants.apiEndPointLogin),
      postCancellationReasonResponse: PostCancellationReasonResponse(baseUrl: AppConstants.apiEndPointLogin),
      postCreateMyTickets: PostCreateMyTickets(
          myTicketAPIRepository: MyTicketAPIRepository(ticketService: TicketService(AppConstants.apiEndPointLogin)),
          baseUrl: AppConstants.apiEndPointLogin,
          ticketService: TicketService(AppConstants.apiEndPointLogin)),
      getOrderTrackingApiResponse: GetOrderTrackingApiResponse(baseUrl: AppConstants.apiEndPointLogin),));
  }
}
