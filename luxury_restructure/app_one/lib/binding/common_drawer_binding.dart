import 'package:app_one/core/consts/constants.dart';
import 'package:get/get.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/common_drawer_controller.dart';
import 'package:domain/usecases/menu_usecase/get_menu_api_response_usecase.dart';

class CommonDrawerBinding extends Bindings{
  @override
  void dependencies() {
    Get.lazyPut(()=>CommonDrawerController(getMenuApiResponseUseCase: Get.find()));
    Get.lazyPut(() => GetMenuApiResponse(baseUrl: AppConstants.apiEndPointLogin));
  }

}