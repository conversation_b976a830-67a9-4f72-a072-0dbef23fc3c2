import 'package:domain/usecases/influencer_usecase/get_influencer_api_response_usecase.dart';
import '../all_imports.dart';
import 'package:base_controller/controllers/influencer_registration_controller.dart';

class InfluencerRegistrationBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => InfluencerRegistrationController(
        getInfluencerApiResponse: GetInfluencerApiResponse()));
  }
}
