import 'package:app_one/core/consts/app_constants.dart';
import 'package:domain/usecases/address_api_usecase/get_country_list_usecase.dart';
import 'package:get/get.dart';
import 'package:meta_package/api/services/add_address_service.dart';
import 'package:base_controller/controllers/select_country_controller.dart';

class SelectCountryBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => GetCountryList(
        addAddressService: AddAddressService(AppConstants.apiEndPointLogin)));
    Get.lazyPut(
        () => SelectCountryController(getCountryListUseCase: Get.find()));
  }
}
