import 'package:app_one/core/consts/app_constants.dart';
import 'package:domain/usecases/my_account_usecase/get_account_detail_response_usecase.dart';
import 'package:domain/usecases/store_api_usecase/get_store_configs_api_response.dart';
import 'package:domain/usecases/store_api_usecase/get_store_websites_api_response.dart';
import 'package:domain/usecases/update_lang_api_usecase/post_device_info_api_response.dart';
import 'package:get/get.dart';
import 'package:meta_package/api/services/local_store_service.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/my_account_controller.dart';
import 'package:base_controller/controllers/country_controllers.dart';

class CountryScreenBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(
        () => GetAccountDetailResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(
        () => MyAccountController(getAccountDetailResponse: Get.find()));
    Get.put(() => LocalStore());
    Get.put(
        GetStoreWebsitesApiResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.put(GetStoreConfigsApiResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.put(PostDeviceInfoApiResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => CountryController(
        getStoreWebsitesApiResponse: Get.find(),
        getStoreConfigsApiResponse: Get.find(),
        postDeviceInfoApiResponse: Get.find(),
        localStore: Get.find()));
  }
}
