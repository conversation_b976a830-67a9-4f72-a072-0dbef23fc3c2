import 'package:app_one/core/consts/app_constants.dart';
import 'package:app_one/main/main.common.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_apple_usecase.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_email_usecase.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_facebook_usecase.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_google_usecase.dart';
import 'package:domain/usecases/auth_api_usecase/login_with_twitter_usecase.dart';
import 'package:get/get.dart';
import 'package:base_controller/controllers/login_controller.dart';

class LoginScreenBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(LoginWithGoogle(baseUrl: AppConstants.baseUrlStaging));
    Get.put(LoginWithApple(baseUrl: AppConstants.baseUrlStaging));
    Get.put(LoginWithTwitter(baseUrl: AppConstants.baseUrlStaging));
    Get.put(LoginWithFacebook(baseUrl: AppConstants.baseUrlStaging));
    Get.put(LoginWithEmail(baseUrl: AppConstants.baseUrlStaging));

    Get.lazyPut(() => LoginController(
        loginWithGoogleUseCase: Get.find(),
        loginWithAppleUseCase: Get.find(),
        loginWithTwitterUseCase: Get.find(),
        loginWithFacebookUseCase: Get.find(),
        loginWithEmailUseCase: Get.find(),
        localStore: localStore));
  }
}
