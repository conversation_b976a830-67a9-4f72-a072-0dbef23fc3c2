import 'package:base_controller/controllers/my_account_information_controller.dart';
import 'package:app_one/core/consts/constants.dart';
import 'package:domain/usecases/my_account_usecase/get_account_detail_response_usecase.dart';

import '../all_imports.dart';import 'package:domain/usecases/my_account_usecase/update_user_usecase.dart';


class MyAccountInfoScreenBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(GetAccountDetailResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => MyAccountInformationController(
      getAccountDetailResponse: Get.find(),
      updateUserUseCase: UpdateUser(baseUrl: AppConstants.apiEndPointLogin),
    ));
  }
}
