import 'package:app_one/core/consts/constants.dart';
import 'package:domain/usecases/brand_list_api_usecase/get_brand_list_usecase.dart';
import 'package:get/get.dart';
import 'package:presentation/navigation/navigation.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/common_designer_page_controller.dart';



class DesignerPageBinding extends Bindings {
  @override
  void dependencies() {

    Get.put( GetBrandList(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => CommonDesignerPageController(getBrandListUseCase: Get.find()));



    Get.lazyPut(() => Navigation());
  }
}
