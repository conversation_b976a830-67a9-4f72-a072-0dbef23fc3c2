import 'package:app_one/core/consts/app_constants.dart';
import 'package:domain/usecases/auth_api_usecase/put_forget_password_usecase.dart';
import 'package:get/get.dart';
import 'package:base_controller/controllers/forgot_password_controller.dart';
import 'package:meta_package/api/api_repository/auth_api_repository.dart';

class ForgotpasswordBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(
      () => CommonForgotPasswordController(
        putForgetPassword: PutForgetPassword(
          baseUrl: AppConstants.apiEndPointLogin,
          authApiRepository: AuthAPIRepository(baseUrl: AppConstants.apiEndPointLogin),
        ),
      ),
    );
  }
}
