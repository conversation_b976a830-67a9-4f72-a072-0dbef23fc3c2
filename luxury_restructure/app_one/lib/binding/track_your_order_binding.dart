import 'package:domain/usecases/track_your_order_usecase/get_track_your_order_api_response.dart';

import '../all_imports.dart';
import 'package:base_controller/controllers/track_your_order_controller.dart';
import '../core/consts/app_constants.dart';

class TrackYourOrderBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(
        GetTrackYourOrderApiResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() =>
        TrackYourOrderController(getTrackYourOrderApiResponse: Get.find()));
  }
}
