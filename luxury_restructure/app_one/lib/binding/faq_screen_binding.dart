import 'package:domain/usecases/about_api_usecase/get_faq_usecase.dart';
import 'package:get/get.dart';
import 'package:app_four/core/const/app_constants.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/common_faq_screen_controller.dart';

class FaqBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => CommonFaqScreenController(faqUseCase: Get.find()));
    Get.lazyPut(() => Faq(
        baseUrl:
            AppConstants.apiEndPointLogin)); //AppConstants.apiEndPointLogin
  }
}
