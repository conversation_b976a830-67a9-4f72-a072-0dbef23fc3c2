import 'package:domain/usecases/address_api_usecase/add_new_address_usecase.dart';
import 'package:domain/usecases/address_api_usecase/get_country_list_usecase.dart';
import 'package:domain/usecases/address_api_usecase/update_address_usecase.dart';
import 'package:domain/usecases/my_account_usecase/get_account_detail_response_usecase.dart';
import 'package:domain/usecases/store_api_usecase/get_store_configs_api_response.dart';
import 'package:domain/usecases/store_api_usecase/get_store_websites_api_response.dart';
import 'package:domain/usecases/update_lang_api_usecase/post_device_info_api_response.dart';
import 'package:get/get.dart';
import 'package:meta_package/api/services/add_address_service.dart';
import 'package:meta_package/api/services/local_store_service.dart';
import 'package:meta_package/constants/key_value_constants.dart';
import 'package:presentation_common_ui/common.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/my_account_controller.dart';
import 'package:base_controller/controllers/add_address_controller.dart';
import 'package:base_controller/controllers/country_controllers.dart';
import '../core/consts/app_constants.dart';
import 'package:base_controller/controllers/country_dropdown_controllers.dart';

class AddAddressBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => GetAccountDetailResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => MyAccountController(getAccountDetailResponse: Get.find()));
    Get.put(() => LocalStore());
    Get.put(GetStoreWebsitesApiResponse(baseUrl: AppConstants.baseUrlStaging));
    Get.put(GetStoreConfigsApiResponse(baseUrl: AppConstants.baseUrlStaging));
    Get.put(PostDeviceInfoApiResponse(baseUrl: AppConstants.baseUrlStaging));
    Get.put(CountryController(
        countryCode: LocalStore.getPrefStringValue(kLocalStoreCountryCodeKey),
        getStoreWebsitesApiResponse: Get.find(),
        getStoreConfigsApiResponse: Get.find(),
        postDeviceInfoApiResponse: Get.find(),
        localStore: Get.find()));
    // Get.lazyPut(() => CountryController(
    //     countryCode: LocalStore.getPrefStringValue(kLocalStoreCountryCodeKey),
    //     getStoreWebsitesApiResponse: Get.find(),
    //     getStoreConfigsApiResponse: Get.find(),
    //     postDeviceInfoApiResponse: Get.find(),
    //     localStore: Get.find()));
    // Get.put(LocalStore());
    Get.lazyPut(() => LocalStore());
    Get.put(LocalStore());
    Get.lazyPut(
        () => GetCountryList(addAddressService: AddAddressService(AppConstants.apiEndPointLogin)));
    Get.lazyPut(
        () => AddNewAddress(addAddressService: AddAddressService(AppConstants.apiEndPointLogin)));
    Get.lazyPut(
        () => UpdateAddress(addAddressService: AddAddressService(AppConstants.apiEndPointLogin)));
    Get.put(AddAddressController(
        countryController: CountryController(
            countryCode: LocalStore.getPrefStringValue(kLocalStoreCountryCodeKey),
            getStoreWebsitesApiResponse: Get.find(),
            getStoreConfigsApiResponse: Get.find(),
            postDeviceInfoApiResponse: Get.find(),
            localStore: Get.find()),
        getCountryListUseCase: Get.find(),
        addNewAddressUseCase: Get.find(),
        updateAddressUseCase: Get.find()));
    Get.put(CountryDropDownController(
      getCountryListUseCase: Get.find(),
    ));
  }
}
