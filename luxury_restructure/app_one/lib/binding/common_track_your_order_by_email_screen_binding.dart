import 'package:app_one/core/consts/constants.dart';
import 'package:get/get.dart';
import 'package:meta_package/api/services/local_store_service.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/track_your_order_by_email_controller.dart';
import 'package:domain/usecases/track_your_order_usecase/get_track_your_order_api_response.dart';

class CommonTrackYourOrderByEmailScreenBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TrackYourOrderByEmailController(
        localStore: Get.find(),
        getTrackYourOrderApiResponse: Get.find()));
    
    Get.lazyPut(() => LocalStore());

    Get.lazyPut(() => GetTrackYourOrderApiResponse(baseUrl: AppConstants.apiEndPointLogin));
  }
}
