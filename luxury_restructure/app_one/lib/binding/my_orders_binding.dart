import 'package:app_one/core/consts/app_constants.dart';
import 'package:domain/usecases/my_orders_usecase/get_cancel_reason_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/get_my_orders_api_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/get_return_reason_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/post_cancellation_reason_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/post_item_cancellation_reason_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/post_item_return_reason_response.dart';
import 'package:domain/usecases/my_orders_usecase/post_return_order_usecase.dart';
import 'package:domain/usecases/my_ticket_usecase/post_create_my_tickets.dart';
import 'package:domain/usecases/order_confirmation_api_usecase/get_order_tracking_response.dart';
import 'package:meta_package/api/api_repository/my_ticket_api_repository.dart';
import 'package:meta_package/api/services/ticket_service.dart';
import 'package:meta_package/api/services/local_store_service.dart';
import '../all_imports.dart';
import 'package:base_controller/controllers/my_orders_controller.dart';

class MyOrdersBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(
        () => MyOrdersController(
            getOrderTrackingApiResponse: GetOrderTrackingApiResponse(baseUrl: AppConstants.apiEndPointLogin),
            localStore: LocalStore(),
            getCancelReasonResponse:
                GetCancelReasonResponse(baseUrl: AppConstants.apiEndPointLogin),
            getReturnReasonResponse:
                GetReturnReasonResponse(baseUrl: AppConstants.apiEndPointLogin),
            getMyOrdersApiResponse:
                GetMyOrdersApiResponse(baseUrl: AppConstants.apiEndPointLogin),
            postCancellationReasonResponse: PostCancellationReasonResponse(
                baseUrl: AppConstants.apiEndPointLogin),
            postItemCancellationReasonResponse:
                PostItemCancellationReasonResponse(
                    baseUrl: AppConstants.apiEndPointLogin),
            postReturnOrderApi:
                PostReturnOrder(baseUrl: AppConstants.apiEndPointLogin),
            postItemReturnReasonResponse: PostItemReturnReasonResponse(
                baseUrl: AppConstants.apiEndPointLogin),
            postCreateMyTickets: PostCreateMyTickets(
                myTicketAPIRepository: MyTicketAPIRepository(
                    ticketService:
                        TicketService(AppConstants.apiEndPointLogin)),
                baseUrl: AppConstants.apiEndPointLogin,
                ticketService: TicketService(AppConstants.apiEndPointLogin))),
        fenix: true);
  }
}
