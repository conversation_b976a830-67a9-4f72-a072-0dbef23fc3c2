// import 'package:base_controller/controllers/contact_us/app_one_contact_us_controller.dart';
import 'package:app_one/core/consts/constants.dart';
import 'package:app_one/main/main.common.dart';
import 'package:domain/usecases/about_api_usecase/contact_us_api_data_usecase.dart';
import 'package:domain/usecases/about_api_usecase/get_contact_us_usecase.dart';
import 'package:domain/usecases/store_api_usecase/get_store_configs_api_response.dart';
import 'package:domain/usecases/store_api_usecase/get_store_websites_api_response.dart';
import 'package:domain/usecases/update_lang_api_usecase/post_device_info_api_response.dart';
import 'package:get/get.dart';
import 'package:meta_package/api/services/local_store_service.dart';
// import '../../../base_controller/lib/controllers/contact_us/app_one_contact_us_controller.dart';
import 'package:base_controller/controllers/country_controllers.dart';
import 'package:base_controller/controllers/contact_us_controller.dart';
import 'package:meta_package/api/services/ticket_service.dart';
import 'package:meta_package/api/api_repository/my_ticket_api_repository.dart';


class ContactUsScreenBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(() => LocalStore());
    Get.put(GetStoreWebsitesApiResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.put(GetStoreConfigsApiResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.put(PostDeviceInfoApiResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => CountryController(
        getStoreWebsitesApiResponse: Get.find(),
        getStoreConfigsApiResponse: Get.find(),
        postDeviceInfoApiResponse: Get.find(),
        localStore: Get.find()));
    // Get.lazyPut(() => ContactUsController(
    //     contactUs: Get.find(), countryController: Get.find(), contactUsApiData: Get.find(), localStore: LocalStore()));
    Get.lazyPut(() => ContactUsController(
        contactUs: Get.find(),
        countryController: Get.find(),
        contactUsApiData: Get.find(), myTicketAPIRepository: MyTicketAPIRepository(
        ticketService: TicketService(AppConstants.apiEndPointLogin))));
    Get.put(ContactUs(baseUrl: AppConstants.apiEndPointLogin));
    Get.put(ContactUsApiData(baseUrl: AppConstants.apiEndPointLogin));
     }
}
