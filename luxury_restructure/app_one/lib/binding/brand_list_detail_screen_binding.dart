import 'package:app_one/core/consts/app_constants.dart';
import 'package:domain/usecases/brand_list_api_usecase/get_brand_list_usecase.dart';
import 'package:get/get.dart';
import 'package:meta_package/meta_package.dart';
import 'package:presentation/navigation/navigation.dart';

import 'package:base_controller/controllers/brand_list_screen_controller.dart';

class BrandListScreenBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => BrandListScreenController(
        groupHelper: Get.find(), getBrandListUseCase: Get.find()));
    Get.lazyPut(() => GetBrandList(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => BrandGroupingHelper());

    // Only For Checking Remove
    Get.lazyPut<Navigation>(() => Navigation());
  }
}
