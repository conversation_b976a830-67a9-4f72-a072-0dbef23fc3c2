import 'package:app_one/core/consts/app_constants.dart';
import 'package:app_one/main/main.common.dart';
import 'package:domain/usecases/store_api_usecase/get_store_credit_response.dart';

import '../all_imports.dart';
import 'package:base_controller/controllers/store_credit_controller.dart';

class StoreCreditBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => StoreCreditController(
        getStoreCreditsApiResponse:
            GetStoreCreditsApiResponse(baseUrl: AppConstants.baseUrlStaging),
        localStore: localStore));
  }
}
