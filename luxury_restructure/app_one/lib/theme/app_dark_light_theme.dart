// // ignore_for_file: deprecated_member_use

// import 'package:app_one/core/consts/app_constants.dart';
// import 'package:app_one/theme/colors.dart';
// import 'package:flutter/material.dart';

// final darkTheme = ThemeData(
//   // primaryColor: whiteColor,
//   backgroundColor: primaryBlack,
//   focusColor: primaryWhite,
//   scaffoldBackgroundColor: primaryBlack,
//   fontFamily: AppConstants.fontPoppins,
//   hintColor: regularGrey,
//   canvasColor: Colors.white,
//   iconTheme: const IconThemeData(
//     color: regularGrey,
//     size: 24,
//   ),
//   textTheme: const TextTheme(
//       titleLarge: TextStyle(color: Colors.white),
//       displayMedium: TextStyle(color: Colors.white),
//       titleSmall: TextStyle(color: Colors.black)),
//   appBarTheme: const AppBarTheme(
//     elevation: 1,
//     iconTheme: IconThemeData(color: whiteColor),
//     backgroundColor: primaryBlack,
//     foregroundColor: whiteColor,
//     centerTitle: true,
//   ),
//   primaryColorDark: whiteColor,
//   checkboxTheme: CheckboxThemeData(
//     fillColor: MaterialStateProperty.all(
//       regularGrey,
//     ),
//   ),
//   colorScheme: ColorScheme.fromSwatch().copyWith(primary: Colors.white),
// );

// final lightTheme = ThemeData(
//   primaryColor: darkBlue,
//   canvasColor: buttonColor,
//   backgroundColor: primaryWhite,
//   focusColor: primaryBlack,
//   scaffoldBackgroundColor: primaryWhite,
//   fontFamily: AppConstants.fontPoppins,
//   hintColor: regularGrey,
//   textTheme: const TextTheme(
//       titleLarge: TextStyle(color: blackColor),
//       displayMedium: TextStyle(color: blackColor),
//       titleSmall: TextStyle(color: Colors.white)),
//   iconTheme: const IconThemeData(
//     color: regularGrey,
//     size: 24,
//   ),
//   appBarTheme: const AppBarTheme(
//     elevation: 1,
//     // ignore: deprecated_member_use
//     backgroundColor: primaryWhite,
//     foregroundColor: titleBlack,
//     centerTitle: true,
//   ),
//   primaryColorDark: regularGrey,
//   checkboxTheme: CheckboxThemeData(
//     fillColor: MaterialStateProperty.all(
//       regularGrey,
//     ),
//   ),
//   colorScheme: ColorScheme.fromSwatch().copyWith(primary: darkBlue),
// );
