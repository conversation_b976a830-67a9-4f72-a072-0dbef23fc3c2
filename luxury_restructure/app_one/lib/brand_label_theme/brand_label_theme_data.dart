// // ignore_for_file: deprecated_member_use

// import 'package:app_one/app_one_theme/colors.dart';
// import 'package:flutter/material.dart';
// import 'package:presentation/app_constants/constants.dart';

// final darkTheme = ThemeData(
//   primaryColor: whiteColor,
//   backgroundColor: primaryBlack,
//   focusColor: primaryWhite,
//   scaffoldBackgroundColor: primaryBlack,
//   fontFamily: Constants.fontPoppins,
//   hintColor: regularGrey,
//   canvasColor: Colors.white,
//   iconTheme: const IconThemeData(
//     color: regularGrey,
//     size: 24,
//   ),
//   textTheme: const TextTheme(
//     titleLarge: TextStyle(color: Colors.white),
//     displayMedium: TextStyle(color: Colors.white),
//   ),
//   appBarTheme: AppBarTheme(
//     elevation: 1,
//     iconTheme: const IconThemeData(color: whiteColor),
//     backgroundColor: primaryBlack,
//     foregroundColor: whiteColor,
//     centerTitle: true,
//     toolbarTextStyle: const TextTheme(
//       titleLarge: TextStyle(color: Colors.white),
//     ).bodyMedium,
//     titleTextStyle: const TextTheme(
//       titleLarge: TextStyle(color: Colors.white),
//     ).titleLarge,
//   ),
//   primaryColorDark: whiteColor,
//   checkboxTheme: CheckboxThemeData(
//     fillColor: MaterialStateProperty.all(
//       regularGrey,
//     ),
//   ),
// );

// final lightTheme = ThemeData(
//   primaryColor: primary,
//   canvasColor: buttonColor1,
//   backgroundColor: primaryWhite,
//   focusColor: primaryBlack,
//   scaffoldBackgroundColor: primaryWhite,
//   fontFamily: Constants.fontPoppins,
//   hintColor: regularGrey,
//   textTheme: const TextTheme(displayMedium: TextStyle(color: darkBlue)),
//   iconTheme: const IconThemeData(
//     color: regularGrey,
//     size: 24,
//   ),
//   appBarTheme: AppBarTheme(
//     elevation: 1,
//     backgroundColor: primaryWhite,
//     foregroundColor: titleBlack,
//     centerTitle: true,
//     toolbarTextStyle:
//         const TextTheme(titleLarge: TextStyle(color: Colors.white)).bodyMedium,
//     titleTextStyle:
//         const TextTheme(titleLarge: TextStyle(color: Colors.white)).titleLarge,
//   ),
//   primaryColorDark: regularGrey,
//   checkboxTheme: CheckboxThemeData(
//     fillColor: MaterialStateProperty.all(
//       regularGrey,
//     ),
//   ),
// );
