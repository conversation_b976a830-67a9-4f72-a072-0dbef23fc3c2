// import 'package:app_one/app_one_theme/theme.dart';
// import 'package:flutter/material.dart';

// enum ThemeType { light, dark }

// abstract class ThemeOptions {
//   ThemeData getTheme({required ThemeType themeType});

//   void setTheme({required ThemeData themeData, required ThemeType themeType});
// }

// class ThemeFactory implements ThemeOptions {
//   final brandLabelLightTheme = BrandLabelLightTheme();
//   final brandLabelDarkTheme = BrandLabelDarkTheme();

//   @override
//   ThemeData getTheme({required ThemeType themeType}) {
//     if (themeType == ThemeType.light) {
//       return brandLabelLightTheme.getLightTheme;
//     } else {
//       return brandLabelDarkTheme.getDarkTheme;
//     }
//   }

//   @override
//   void setTheme({required ThemeData themeData, required ThemeType themeType}) {
//     if (themeType == ThemeType.light) {
//       brandLabelLightTheme.setLightTheme = themeData;
//     } else {
//       brandLabelDarkTheme.setDarkTheme = themeData;
//     }
//   }
// }
