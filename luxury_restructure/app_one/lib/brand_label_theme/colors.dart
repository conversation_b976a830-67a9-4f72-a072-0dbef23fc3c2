// import 'package:flutter/material.dart';

// const Color appColor = Color(0xff713213);
// const Color backGroundColor = Color(0xffF7E8E1);
// const Color secondaryColor = Color(0xffEFD8CF);
// const Color offWhite = Color(0xffFDF4F0);
// const Color darkGrey = Color(0xff181818);
// const Color darkBlue = Color(0xff000080);
// const Color lightGrey = Color(0xff5F5959);
// const Color silver = Color(0xff717171);
// const Color brownColor = Color(0xff8A3A07);
// const Color lightBrownColor = Color(0xffECDAD4);
// const Color profileTileBG = Color(0xffFAFAFA);
// const Color buttonColor1 = Color(0xff2D2F77);
// const Color primary = Color(0xff2D2F77);
// const Color blueTileColor = Color(0xffefefff);

// const Color regularEDEDED = Color(0xffEDEDED);
// const Color regularF5F5F5 = Color(0xffF5F5F5);

// const Color darkBlue2A2B9B = Color(0xff2A2B9B);

// const Color whiteColor = Color(0xffFFFFFF);
// const Color blackColor = Color(0xff000000);
// Color shoppingcartGrey = const Color(0xffF5F5F5);
// Color cartblueColor = const Color(0xff000080);

// const Color backgroundGrey = Color(0xffECEFF3);

// const Color hintGrey = Color(0xffBABABA);
// const Color contentGrey = Color(0xff969498);
// const Color regularGrey = Color(0xff7F7D89);
// const Color grey636363 = Color(0xff636363);
// const Color grey969290 = Color(0xff969290);
// const Color grey868686 = Color(0xff868686);
// const Color grey6D6D6D = Color(0xff6D6D6D);
// const Color darkGreyBlue = Color(0xff2B2C36); //505050
// const Color titleBlack = Color(0xff333333);
// const Color lightappColor = Color(0xffFFE7E7);
// const Color red = Color(0xffE22A2A);
// const Color redF0F0F0 = Color(0xffF0F0F0);
// const Color success = Color(0xff5FB924);
// const Color infoDialog = Color(0xff79B3E4);
// const Color blue = Color(0xff068AEC);
// const Color yellow = Color(0xffFFCC00);
// const Color borderGrey = Color(0xffDBDBDB);
// const Color textfieldBorderGrey = Color(0xffC8C8C8);

// const Color filterBackground = Color(0xFFF6F6F6);

// const Color borderBlue = Color(0xFF000080);
// const Color appBarPrimary = primary;
// const Color appButtonColor = Color(0xff000080);
// const Color appDividerColor = Color(0xff838383);
// const Color appRadioColor = Color(0xff797A78);
// const Color buttonColor = Color(0xff000080);
// const Color dividerColor = Color(0xff7C7474);
// const Color titleColor = Color(0xff000080);

// Color lightSilver = const Color(0xffF7F7F7);
// Color darkSilver = const Color(0xffE4E4E4);
// Color grey = const Color(0xff999999);

// const MaterialColor primaryBlack = MaterialColor(
//   _blackPrimaryValue,
//   <int, Color>{
//     50: Color(0xFF000000),
//     100: Color(0xFF000000),
//     200: Color(0xFF000000),
//     300: Color(0xFF000000),
//     400: Color(0xFF000000),
//     500: Color(_blackPrimaryValue),
//     600: Color(0xFF000000),
//     700: Color(0xFF000000),
//     800: Color(0xFF000000),
//     900: Color(0xFF000000),
//   },
// );
// const int _blackPrimaryValue = 0xFF000000;

// const MaterialColor primaryWhite = MaterialColor(
//   _whitePrimaryValue,
//   <int, Color>{
//     50: Color(0xFFFFFFFF),
//     100: Color(0xFFFFFFFF),
//     200: Color(0xFFFFFFFF),
//     300: Color(0xFFFFFFFF),
//     400: Color(0xFFFFFFFF),
//     500: Color(_whitePrimaryValue),
//     600: Color(0xFFFFFFFF),
//     700: Color(0xFFFFFFFF),
//     800: Color(0xFFFFFFFF),
//     900: Color(0xFFFFFFFF),
//   },
// );
// const int _whitePrimaryValue = 0xFFFFFFFF;
