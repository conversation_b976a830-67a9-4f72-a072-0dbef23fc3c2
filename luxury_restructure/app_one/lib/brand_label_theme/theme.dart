// import 'package:flutter/material.dart';

// // light theme.
// class BrandLabelLightTheme {
//   ThemeData _lightTheme = ThemeData();

//   ThemeData get getLightTheme => _lightTheme;

//   set setLightTheme(ThemeData themeData) {
//     _lightTheme = themeData;
//   }
// }

// // dark theme.

// class BrandLabelDarkTheme {
//   ThemeData _darkTheme = ThemeData();

//   ThemeData get getDarkTheme => _darkTheme;

//   set setDarkTheme(ThemeData themeData) {
//     _darkTheme = themeData;
//   }
// }
