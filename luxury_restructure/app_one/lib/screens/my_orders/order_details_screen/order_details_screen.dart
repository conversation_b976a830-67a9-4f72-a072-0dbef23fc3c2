// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:app_one/core/consts/app_style.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';
import 'package:meta_package/api/models/my_orders/my_orders_data.dart';

import 'package:intl/date_symbol_data_local.dart';
import 'package:presentation/const/app_text_style/presentation_app_text_style.dart';
import 'package:presentation/const/size_utils.dart';
import 'package:presentation/widgets/base_controller_widgets/controller_helper.dart';
import 'package:presentation/widgets/common_buttons/common_theme_button.dart';
import 'package:presentation_common_ui/common_screens/common_my_order_screen/common_order_details_screen.dart';

import '../../../all_imports.dart';
import '../../../app_pages/app_routes.dart';
import 'package:base_controller/controllers/order_details_controller.dart';
import '../../../theme/colors.dart';

class OrderDetailsScreen extends GetView<OrderDetailsController> {
  const OrderDetailsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    MyOrdersDataItem? argumentData12 = Get.arguments[1] as MyOrdersDataItem;

    List<ParentItemElement> argumentData = argumentData12.items ?? [];
    String locale = Get.locale?.languageCode ?? 'en';
    initializeDateFormatting(locale, null);

    return CommonOrderDetailScreen(
      argumentData: argumentData,
      argumentData12: argumentData12,
    );
  }

//
}

class BillTileWidget extends StatelessWidget {
  final String title;
  final Widget child;
  const BillTileWidget({
    Key? key,
    required this.title,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration:
          BoxDecoration(borderRadius: BorderRadius.circular(12), border: Border.all(color: borderGreyColor, width: 2)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: 10.iX, right: 10.iX),
            height: 40.iY,
            width: double.infinity,
            decoration: const BoxDecoration(
                color: tileBackgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(11),
                  topRight: Radius.circular(11),
                )),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                title,
                style: AppStyle.textStyleUtils500(size: 14.iY, color: primaryColor),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.all(15.iX),
            child: Align(
              alignment: Alignment.centerLeft,
              child: child,
            ),
          )
        ],
      ),
    );
  }
}
