import 'package:meta_package/utils/validator.dart';
import 'package:presentation/widgets/common_text_fields/input_text_field_widget.dart';

import '../../../../../../all_imports.dart';
import 'package:base_controller/controllers/my_orders_controller.dart';
import 'package:presentation_common_ui/widgets/login_screen/app_one/login_email_text_field.dart';

class NameTextField extends GetView<MyOrdersController> {
  const NameTextField({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormFieldWidget(
      controller: controller.firstNameController.value,
      hintText: LanguageConstants.nameChatText.tr,
      textAlign: TextAlign.center,
      validator: (value) => Validators.validateRequired(value?.trim() ?? '', "Name"),
    );
  }
}

class EmailTextField extends GetView<MyOrdersController> {
  const EmailTextField({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EmailWidget(
      controller: controller.emailController.value,
      hintText: LanguageConstants.emailText.tr,
      textAlign: TextAlign.center,
      validator: (value) => Validators.validateEmail(
        value?.trim(),
      ),
    );
  }
}
