import 'package:app_one/main/main.common.dart';
import 'package:domain/usecases/my_orders_usecase/get_my_orders_api_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/get_return_reason_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/post_cancellation_reason_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/post_item_cancellation_reason_response_usecase.dart';
import 'package:domain/usecases/my_orders_usecase/post_item_return_reason_response.dart';
import 'package:domain/usecases/my_orders_usecase/post_return_order_usecase.dart';
import 'package:domain/usecases/my_ticket_usecase/post_create_my_tickets.dart';
import 'package:presentation/widgets/common_alert_dialog/common_alert_dialog.dart';
import '../../all_imports.dart';
import '../../app_pages/app_routes.dart';
import 'package:base_controller/controllers/my_orders_controller.dart';
// import '../../theme/app_asset.dart';
import 'order_details_screen/widget/order_textfileds.dart';
import 'package:meta_package/api/services/ticket_service.dart';
import 'order_details_screen/widget/start_chat_button.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';
import 'package:presentation_common_ui/common_ui/common_my_order_screen/common_my_order_screen.dart';


class MyOrdersScreen extends GetView<MyOrdersController> {
  const MyOrdersScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    Get.lazyPut(()=>GetReturnReasonResponse(baseUrl: ''));
    Get.lazyPut(()=>GetMyOrdersApiResponse(baseUrl: ''));
    Get.lazyPut(()=>PostCancellationReasonResponse(baseUrl: ''));
    Get.lazyPut(()=>PostItemCancellationReasonResponse(baseUrl: ''));
    Get.lazyPut(()=>PostReturnOrder(baseUrl: ''));
    Get.lazyPut(()=>PostItemReturnReasonResponse(baseUrl: ''));
    Get.lazyPut(()=>PostCreateMyTickets(baseUrl: '', ticketService: TicketService("")));

    return Obx(
      () => CommonMyOrderScreen(
          scaffoldKey: controller.scaffoldKey.value,
          formKey: controller.formKey,
          isLoading: controller.isLoading.value /*false*/,
          liveChatIcon: AppAsset.plus,
          nothingToShowIcon: AppAsset.nothigToShowAnimation,
          buttonColor: Theme.of(context).primaryColor,
          errorImagePath: AppAsset.logo,

          myOrderTitleTextStyle: AppStyle.textStyleUtils500(size: 36,color: Theme.of(context).textTheme.titleLarge?.color),
          orderIdTextStyle: AppStyle.textStyleUtils500(size: 16,color: Theme.of(context).textTheme.titleLarge?.color?.withOpacity(0.5)),
          orderDateTextStyle: AppStyle.textStyleUtils500(size: 14,color: Theme.of(context).textTheme.titleLarge?.color?.withOpacity(0.5)),
          itemNameTextStyle: AppStyle.textStyleUtils500(size: 16,color: Theme.of(context).textTheme.titleLarge?.color),
          skuTextStyle: AppStyle.textStyleUtils500(size: 12,color: Theme.of(context).textTheme.titleLarge?.color?.withOpacity(0.5)),
          colorAnSizeTextStyle: AppStyle.textStyleUtils500(size: 14,color: Theme.of(context).textTheme.titleLarge?.color?.withOpacity(0.4)),
          priceTextStyle: AppStyle.textStyleUtils500(size: 14,color: Theme.of(context).textTheme.titleLarge?.color),
          orderStatusTextStyle: AppStyle.textStyleUtils700(size: 14,color: Theme.of(context).primaryColor),
          buttonTextStyle: AppStyle.textStyleUtils700(size: 14,color: Colors.white),
          buttonBorderRadius: 50.0,
          productContainerPadding: const EdgeInsetsDirectional.only(start: 30,bottom: 24,end: 50),
          onClickLiveChatIcon: (){
            if (localStore.customerToken.toString() == "") {
              showTitleDialog(context);
            } else {
              controller.clickChatEvent();
            }
          },
          onClickContinueShopping: (){
            Get.offAndToNamed<dynamic>(RoutesConstants.dashboardScreen);
          }),
    );
  }

  Future showTitleDialog(BuildContext context) {
    return showDialog<dynamic>(
        context: context,
        builder: (BuildContext context) {
          return CommonAlertDialog(
            backgroundColor: backgroundColor,
            insetPadding: const EdgeInsets.all(10),
            contentWidget: Stack(
              clipBehavior: Clip.none,
              children: <Widget>[
                Positioned(
                  left: 0,
                  right: 0,
                  top: -45.0,
                  child: CircleAvatar(
                    radius: 45,
                    backgroundColor: appColor,
                    child: Image.asset(
                      AppAsset.account,
                      color: Colors.white,
                      width: 40,
                      height: 40,
                    ),
                  ),
                ),
                Form(
                  key: controller.formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Container(
                        //height: 70,
                        margin: const EdgeInsets.only(top: 60),
                        width: MediaQuery.of(context).size.width,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Center(
                                child: Text(LanguageConstants.welcometoChatText.tr,
                                    textAlign: TextAlign.center, style: AppStyle.textStyleUtils400_16())),
                            const SizedBox(
                              height: 5,
                            ),
                            Center(
                                child: Text(LanguageConstants.fillTheFormText.tr,
                                    textAlign: TextAlign.center, style: AppStyle.textStyleUtils400())),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 20, right: 20, top: 10),
                        child: Column(
                          children: [
                            Container(
                                height: 40,
                                width: Get.width,
                                decoration: BoxDecoration(
                                  color: backgroundColor,
                                  border: Border.all(
                                    color: Colors.black,
                                    width: 1,
                                  ),
                                ),
                                child: const NameTextField()),
                            const SizedBox(
                              height: 10,
                            ),
                            Container(
                                height: 40,
                                width: Get.width,
                                decoration: BoxDecoration(
                                  color: backgroundColor,
                                  border: Border.all(
                                    color: Colors.black,
                                    width: 1,
                                  ),
                                ),
                                child: const EmailTextField()),
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(top: 20.0, bottom: 20.0),
                        child: StartChatButton(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }
}
