import 'package:app_one/app_constants/app_constants.dart';
import 'package:domain/arguments/app_three/brand_list_detail_argument.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:presentation/const/app_colors/presentation_colors.dart';
import 'package:presentation/const/size_utils.dart';
import 'package:presentation/navigation/navigation.dart';
import 'package:presentation/widgets/common_app_bar/common_app_bar.dart';
import 'package:presentation/widgets/common_text/common_text_poppins.dart';
import 'package:app_one/app_pages/app_routes.dart';
import 'package:base_controller/controllers/alphabetic_brand_list_controller.dart';
import 'package:presentation_common_ui/common_screens/common_alphabatic_brand_list_screen/common_alphabatic_brand_list_screen.dart';


class AlphabeticBrandListScreen extends GetView<AlphabeticBrandListController> {
  const AlphabeticBrandListScreen({super.key});

  @override
  Widget build(BuildContext context) {

    debugPrint("inside alphabetic brand list");
    return const CommonAlphabeticBrandListScreen(
      projectName: APP_NAME,
    );
  }
}
