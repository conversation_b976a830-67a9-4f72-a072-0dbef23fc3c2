import 'package:base_controller/controllers/affiliate_program_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:presentation_common_ui/common_screens/common_aafiliate_program_screen/common_aafiliate_program_screen.dart';
import 'package:presentation_common_ui/gen/app_five/assets.gen.dart';

import '../../app_constants/app_constants.dart';

class AffiliateProgramScreen extends GetView<AffiliateProgramController> {
  const AffiliateProgramScreen({Key? key}) : super(key: key);

// late Rx<TextModel> textModel = Rx<TextModel>();

  @override
  Widget build(BuildContext context) {
    return CommonAffiliateProgramScreen(
      projectName: APP_NAME,
      // imgpath1: Assets.images.imgAffiliate1.path,
      // imgpath2: Assets.images.imgAffiliate2.path,
      // imgpath3: Assets.images.imgAffiliate3.path,
    );
  }
  /*Obx(() => Scaffold(
          appBar: commonAppbar(
              titleWidget: Text(LanguageConstants.affiliateProgramTitleText.tr),
              backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
              foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
              leadingWidget: IconButton(
                  onPressed: () {
                    Get.find<Navigation>().getBack<void>(null);
                  },
                  icon: Icon(
                    Icons.arrow_back,
                    color: Theme.of(context).iconTheme.color,
                  ))),
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          body: Expanded(
            child: ScrollConfiguration(
              behavior: MyBehavior(),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).padding.top,
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      height: 10.iY,
                    ),
                    // Container(child:
                    // SizedBox(height: 200.iX,),
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: CommonRichTextWidget(list: [
                        TextModel(
                            padding: EdgeInsets.symmetric(
                                vertical: 6, horizontal: 16),
                            text: LanguageConstants.whoAreWeLookingTitleText.tr,
                            fontSize: 16.iX,
                            textType: "title",
                            textAlign: TextAlign.left,
                            textColor: Theme.of(context)
                                .textTheme
                                .displayMedium!
                                .color),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                          text: LanguageConstants
                              .whoAreWeLookingDescriptionOneText.tr,
                          textColor:
                              Theme.of(context).textTheme.displayMedium!.color,
                          textAlign: TextAlign.start,
                          fontSize: 15.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                          text: LanguageConstants.whatCanYouExpectTitleText.tr,
                          textColor:
                              Theme.of(context).textTheme.displayMedium!.color,
                          textAlign: TextAlign.center,
                          fontSize: 16.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 1, horizontal: 16),
                          text: LanguageConstants.expectRuleOneText.tr,
                          textType: "bullet",
                          fontFamily: AppConstants.fontOpenSans,
                          textAlign: TextAlign.start,
                          fontSize: 15.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 1, horizontal: 16),
                          text: LanguageConstants.expectRuleTwoText.tr,
                          textType: "bullet",
                          fontFamily: AppConstants.fontOpenSans,
                          textAlign: TextAlign.start,
                          fontSize: 15.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 1, horizontal: 16),
                          text: LanguageConstants.expectRuleThreeText.tr,
                          textType: "bullet",
                          fontFamily: AppConstants.fontOpenSans,
                          textAlign: TextAlign.start,
                          fontSize: 15.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 1, horizontal: 16),
                          text: LanguageConstants.expectRuleFourText.tr,
                          textType: "bullet",
                          fontFamily: AppConstants.fontOpenSans,
                          textAlign: TextAlign.start,
                          fontSize: 15.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 1, horizontal: 16),
                          text: LanguageConstants.expectRuleFiveText.tr,
                          textType: "bullet",
                          fontFamily: AppConstants.fontOpenSans,
                          textAlign: TextAlign.start,
                          fontSize: 15.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                          text: LanguageConstants.promoteSoloQuestionText.tr,
                          textColor:
                              Theme.of(context).textTheme.displayMedium!.color,
                          textAlign: TextAlign.center,
                          fontSize: 16.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 1, horizontal: 16),
                          text: LanguageConstants.promoteSoloAnswerOneText.tr,
                          textType: "bullet",
                          fontFamily: AppConstants.fontOpenSans,
                          textAlign: TextAlign.start,
                          fontSize: 15.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 1, horizontal: 16),
                          text: LanguageConstants.promoteSoloAnswerTwoText.tr,
                          textType: "bullet",
                          fontFamily: AppConstants.fontOpenSans,
                          textAlign: TextAlign.start,
                          fontSize: 15.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 1, horizontal: 16),
                          text: LanguageConstants.promoteSoloAnswerThreeText.tr,
                          textType: "bullet",
                          fontFamily: AppConstants.fontOpenSans,
                          textAlign: TextAlign.start,
                          fontSize: 15.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 1, horizontal: 16),
                          text: LanguageConstants.promoteSoloAnswerFourText.tr,
                          textType: "bullet",
                          fontFamily: AppConstants.fontOpenSans,
                          textAlign: TextAlign.start,
                          fontSize: 15.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 1, horizontal: 16),
                          text: LanguageConstants.promoteSoloAnswerFiveText.tr,
                          textType: "bullet",
                          fontFamily: AppConstants.fontOpenSans,
                          textAlign: TextAlign.start,
                          fontSize: 15.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                          text: LanguageConstants.howApplyTitleText.tr,
                          textColor:
                              Theme.of(context).textTheme.displayMedium!.color,
                          textAlign: TextAlign.center,
                          fontSize: 16.iX,
                        ),
                        TextModel(
                          padding:
                              EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                          text: LanguageConstants.howApplyAnswerOneText.tr,
                          textColor:
                              Theme.of(context).textTheme.displayMedium!.color,
                          textAlign: TextAlign.start,
                          fontSize: 14.iX,
                        )
                      ]),
                    ),
                    affiliatePersonForm(context: context),
                  ],
                ),
              ),
            ),
          ),
        ));
  }

  Widget bulletSentence({required BuildContext context, required String text}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        HeadlineBodyOneBaseWidget(
          title: "•",
          titleColor: Theme.of(context).textTheme.displayMedium!.color,
          titleTextAlign: TextAlign.start,
          fontSize: 14.iX,
        ),
        SizedBox(
          width: 4.iX,
        ),
        Expanded(
          child: HeadlineBodyOneBaseWidget(
            title: text,
            titleColor: Theme.of(context).textTheme.displayMedium!.color,
            titleTextAlign: TextAlign.start,
            fontSize: 14.iX,
          ),
        ),
      ],
    );
  }

  Widget lookingForWidget({required BuildContext context}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 20.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HeadlineBodyOneBaseWidget(
            title: LanguageConstants.whoAreWeLookingTitleText.tr,
            titleColor: Theme.of(context).textTheme.displayMedium!.color,
            titleTextAlign: TextAlign.center,
            fontSize: 16.iX,
          ),
          Padding(
            padding: const EdgeInsets.all(10.0),
            child: Column(
              children: [
                HeadlineBodyOneBaseWidget(
                  title: LanguageConstants.whoAreWeLookingDescriptionOneText.tr,
                  titleColor: Theme.of(context).textTheme.displayMedium!.color,
                  titleTextAlign: TextAlign.start,
                  fontSize: 14.iX,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget expectForWidget({required BuildContext context}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HeadlineBodyOneBaseWidget(
            title: LanguageConstants.whatCanYouExpectTitleText.tr,
            titleColor: Theme.of(context).textTheme.displayMedium!.color,
            titleTextAlign: TextAlign.center,
            fontSize: 16.iX,
          ),
          Padding(
            padding: const EdgeInsets.all(10.0),
            child: Column(
              children: [
                bulletSentence(
                    context: context,
                    text: LanguageConstants.expectRuleOneText.tr),
                bulletSentence(
                    context: context,
                    text: LanguageConstants.expectRuleTwoText.tr),
                bulletSentence(
                    context: context,
                    text: LanguageConstants.expectRuleThreeText.tr),
                bulletSentence(
                    context: context,
                    text: LanguageConstants.expectRuleFourText.tr),
                bulletSentence(
                    context: context,
                    text: LanguageConstants.expectRuleFiveText.tr),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget promoteForWidget({required BuildContext context}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HeadlineBodyOneBaseWidget(
            title: LanguageConstants.promoteSoloQuestionText.tr,
            titleColor: Theme.of(context).textTheme.displayMedium!.color,
            titleTextAlign: TextAlign.center,
            fontSize: 16.iX,
          ),
          Padding(
            padding: const EdgeInsets.all(10.0),
            child: Column(
              children: [
                bulletSentence(
                    context: context,
                    text: LanguageConstants.promoteSoloAnswerOneText.tr),
                bulletSentence(
                    context: context,
                    text: LanguageConstants.promoteSoloAnswerTwoText.tr),
                bulletSentence(
                    context: context,
                    text: LanguageConstants.promoteSoloAnswerThreeText.tr),
                bulletSentence(
                    context: context,
                    text: LanguageConstants.promoteSoloAnswerFourText.tr),
                bulletSentence(
                    context: context,
                    text: LanguageConstants.promoteSoloAnswerFiveText.tr),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget applyForWidget({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.iX),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HeadlineBodyOneBaseWidget(
            title: LanguageConstants.howApplyTitleText.tr,
            titleColor: Theme.of(context).textTheme.displayMedium!.color,
            titleTextAlign: TextAlign.center,
            fontSize: 16.iX,
          ),
          Padding(
            padding: EdgeInsets.all(10.iX),
            child: Column(
              children: [
                HeadlineBodyOneBaseWidget(
                  title: LanguageConstants.howApplyAnswerOneText.tr,
                  titleColor: Theme.of(context).textTheme.displayMedium!.color,
                  titleTextAlign: TextAlign.start,
                  fontSize: 14.iX,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget affiliatePersonForm({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.iX, vertical: 24.iY),
      child: Form(
        autovalidateMode: AutovalidateMode.disabled,
        key: controller.formKey.value,
        child: GetBuilder<AffiliateProgramController>(
          id: "affi",
          builder: (controller) {
            return Column(
              children: [
                profilePartWidget(context: context),
                SizedBox(
                  height: 16.iY,
                ),
                addressPartWidget(context: context),
                SizedBox(
                  height: 20.iY,
                ),
                SizedBox(
                  child: ElevatedButton(
                    onPressed: () {
                      controller.onTap();
                    },
                    style: ElevatedButton.styleFrom(
                      elevation: 1,
                      backgroundColor: darkBlueColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: CommonTextOpenSans(
                      LanguageConstants.submitText.tr,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 16.iX,
                      height: 1.iY,
                    ),
                  ),
                ),
                SizedBox(
                  height: 40.iY,
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget profilePartWidget({required BuildContext context}) {
    return Column(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: HeadlineBodyOneBaseWidget(
            title: LanguageConstants.profile.tr,
            titleColor: darkBlueColor,
            titleTextAlign: TextAlign.left,
            fontWeight: FontWeight.w500,
            // underline: true,
            fontSize: 16.iX,
          ),
        ),
        SizedBox(
          height: 10.iY,
        ),
        Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.iX),
              border: Border.all(color: borderGreyColor, width: 2.iX)),
          child: Row(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.0.iX),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    onChanged: (String? value) {
                      controller.selectedValue.value = value.toString();
                    },
                    dropdownColor: Theme.of(context).colorScheme.surface,
                    icon: Image.asset(
                      AppAsset.downArrow,
                      height: 16.iY,
                      width: 16.iX,
                      color: Theme.of(context).iconTheme.color,
                    ),
                    style: const TextStyle(
                        color: Colors.grey,
                        fontFamily: AppConstants.fontPoppins),
                    itemHeight: 50.iY,
                    value: controller.selectedValue.value,
                    items: <String>[
                      LanguageConstants.mr.tr,
                      LanguageConstants.mrs.tr
                    ]
                        .map(
                          (String value) => DropdownMenuItem<String>(
                            value: value,
                            child: HeadlineBodyOneBaseWidget(
                              title: value,
                              fontSize: 14,
                              titleColor: Colors.grey,
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ),
              ),
              SizedBox(
                width: 10.iX,
              ),
              Expanded(
                child: SizedBox(
                  child: TextFormField(
                    controller: controller.firstNameController.value,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.titleLarge!.color,
                    ),
                    decoration: InputDecoration(
                      contentPadding: EdgeInsets.only(top: 8.iY, bottom: 8.iY),
                      border:
                          const OutlineInputBorder(borderSide: BorderSide.none),
                      hintText: controller.isValidation &&
                              controller.firstNameController.value.text == ""
                          ? LanguageConstants.enterFirstName.tr
                          : LanguageConstants.firstNameText.tr,
                      hintStyle: TextStyle(
                          fontSize: 14.iX,
                          fontFamily: AppConstants.fontPoppins,
                          color: Theme.of(context).hintColor),
                    ),
                    validator: (value) {
                      return Validators.validateName(value);
                    },
                  ),
                ),
              )
            ],
          ),
        ),
        SizedBox(
          height: 10.iY,
        ),
        Container(
          padding: EdgeInsets.only(left: 8.iX),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.iX),
              border: Border.all(color: borderGreyColor, width: 2.iX)),
          child: TextFormField(
            controller: controller.lastNameController.value,
            style: TextStyle(
              color: Theme.of(context).textTheme.displayMedium!.color,
            ),
            decoration: InputDecoration(
              contentPadding: EdgeInsets.only(top: 8.iY, bottom: 8.iY),
              border: const OutlineInputBorder(borderSide: BorderSide.none),
              hintText: controller.isValidation &&
                      controller.lastNameController.value.text == ""
                  ? LanguageConstants.enterLastName.tr
                  : LanguageConstants.lastNameText.tr,
              hintStyle: TextStyle(
                fontSize: 14.iX,
                fontFamily: AppConstants.fontPoppins,
              ),
            ),
            validator: (value) {
              return Validators.validateName(value);
            },
          ),
        ),
        SizedBox(
          height: 10.iY,
        ),
        Row(
          children: [
            Expanded(
              child: Container(
                padding: EdgeInsets.only(left: 8.iX),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6.iX),
                    border: Border.all(color: borderGreyColor, width: 2.iX)),
                child: TextFormField(
                  controller: controller.emailController.value,
                  keyboardType: TextInputType.emailAddress,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.displayMedium!.color,
                  ),
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.only(top: 8.iY, bottom: 8.iY),
                    border:
                        const OutlineInputBorder(borderSide: BorderSide.none),
                    hintText: controller.isValidation &&
                            controller.emailController.value.text == ""
                        ? LanguageConstants.enterEmailAddress.tr
                        : LanguageConstants.emailText.tr,
                    hintStyle: TextStyle(
                        fontSize: 14.iX,
                        fontFamily: AppConstants.fontPoppins,
                        color: Theme.of(context).hintColor),
                  ),
                  validator: (value) {
                    return Validators.validateEmail(value);
                  },
                ),
              ),
            ),
          ],
        ),
        SizedBox(
          height: 10.iY,
        ),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                child: CommonTextPhoneField(
                  textController: controller.contactNoController.value,
                  cursorColor: greyColor,
                  dropdownIconColor: Theme.of(context).iconTheme.color,
                  country: Get.find<CountryController>().country?.value,
                  borderColor: controller.phoneErrorMsg.value.isEmpty
                      ? borderGreyColor
                      : Colors.red,
                  focusedColor: blackColor,
                  dropdownTextStyle: AppTextStyle.textStyleUtils400(
                    color: Theme.of(context).textTheme.titleLarge!.color,
                  ),
                  fontStyle: AppTextStyle.textStyleUtils400(
                    color: Theme.of(context).textTheme.titleLarge!.color,
                  ),
                  hintStyle: TextStyle(
                      fontSize: 14.iX,
                      fontFamily: AppConstants.fontPoppins,
                      color: Theme.of(context).hintColor),
                  errorBorderColor: Colors.red,
                  hintText: controller.isValidation &&
                          controller.contactNoController.value.text == ""
                      ? LanguageConstants.enterPhoneNumber.tr
                      : LanguageConstants.contactNoText.tr,
                  onCountryChanged: (country) {
                    controller.countryCode = country.dialCode;
                  },
                ),
              ),
            ),
          ],
        ),
        SizedBox(
          height: 10.iY,
        ),
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Container(
                padding: EdgeInsets.only(left: 8.iX),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6.iX),
                    border: Border.all(color: borderGreyColor, width: 2.iX)),
                child: TextFormField(
                  controller: controller.websiteController.value,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.displayMedium!.color,
                  ),
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.only(top: 8.iY, bottom: 8.iY),
                    border:
                        const OutlineInputBorder(borderSide: BorderSide.none),
                    hintText: controller.isValidation &&
                            controller.websiteController.value.text == ""
                        ? LanguageConstants.enterWebsite.tr
                        : LanguageConstants.websiteUrlText.tr,
                    hintStyle: TextStyle(
                        fontSize: 14.iX,
                        fontFamily: AppConstants.fontPoppins,
                        color: Theme.of(context).hintColor),
                  ),
                  validator: (value) {
                    return Validators.validateRequired(
                        value ?? '', LanguageConstants.websiteUrlText.tr);
                  },
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget addressPartWidget({required BuildContext context}) {
    return Obx(() {
      return Column(
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: HeadlineBodyOneBaseWidget(
              title: LanguageConstants.addressText.tr,
              titleColor: darkBlueColor,
              titleTextAlign: TextAlign.left,
              fontWeight: FontWeight.w500,
              // underline: true,
              fontSize: 16.iX,
            ),
          ),
          SizedBox(
            height: 10.iY,
          ),
          Row(
            children: [
              Expanded(
                child: Container(
                  decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.iX),
                          side: const BorderSide(
                              color: borderGreyColor, width: 2))),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 10.iX),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        onChanged: (String? value) {
                          controller.selectedVisitorValue.value =
                              value.toString();
                        },
                        dropdownColor: Theme.of(context).colorScheme.surface,
                        style: AppTextStyle.textStyleUtils400(
                          color: Theme.of(context).textTheme.titleLarge!.color,
                        ),
                        icon: Image.asset(
                          AppAsset.downArrow,
                          height: 16.iY,
                          width: 16.iX,
                          color: Theme.of(context).iconTheme.color,
                        ),
                        value: controller.selectedVisitorValue.value,
                        items: <String>[
                          LanguageConstants.visitorMonthHintText.tr,
                          LanguageConstants.lessThen500Text.tr,
                          LanguageConstants.fiveHundredToThousandText.tr,
                          LanguageConstants.over500Text.tr,
                        ]
                            .map(
                              (String value) => DropdownMenuItem<String>(
                                value: value,
                                child: HeadlineBodyOneBaseWidget(
                                    title: value,
                                    fontSize: 14.iX,
                                    textOverflow: TextOverflow.ellipsis,
                                    fontFamily: AppConstants.fontPoppins,
                                    titleColor: Colors.grey),
                              ),
                            )
                            .toList(),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.iY,
          ),
          Row(
            children: [
              Expanded(
                child: Container(
                  decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: const BorderSide(
                              color: borderGreyColor, width: 2))),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        onChanged: (String? value) {
                          controller.selectedViewsValue.value =
                              value.toString();
                        },
                        icon: Image.asset(
                          AppAsset.downArrow,
                          height: 16,
                          width: 16.0,
                          color: Theme.of(context).iconTheme.color,
                        ),
                        dropdownColor: Theme.of(context).colorScheme.surface,
                        style: AppTextStyle.textStyleUtils400(
                          color: Theme.of(context).textTheme.titleLarge!.color,
                        ),
                        value: controller.selectedViewsValue.value,
                        items: <String>[
                          LanguageConstants.viewsMonthHintText.tr,
                          LanguageConstants.lessThen500Text.tr,
                          LanguageConstants.fiveHundredToThousandText.tr,
                          LanguageConstants.over500Text.tr,
                        ]
                            .map(
                              (String value) => DropdownMenuItem<String>(
                                value: value,
                                child: HeadlineBodyOneBaseWidget(
                                    title: value,
                                    fontFamily: AppConstants.fontPoppins,
                                    textOverflow: TextOverflow.ellipsis,
                                    fontSize: 14.iX,
                                    titleColor: Colors.grey),
                              ),
                            )
                            .toList(),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.iY,
          ),
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.only(left: 8.iX),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.iX),
                      border: Border.all(color: borderGreyColor, width: 2.iX)),
                  child: TextFormField(
                    controller: controller.addressOneController.value,
                    keyboardType: TextInputType.streetAddress,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.displayMedium!.color,
                    ),
                    decoration: InputDecoration(
                      contentPadding: EdgeInsets.only(top: 8.iY, bottom: 8.iY),
                      border:
                          const OutlineInputBorder(borderSide: BorderSide.none),
                      hintText: controller.isValidation &&
                              controller.addressOneController.value.text == ""
                          ? LanguageConstants.enterAddress1.tr
                          : LanguageConstants.addressOneText.tr,
                      hintStyle: TextStyle(
                          fontSize: 14.iX,
                          fontFamily: AppConstants.fontPoppins,
                          color: Theme.of(context).hintColor),
                    ),
                    validator: (value) {
                      return Validators.validateAddress(
                          value ?? '', LanguageConstants.addressOneText.tr);
                    },
                  ),
                ),
              ),
              SizedBox(
                width: 10.iX,
              ),
              Expanded(
                child: Container(
                  padding: EdgeInsets.only(left: 8.iX),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.iX),
                      border: Border.all(color: borderGreyColor, width: 2.iX)),
                  child: TextFormField(
                    controller: controller.addressTwoController.value,
                    keyboardType: TextInputType.streetAddress,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.displayMedium!.color,
                    ),
                    decoration: InputDecoration(
                      contentPadding: EdgeInsets.only(top: 8.iY, bottom: 8.iY),
                      border:
                          const OutlineInputBorder(borderSide: BorderSide.none),
                      hintText: controller.isValidation &&
                              controller.addressTwoController.value.text == ""
                          ? LanguageConstants.enterAddress2.tr
                          : LanguageConstants.addressTwoText.tr,
                      hintStyle: TextStyle(
                          fontSize: 14.iX,
                          fontFamily: AppConstants.fontPoppins,
                          color: Theme.of(context).hintColor),
                    ),
                    validator: (value) {
                      return Validators.validateAddress(
                          value ?? '', LanguageConstants.addressTwoText.tr);
                    },
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.iY,
          ),
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.only(left: 8.iX),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.iX),
                      border: Border.all(color: borderGreyColor, width: 2.iX)),
                  child: TextFormField(
                    controller: controller.cityController.value,
                    keyboardType: TextInputType.streetAddress,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.displayMedium!.color,
                    ),
                    decoration: InputDecoration(
                      contentPadding: EdgeInsets.only(top: 8.iY, bottom: 8.iY),
                      border:
                          const OutlineInputBorder(borderSide: BorderSide.none),
                      hintText: controller.isValidation &&
                              controller.cityController.value.text == ""
                          ? LanguageConstants.enterCityName.tr
                          : LanguageConstants.cityHintText.tr,
                      hintStyle: TextStyle(
                          fontSize: 14.iX,
                          fontFamily: AppConstants.fontPoppins),
                    ),
                    validator: (value) {
                      return Validators.validateAddress(
                          value ?? '', LanguageConstants.enterCityName.tr);
                    },
                  ),
                ),
              ),
              SizedBox(
                width: 10.iX,
              ),
              Expanded(
                child: Container(
                  decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.iX),
                          side: const BorderSide(
                              color: borderGreyColor, width: 2))),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 10.iX),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        dropdownColor: Theme.of(context).colorScheme.surface,
                        style: AppTextStyle.textStyleUtils400(
                          color: Theme.of(context).textTheme.titleLarge!.color,
                        ),
                        onChanged: (String? value) {
                          controller.countryValue.value = value.toString();
                        },
                        icon: Image.asset(
                          AppAsset.downArrow,
                          height: 16.iY,
                          width: 16.iX,
                          color: Theme.of(context).iconTheme.color,
                        ),
                        value: controller.countryValue.value,
                        items: <String>[
                          LanguageConstants.countryHintText.tr,
                          LanguageConstants.mr.tr,
                          LanguageConstants.mrs.tr
                        ]
                            .map(
                              (String value) => DropdownMenuItem<String>(
                                value: value,
                                child: HeadlineBodyOneBaseWidget(
                                    title: value,
                                    fontSize: 14,
                                    titleColor: Colors.grey),
                              ),
                            )
                            .toList(),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.iY,
          ),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Container(
                  padding: EdgeInsets.only(left: 8.iX),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.iX),
                      border: Border.all(color: borderGreyColor, width: 2.iX)),
                  child: TextFormField(
                    controller: controller.postCodeController.value,
                    keyboardType: TextInputType.streetAddress,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.displayMedium!.color,
                    ),
                    decoration: InputDecoration(
                      contentPadding: EdgeInsets.only(top: 8.iY, bottom: 8.iY),
                      border:
                          const OutlineInputBorder(borderSide: BorderSide.none),
                      hintText: controller.isValidation &&
                              controller.postCodeController.value.text == ""
                          ? LanguageConstants.enterPostCode.tr
                          : LanguageConstants.postCodeText.tr,
                      hintStyle: TextStyle(
                          fontSize: 14.iX,
                          fontFamily: AppConstants.fontPoppins,
                          color: Theme.of(context).hintColor),
                    ),
                    validator: (value) {
                      return Validators.validateAddress(
                          value ?? '', LanguageConstants.addressOneText.tr);
                    },
                  ),
                ),
              ),
              SizedBox(
                width: 10.iX,
              ),
              Expanded(
                flex: 1,
                child: Container(),
              ),
            ],
          ),
        ],
      );
    });
  }*/
}
