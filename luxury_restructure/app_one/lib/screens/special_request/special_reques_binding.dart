import 'package:base_controller/controllers/special_request_controller.dart';
import 'package:app_one/core/consts/app_constants.dart';

import 'package:get/get.dart';
import 'package:meta_package/api/api_repository/my_ticket_api_repository.dart';
import 'package:meta_package/api/api_repository/special_request_repository.dart';
import 'package:meta_package/api/services/ticket_service.dart';

class SpecialRequestBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SpecialRequestController(Get.find(), Get.find()));
    Get.lazyPut(
        () => SpecialRequestRepository(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => MyTicketAPIRepository(
        ticketService: TicketService(AppConstants.apiEndPointLogin)));
  }
}
