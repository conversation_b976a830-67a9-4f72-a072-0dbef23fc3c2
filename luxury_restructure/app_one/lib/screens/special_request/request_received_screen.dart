import 'package:app_one/app_pages/app_routes.dart';
import 'package:flutter/material.dart';
import 'package:presentation_common_ui/common_screens/common_request_received_screen/common_request_received_screen.dart';

import '../../app_constants/app_constants.dart';

class RequestReceivedScreen extends StatelessWidget {
  const RequestReceivedScreen({Key? key}) : super(key: key);
  /*PreferredSizeWidget _buildAppBar(BuildContext context) {
    return commonAppbar(
        titleWidget: CommonTextPoppins(LanguageConstants.yourRequestHasBeenReceived.tr, color: Colors.black));
  }*/

  @override
  Widget build(BuildContext context) {
    return const CommonRequestReceivedScreen(
      projectName: APP_NAME,
      dashboardRoute: RoutesConstants.dashboardScreen,
      ticketScreenRoute: RoutesConstants.trackTicketScreen,
    );
    /*WillPopScope(
      onWillPop: () async {
        Get.offAllNamed<void>(RoutesConstants.dashboardScreen);
        return true;
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: _buildAppBar(context),
        body: Center(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.iX),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                requestReceivedWidget(context),
                SizedBox(height: 30.iY),
                CommonThemeButton(
                  onTap: () {
                    Get.offAndToNamed<void>(RoutesConstants.myTicketsScreen);
                  },
                  title: LanguageConstants.myTicketsText.tr,
                  buttonColor: darkBlueColor,
                ),
                const SizedBox(height: 10),
                CommonThemeButton(
                  onTap: () {
                    Get.offAllNamed<void>(RoutesConstants.dashboardScreen);
                  },
                  title: LanguageConstants.continueShopping.tr,
                  buttonColor: darkBlueColor,
                ),
              ],
            ),
          ),
        ),
      ),
    );*/
  }

/*  Widget requestReceivedWidget(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.iX),
      child: Column(
        children: [
          // Text(
          //   LanguageConstants.yourRequestHasBeenReceived.tr,
          //   textAlign: TextAlign.center,
          //   style: AppTextStyle.textStyleUtilsUnderLine20(
          //     color: blackColor,
          //     fontWeight: FontWeight.w400,
          //   ),
          // ),
          // const SizedBox(height: 10),
          Text(
            LanguageConstants.youCanViewYourRequestsatMyTickets.tr,
            textAlign: TextAlign.center,
            style: AppTextStyle.textStyleUtils400(color: Theme.of(context).textTheme.titleLarge!.color),
          ),
        ],
      ),
    );
  }*/
}
