import 'package:flutter/material.dart';
import 'package:base_controller/controllers/country_controllers.dart';
import 'package:base_controller/controllers/special_request_controller.dart';
import 'package:app_one/theme/colors.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:presentation/const/app_text_style/presentation_app_text_style.dart';
import 'package:presentation/const/size_utils.dart';
import 'package:presentation/widgets/common_buttons/common_theme_button.dart';
import 'package:presentation/widgets/common_text_fields/input_text_field_widget.dart';
import 'package:presentation/widgets/custom_phone_field/common_text_phone_field.dart';
import 'package:presentation_common_ui/common_screens/common_special_request_screen/common_special_request_screen.dart';

import '../../app_constants/app_constants.dart';

class SpecialRequestScreen extends GetView<SpecialRequestController> {
  const SpecialRequestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const CommonSpecialRequestScreen(
      projectName: APP_NAME,
    );
  }

//   Widget nameTextField(BuildContext context) {
//     return GetBuilder<SpecialRequestController>(
//         id: "Create",
//         builder: (c) {
//           return SizedBox(
//             child: TextFormFieldWidget(
//               maxLines: 1,
//               controller: controller.nameController,
//               hintText: controller.nameController.text == "" &&
//                       controller.isValidation
//                   ? LanguageConstants.firstNameIsRequired.tr
//                   : LanguageConstants.firstNameText.tr,
//               hintStyle: AppTextStyle.textStyleUtils400(
//                   color: Theme.of(context).hintColor),
//               textStyle: TextStyle(
//                 color: Theme.of(context).textTheme.titleLarge!.color,
//               ),
//               validator: (value) => null,
//             ),
//           );
//         });
//   }
//
//   Widget lastNameField(BuildContext context) {
//     return GetBuilder<SpecialRequestController>(
//         id: "Create",
//         builder: (c) {
//           return SizedBox(
//             child: TextFormFieldWidget(
//               inputFormFilledColor: Theme.of(context).scaffoldBackgroundColor,
//               maxLines: 1,
//               controller: controller.lastNaneController,
//               hintText: controller.lastNaneController.text == "" &&
//                       controller.isValidation
//                   ? LanguageConstants.lastNameIsRequired.tr
//                   : LanguageConstants.lastNameText.tr,
//               hintStyle: AppTextStyle.textStyleUtils400(
//                   color: Theme.of(context).hintColor),
//               textStyle: TextStyle(
//                 color: Theme.of(context).textTheme.titleLarge!.color,
//               ),
//               validator: (value) => null,
//             ),
//           );
//         });
//   }
//
//   Widget emailField(BuildContext context) {
//     return GetBuilder<SpecialRequestController>(
//         id: "Create",
//         builder: (c) {
//           return SizedBox(
//             child: TextFormFieldWidget(
//               maxLines: 1,
//               controller: controller.emailController,
//               hintText: controller.emailController.text == "" &&
//                       controller.isValidation
//                   ? LanguageConstants.emailRequired.tr
//                   : LanguageConstants.emailAddreessText.tr,
//               hintStyle: AppTextStyle.textStyleUtils400(
//                   color: Theme.of(context).hintColor),
//               textStyle: TextStyle(
//                 color: Theme.of(context).textTheme.titleLarge!.color,
//               ),
//               validator: (value) => null,
//             ),
//           );
//         });
//   }
//
//   Widget phoneNumberField(BuildContext context) {
//     return GetBuilder<SpecialRequestController>(
//         id: "Create",
//         builder: (c) {
//           return SizedBox(
//             child: CommonTextPhoneField(
//               borderRadius: BorderRadius.circular(12),
//               textController: controller.phoneNumberController,
//               cursorColor: greyColor,
//               dropdownIconColor: Colors.grey.shade500,
//               borderColor: Colors.grey.shade400,
//               focusedColor: Colors.grey.shade400,
//               country: Get.find<CountryController>().country?.value,
//               dropdownTextStyle: AppTextStyle.textStyleUtils400(
//                 color: Theme.of(context).textTheme.titleLarge!.color,
//               ),
//               fontStyle: AppTextStyle.textStyleUtils400(
//                 color: Theme.of(context).textTheme.titleLarge!.color,
//               ),
//               hintStyle: AppTextStyle.textStyleUtils400(
//                   color: Theme.of(context).hintColor),
//               hintText: controller.phoneNumberController.text == "" &&
//                       controller.isValidation
//                   ? LanguageConstants.phoneNumberIsRequired.tr
//                   : LanguageConstants.phoneNumberText.tr,
//               onCountryChanged: (country) {
//                 controller.countryCode = country.dialCode;
//               },
//               errorBorderColor: Colors.black,
//             ),
//           );
//         });
//   }
//
//   Widget brandNameField(BuildContext context) {
//     return GetBuilder<SpecialRequestController>(
//         id: "Create",
//         builder: (c) {
//           return SizedBox(
//             child: TextFormFieldWidget(
//               maxLines: 1,
//               controller: controller.brandNameControlletr,
//               hintText: controller.brandNameControlletr.text == "" &&
//                       controller.isValidation
//                   ? LanguageConstants.brandNameIsRequired.tr
//                   : LanguageConstants.brandName.tr,
//               hintStyle: AppTextStyle.textStyleUtils400(
//                   color: Theme.of(context).hintColor),
//               textStyle: TextStyle(
//                 color: Theme.of(context).textTheme.titleLarge!.color,
//               ),
//               validator: (value) => null,
//             ),
//           );
//         });
//   }
//
//   Widget styleField(BuildContext context) {
//     return GetBuilder<SpecialRequestController>(
//         id: "Create",
//         builder: (c) {
//           return SizedBox(
//             child: TextFormFieldWidget(
//               maxLines: 1,
//               controller: controller.styleController,
//               hintText: controller.styleController.text == "" &&
//                       controller.isValidation
//                   ? LanguageConstants.styleIsRequired.tr
//                   : LanguageConstants.style.tr,
//               hintStyle: AppTextStyle.textStyleUtils400(
//                   color: Theme.of(context).hintColor),
//               textStyle: TextStyle(
//                 color: Theme.of(context).textTheme.titleLarge!.color,
//               ),
//               validator: (value) => null,
//             ),
//           );
//         });
//   }
//
//   Widget keywordField(BuildContext context) {
//     return GetBuilder<SpecialRequestController>(
//         id: "Create",
//         builder: (c) {
//           return SizedBox(
//             child: TextFormFieldWidget(
//               maxLines: 1,
//               controller: controller.keywordControlle,
//               hintText: controller.keywordControlle.text == "" &&
//                       controller.isValidation
//                   ? LanguageConstants.keywordIsRequired.tr
//                   : LanguageConstants.keyword.tr,
//               hintStyle: AppTextStyle.textStyleUtils400(
//                   color: Theme.of(context).hintColor),
//               textStyle: TextStyle(
//                 color: Theme.of(context).textTheme.titleLarge!.color,
//               ),
//               validator: (value) => null,
//             ),
//           );
//         });
//   }
//
//   Widget imageUrlField(BuildContext context) {
//     return GetBuilder<SpecialRequestController>(
//         id: "Create",
//         builder: (c) {
//           return SizedBox(
//             child: TextFormFieldWidget(
//               maxLength: 70,
//               maxLines: 1,
//               controller: controller.imageUrlController,
//               hintText: controller.imageUrlController.text == "" &&
//                       controller.isValidation
//                   ? LanguageConstants.imageUrlIsRequired.tr
//                   : LanguageConstants.imageURL.tr,
//               hintStyle: AppTextStyle.textStyleUtils400(
//                   color: Theme.of(context).hintColor),
//               textStyle: TextStyle(
//                 color: Theme.of(context).textTheme.titleLarge!.color,
//               ),
//               validator: (value) => null,
//             ),
//           );
//         });
//   }
//
//   Widget remarkField(BuildContext context) {
//     return GetBuilder<SpecialRequestController>(
//         id: "Create",
//         builder: (c) {
//           return SizedBox(
//             child: TextFormFieldWidget(
//               maxLines: 1,
//               controller: controller.remarkController,
//               hintText: controller.remarkController.text == "" &&
//                       controller.isValidation
//                   ? LanguageConstants.remarkIsRequired.tr
//                   : LanguageConstants.remark.tr,
//               hintStyle: AppTextStyle.textStyleUtils400(
//                   color: Theme.of(context).hintColor),
//               textStyle: TextStyle(
//                 color: Theme.of(context).textTheme.titleLarge!.color,
//               ),
//               validator: (value) => null,
//             ),
//           );
//         });
//   }
//
//   Widget submitAccountButton(BuildContext context) {
//     return Padding(
//       padding: EdgeInsets.only(bottom: 15.iY),
//       child: SizedBox(
//         child: CommonThemeButton(
//           onTap: () {
//             controller.contactUsPost(context);
//           },
//           title: LanguageConstants.submitText.tr,
//           buttonColor: darkBlueColor,
//         ),
//       ),
//     );
//   }
}
