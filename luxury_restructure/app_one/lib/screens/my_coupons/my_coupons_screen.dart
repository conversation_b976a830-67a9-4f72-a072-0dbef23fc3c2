import 'package:presentation_common_ui/common_screens/common_coupon_list_screen/common_coupon_list_screen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:base_controller/controllers/my_coupons_controller.dart';
import '../../app_constants/app_constants.dart';
import '../../app_pages/app_routes.dart';

class MyCouponsScreen extends GetView<MyCouponsController> {
  const MyCouponsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const CommonCouponListScreen(
      projectName: APP_NAME,
      route: RoutesConstants.specialRequestScreen,
    );
    /*Obx(
      () => Scaffold(
        // appBar: commonAppbar(
        //     titleWidget: ommonTextBrasley(
        //           //   LanguageConstants.myCouponsText.tr,
        //           //   fontSize: 36.iY,
        //           //   fontWeight: FontWeight.w500,
        //           //   color: primary500Color,
        //           // )C),
        body: controller.isLoading.value
            ? const Center(
                child: SpinKitThreeBounce(
                  color: darkBlueColor,
                  // size: 50.0,
                ),
              )
            : SafeArea(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(
                          height: 34,
                        ),
                        GestureDetector(
                          onTap: (){
                            CommonDialog.showFormCommonDialog(
                                endpoint:AppConstants.apiEndPointLogin,
                                context,
                                title:'Need Help with Coupon list?',
                                formType: 'coupon_help',
                                buttonColor: darkBlueColor,
                                // textStyle: AppTextStyle.textStyleUtils600(color: Theme.of(Get.context!).textTheme.titleLarge!.color),
                                projectName: 'app_one',
                            );
                          },
                          child: Text(
                            "Need help?",
                            textAlign: TextAlign.center,
                            style: AppStyle.textStyleUtils500(
                              color: Theme.of(context).textTheme.displayMedium!.color,
                            ),
                          ),
                        ),
                        Center(
                          child: CommonTextBrasley(
                            LanguageConstants.myCouponsText.tr,
                            fontSize: 36.iY,
                            fontWeight: FontWeight.w500,
                            color: primary500Color,
                          ),
                        ),
                        const SizedBox(
                          height: 54,
                        ),
                        Text(
                          LanguageConstants.activeCoupons.tr,
                          style: AppStyle.textStyleUtils500_20b(),
                        ),
                        const SizedBox(
                          height: 30,
                        ),
                        ListView.builder(
                            physics: const BouncingScrollPhysics(),
                            shrinkWrap: true,
                            itemCount: 5,
                            itemBuilder: (context, index) {
                              return Container(
                                margin: const EdgeInsets.only(bottom: 30),
                                padding: const EdgeInsets.all(30),
                                decoration: BoxDecoration(
                                    border: Border.all(
                                        color: secondaryWhiteColor, width: 2),
                                    borderRadius: BorderRadius.circular(10)),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      height: 36,
                                      width: 92,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16),
                                      decoration: BoxDecoration(
                                          color: secondaryWhiteColor,
                                          borderRadius:
                                              BorderRadius.circular(4)),
                                      child: Center(
                                          child: Text(
                                        "Effective",
                                        style: AppStyle.textStyleUtils700_14b(
                                            color: secondaryBlackColor),
                                      )),
                                    ),
                                    SizedBox(
                                      height: 20.iY,
                                    ),
                                    Text.rich(
                                      TextSpan(
                                        children: [
                                          TextSpan(
                                              text: "Bottega: ",
                                              style: AppStyle
                                                  .textStyleUtils700_20b()),
                                          TextSpan(
                                              text:
                                                  'Buy over \$1500 get 20% off on cart',
                                              style: AppStyle
                                                  .textStyleUtils500_20b()),
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      height: 20.iY,
                                    ),
                                    Text(
                                        "May 23, 2023, 8:00:00 PM - June 10, 2023, 6:59:59 PM",
                                        style: AppStyle.textStyleUtils500_16b(
                                            color: secondaryBlackColor)),
                                    SizedBox(
                                      height: 30.iY,
                                    ),
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 10.iX),
                                      decoration: BoxDecoration(
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(50)),
                                          border: Border.all(
                                              width: 2,
                                              color: primary400Color)),
                                      child: Center(
                                        child: Text(
                                            LanguageConstants.readMore.tr,
                                            style:
                                                AppStyle.textStyleUtils700_14b(
                                                    color: primary400Color)),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 12.iY,
                                    ),
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 10.iX),
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                          color: primary500Color,
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(50)),
                                          border: Border.all(
                                              color: primary500Color)),
                                      child: Center(
                                        child: Text(
                                            LanguageConstants.findProducts.tr,
                                            style:
                                                AppStyle.textStyleUtils700_14b(
                                                    color:
                                                        whitePrimary50Color)),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            })
                      ],
                    ),
                  ),
                ),
              ),
        // Column(
        //         mainAxisSize: MainAxisSize.min,
        //         mainAxisAlignment: MainAxisAlignment.start,
        //         children: [
        //           SizedBox(height: 14.iY),
        //           SizedBox(height: 14.iY),
        //           Expanded(
        //             child: ((controller.couponCode.value.items?.length ?? 0) == 0)
        //                 ? Column(
        //                     mainAxisAlignment: MainAxisAlignment.center,
        //                     children: [
        //                       NothingToShowAnimationWidget(
        //                         nothingToShowAnimationPath: AppAsset.nothigToShowAnimation,
        //                       ),
        //                       Text(
        //                         LanguageConstants.noCouponsFound.tr,
        //                         textAlign: TextAlign.center,
        //                         style: AppStyle.textStyleUtils500(
        //                           color: Theme.of(context).textTheme.displayMedium!.color,
        //                         ),
        //                       ),
        //                     ],
        //                   )
        //                 : ListView.builder(
        //                     itemCount: (controller.couponCode.value.items?.length ?? 0),
        //                     itemBuilder: (context, index) {
        //                       CouponItems? couponItem = controller.couponCode.value.items?[index];
        //                       return Column(
        //                         children: [
        //                           const SizedBox(
        //                             height: 8,
        //                           ),
        //                           Row(
        //                             children: [
        //                               Expanded(
        //                                 flex: 2,
        //                                 child: Text(
        //                                   couponItem?.couponId?.toString() ?? '-',
        //                                   textAlign: TextAlign.center,
        //                                   style: AppStyle.textStyleUtils500(
        //                                     color: Theme.of(context).textTheme.displayMedium!.color,
        //                                   ),
        //                                 ),
        //                               ),
        //                               Expanded(
        //                                 flex: 3,
        //                                 child: Text(
        //                                   couponItem?.code?.toString() ?? '-',
        //                                   textAlign: TextAlign.center,
        //                                   style: AppStyle.textStyleUtils500(
        //                                     color: Theme.of(context).textTheme.displayMedium!.color,
        //                                   ),
        //                                 ),
        //                               ),
        //                               Expanded(
        //                                 flex: 2,
        //                                 child: Text(
        //                                   couponItem?.expirationDate == null &&
        //                                           (couponItem?.expirationDate?.isEmpty ?? true)
        //                                       ? '-'
        //                                       : DateFormat('dd MMM yyyy HH:mm:ss').format(
        //                                           DateTime.parse(couponItem?.expirationDate ?? ''),
        //                                         ),
        //                                   textAlign: TextAlign.center,
        //                                   style: AppStyle.textStyleUtils500(
        //                                     color: Theme.of(context).textTheme.displayMedium!.color,
        //                                   ),
        //                                 ),
        //                               ),
        //                             ],
        //                           ),
        //                           const SizedBox(
        //                             height: 8,
        //                           ),
        //                           const Divider(),
        //                         ],
        //                       );
        //                     },
        //                   ),
        //           ),
        //         ],
        //       ),
      ),
    );*/
  }
}
