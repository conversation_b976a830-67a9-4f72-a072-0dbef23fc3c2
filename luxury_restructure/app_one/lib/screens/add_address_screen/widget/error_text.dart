import 'package:presentation/const/app_text_style/presentation_app_text_style.dart';

import '../../../all_imports.dart';

class ErrorText extends StatelessWidget {
  const ErrorText({Key? key, required this.text, required this.textAlign})
      : super(key: key);
  final String text;
  final TextAlign? textAlign;

  @override
  Widget build(BuildContext context) {
    return text.isNotEmpty
        ? Padding(
            padding: const EdgeInsets.only(top: 5.0),
            child: CommonTextOpenSans(
              text,
              style: AppTextStyle.textStyleError(),
              textAlign: textAlign,
            ),
          )
        : const SizedBox.shrink();
  }
}
