import 'package:get/get.dart';
import 'package:base_controller/controllers/add_address_controller.dart';
import 'package:presentation_common_ui/common_screens/common_add_address_screen/common_add_address_screen.dart';
import 'package:flutter/material.dart';

import '../../app_constants/app_constants.dart';

class AddAddressScreen extends GetView<AddAddressController> {
  const AddAddressScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const CommonAddAddressScreen(
      projectName: APP_NAME,
    );
    /*WillPopScope(
      onWillPop: () async {
        Get.back(result: false);
        return false;
      },
      child: Scaffold(
        appBar: commonAppbar(
          titleWidget: CommonTextPoppins(
            LanguageConstants.addressDetails.tr,
            height: 2.iY,
            color: Theme.of(context).textTheme.displayMedium!.color,
          ),
          leadingWidget: IconButton(
              onPressed: () {
                Get.back(result: false);
              },
              icon: Icon(
                Icons.arrow_back,
                color: Theme.of(context).textTheme.displayMedium!.color,
              )),
        ),
        body: Obx(
          () => controller.isLoading.value
              ? Center(
                  child: AbsorbPointer(
                    absorbing: true,
                    child: Center(
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
                        child: const ScreenLoading(appColor: darkBlueColor),
                      ),
                    ),
                  ),
                )
              : Stack(
                  children: [
                    _AddAddressBody(),
                    Obx(() => controller.isScreenLoading.value
                        ? const ScreenLoading(
                            appColor: darkBlueColor,
                          )
                        : const SizedBox.shrink())
                  ],
                ),
        ),
      ),
    )*/
    ;
  }
}

/*class _AddAddressBody extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: GetBuilder<AddAddressController>(
        id: "Address",
        builder: (controller) {
          return Form(
            key: controller.formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(height: 20.iY),
                CommonRadioButtonWidget(
                  list: ['Home', 'Office', 'Other'],
                  selectedIndex: 0,
                  backGroundColor: Colors.blue,
                  onItemClick: () {},
                  title: 'title',
                  trailingText: 'trialing text',
                ),
                SizedBox(height: 20.iY),
                Container(
                  padding: EdgeInsets.all(24.iY),
                  child: Column(
                    children: [
                      TextFormField(
                        controller: controller.firstNameController,
                        cursorColor: darkBlueColor,
                        decoration: addressDecoration(hintText: LanguageConstants.firstNameText.tr),
                        validator: (value) => Validators.validateName(
                          value?.trim(),
                        ),
                      ),
                      SizedBox(height: 16.iY),
                      SizedBox(
                        child: TextFormField(
                          cursorColor: darkBlueColor,
                          controller: controller.lastNameController,
                          decoration: addressDecoration(hintText: LanguageConstants.lastNameText.tr),
                          validator: (value) => Validators.validateName(
                            value?.trim(),
                          ),
                        ),
                      ),
                      SizedBox(height: 16.iY),
                      SizedBox(
                        child: CommonTextPhoneField(
                          filled: true,
                          fillColor: tileBackgroundColor,
                          textController: controller.phoneNumberController,
                          textColor: appColorButton,
                          cursorColor: darkBlueColor,
                          dropdownTextColor: appColorButton,
                          dropdownIconColor: appColorButton,
                          borderColor: Colors.transparent,
                          dropdownTextStyle: const TextStyle(
                            color: blackColor,
                          ),
                          fontStyle: const TextStyle(
                            color: blackColor,
                          ),
                          hintStyle: const TextStyle(color: greyColor),
                          country: Get.find<CountryController>().country?.value,
                          hintText: controller.isValidation && controller.phoneNumberController.text == "" ? LanguageConstants.enterPhoneNumber.tr : LanguageConstants.contactNoText.tr,
                          onCountryChanged: (country) {
                            controller.countryCodeNumber.value = country.dialCode;
                          },
                          validator: (value) => Validators.validateMobile(
                            value?.number ?? '',
                          ),
                          errorBorderColor: Colors.red,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 10.iX),
                        child: Align(
                          alignment: Alignment.topLeft,
                          child: ErrorText(
                            text: controller.phoneErrorMsg.value,
                            // textAlign: TextAlign.left,
                          ),
                        ),
                      ),
                      SizedBox(height: 16.iY),
                      SizedBox(
                        child: TextFormField(
                          cursorColor: darkBlueColor,
                          controller: controller.zipPovinceController,
                          keyboardType: TextInputType.number,
                          decoration: addressDecoration(hintText: LanguageConstants.zipOrProvinceText.tr),
                          validator: (value) => Validators.validateZip(value?.trim() ?? '', LanguageConstants.enterZipOrProvince.tr),
                        ),
                      ),
                      SizedBox(height: 16.iY),
                      SizedBox(
                        child: TextFormField(
                          cursorColor: darkBlueColor,
                          controller: controller.address1Controller,
                          decoration: addressDecoration(hintText: LanguageConstants.addressOneText.tr, borderRadius: BorderRadius.circular(0.0)),
                          validator: (value) => Validators.validateAddress(value?.trim() ?? '', LanguageConstants.enterAddress1.tr),
                        ),
                      ),
                      SizedBox(height: 16.iY),
                      SizedBox(
                        child: TextFormField(
                          cursorColor: darkBlueColor,
                          controller: controller.address2Controller,
                          decoration: addressDecoration(hintText: LanguageConstants.addressTwoText.tr),
                          validator: (value) => Validators.validateAddress(value?.trim() ?? '', LanguageConstants.enterAddress2.tr),
                        ),
                      ),
                      SizedBox(height: 16.iY),
                      SizedBox(
                        child: TextFormField(
                          cursorColor: darkBlueColor,
                          controller: controller.cityController,
                          decoration: addressDecoration(
                              hintText: controller.isValidation && controller.cityController.text == "" ? LanguageConstants.enterCity.tr : LanguageConstants.cityText.tr,
                              borderRadius: BorderRadius.circular(0.0)),
                          validator: (value) => Validators.validateAddress(value?.trim() ?? '', LanguageConstants.enterCity.tr),
                        ),
                      ),
                      SizedBox(height: 16.iY),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 10.0.iX,
                        ),
                        width: double.infinity,
                        decoration: BoxDecoration(color: tileBackgroundColor, borderRadius: BorderRadius.circular(12)),
                        child: CommonCountryDropdown(
                          onChanged: (v) {
                            controller.selectedCoutry.value = v as CountryListModel;
                          },
                        ),
                        // child: DropdownButton(
                        //   dropdownColor: backgroundColor,
                        //   items: controller.getcountryList
                        //       .map((value) => DropdownMenuItem<CountryListModel>(
                        //             value: value,
                        //             child: Text(value.fullNameEnglish.toString()),
                        //           ))
                        //       .toList(),
                        //   isExpanded: true,
                        //   hint: controller.selectedCoutry.value.fullNameEnglish.toString() == "null"
                        //       ? Text(
                        //           LanguageConstants.countryText.tr,
                        //           style: const TextStyle(color: Colors.black54),
                        //         )
                        //       : Text(
                        //           controller.selectedCoutry.value.fullNameEnglish.toString(),
                        //           style: const TextStyle(color: Colors.black),
                        //         ),
                        //   icon: Icon(
                        //     Icons.keyboard_arrow_down,
                        //     size: 28.iY,
                        //     color: Colors.black,
                        //   ),
                        //   onChanged: (value) {
                        //     debugPrint("value Is $value");
                        //     controller.selectedCoutry.value = value as CountryListModel;
                        //   },
                        // ),
                      ),
                      SizedBox(height: 16.iY),
                      Obx(() => controller.selectedCoutry.value.availableRegions == null
                          ? Container()
                          : Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 10.iX,
                              ),
                              width: double.infinity,
                              decoration: BoxDecoration(color: tileBackgroundColor, borderRadius: BorderRadius.circular(12.iY)),
                              child: CommonDropDownButton(
                                dropdownColor: backgroundColor,
                                items: controller.selectedCoutry.value.availableRegions!
                                    .map((value) => DropdownMenuItem<AvailableRegion>(
                                          value: value,
                                          child: Text(value.name.toString()),
                                        ))
                                    .toList(),
                                isExpanded: true,
                                hint: controller.selectedState.value.name.toString() == "null"
                                    ? Text(
                                        LanguageConstants.stateText.tr,
                                        style: const TextStyle(
                                          color: Colors.black54,
                                        ),
                                      )
                                    : Text(
                                        controller.selectedState.value.name.toString(),
                                        style: const TextStyle(color: Colors.black),
                                      ),
                                icon: Icon(
                                  Icons.keyboard_arrow_down,
                                  size: 28.iY,
                                  color: Colors.black,
                                ),
                                onChanged: (value) {
                                  controller.selectedState.value = value! as AvailableRegion;
                                },
                              ),
                            )),
                      SizedBox(height: 16.iY),
                      SizedBox(
                        child: CommonButton(
                          buttonType: ButtonType.elevatedButton,
                          color: darkBlueColor,
                          onPressed: () {
                            controller.isValidation = true;
                            controller.update(["Address"]);
                            if (Get.arguments['value'] == 0) {
                              controller.addAddress(
                                context,
                                controller.formKey,
                              );
                            } else {
                              controller.updateAddress(
                                context,
                                controller.formKey,
                              );
                            }
                          },
                          textColor: Colors.white,
                          borderRadius: 5.0,
                          child: CommonTextPoppins(
                            Get.arguments['value'] == 0 ? LanguageConstants.saveAddress.tr : LanguageConstants.updateAddress.tr,
                            height: 1,
                            fontSize: 14.0,
                            fontWeight: FontWeight.w500,
                            color: backgroundColor,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  InputDecoration addressDecoration({required String hintText, BorderRadius? borderRadius}) {
    return InputDecoration(
      filled: true,
      fillColor: tileBackgroundColor,
      hintText: hintText,
      labelStyle: const TextStyle(color: Colors.black54),
      errorStyle: const TextStyle(color: Colors.red),
      focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12.iY), borderSide: BorderSide.none),
      enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12.iY), borderSide: BorderSide.none),
      border: OutlineInputBorder(
        borderSide: BorderSide.none,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
      ),
    );
  }
}*/
