import 'package:app_one/all_imports.dart';
import 'package:app_one/app_pages/app_routes.dart';
import 'package:app_one/core/consts/app_constants.dart';
import 'package:app_one/screens/show_ticket_response/show_ticket_response.dart';
import 'package:base_controller/controllers/product_detail_controller.dart';
// import 'package:app_one/theme/app_asset.dart';
import 'package:meta_package/utils/validator.dart';
import 'package:presentation/const/app_text_style/presentation_app_text_style.dart';
import 'package:presentation/const/size_utils.dart';
import 'package:presentation/navigation/navigation.dart';
import 'package:presentation/widgets/common_alert_dialog/common_alert_dialog.dart';
import 'package:presentation/widgets/common_buttons/common_normal_button.dart';
import 'package:presentation/widgets/screen_loading.dart';
import 'package:presentation_common_ui/common_screens/common_best_price_promise_dialog/common_best_price_promise_dialog.dart';
import 'package:presentation_common_ui/common_widget/common_add_to_wish_list_button.dart';
import 'package:presentation_common_ui/common_widget/common_form_dialog_widget.dart';
import 'package:presentation_common_ui/common_widget/new_common_widget/cart_screen/common_cart_name_text_field.dart';
import 'package:presentation_common_ui/common_widget/new_common_widget/cart_screen/common_email_text_field.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';
import 'package:presentation_common_ui/theme/buttons_decoration_helper.dart';
import 'package:presentation_common_ui/theme/color_helper.dart';
import 'package:presentation_common_ui/theme/font_size_helper.dart';
import 'package:presentation_common_ui/theme/padding_helper.dart';
// import 'package:presentation_common_ui/widgets/home/<USER>/home_banner_widget.dart';
import 'package:presentation_common_ui/widgets/product_detail_screen/app_one/add_to_cart_button.dart';
import 'package:presentation_common_ui/widgets/product_detail_screen/app_one/all_product_image.dart';
import 'package:presentation_common_ui/widgets/product_detail_screen/app_one/choose_an_optionwidget.dart';
import 'package:presentation_common_ui/widgets/product_detail_screen/app_one/create_ticket_best_price_dialog.dart';
import 'package:presentation_common_ui/widgets/product_detail_screen/app_one/expantion_widget.dart';
import 'package:presentation_common_ui/widgets/product_detail_screen/app_one/image_carousel.dart';
import 'package:presentation_common_ui/widgets/product_detail_screen/app_one/recommentaton_widget.dart';
import 'package:presentation_common_ui/widgets/product_detail_screen/app_one/size_chart_widget.dart';
import 'package:presentation_common_ui/widgets/product_detail_screen/app_one/start_chat_button.dart';

import '../../app_constants/app_constants.dart';

class ProductDetailScreen extends GetView<ProductDetailController> {
  const ProductDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final ColorModal appColors = getColorModal(APP_NAME);
    final FontSizeModal appFontSize = getFontSizeModal(APP_NAME);
    final PaddingModal appPadding = getPaddingModal(APP_NAME);
    final ButtonsDecorationModal appButtonsDecoration =
        getButtonDecorationModal(APP_NAME);
    return Scaffold(
      body: Theme(
        data: Theme.of(context).copyWith(
            indicatorColor: appColors.primaryColor,
            dividerColor: Colors.transparent),
        child: Scaffold(
          // extendBodyBehindAppBar: true,
          // appBar: commonAppbar(title: "Product Details"),
          backgroundColor: appColors.backgroundColor,
          appBar: commonAppbar(
              backgroundColor: appColors.backgroundColor,
              titleWidget: CommonTextPoppins(
                LanguageConstants.productDetails.tr,
                fontSize: appFontSize.subTitleTextSize.iY,
                height: 2.iY,
              ),
              leadingWidget: IconButton(
                  onPressed: () {
                    Get.find<Navigation>().getBack<void>(null);
                  },
                  icon: Icon(
                    Icons.arrow_back,
                    color: Theme.of(context).iconTheme.color,
                  ))),
          body: Obx(
            () => controller.isLoading.value
                ? Center(
                    child: ScreenLoading(appColor: appColors.primaryColor),
                  )
                : _buildBody(
                    context,
                    appColors,
                    appFontSize,
                    appPadding,
                    appButtonsDecoration,
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody(
    BuildContext context,
    ColorModal appColors,
    FontSizeModal appFontSize,
    PaddingModal appPadding,
    ButtonsDecorationModal appButtonsDecoration,
  ) {
    return Stack(
      children: [
        SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: appPadding.mediumPadding.iY),
              const ImageCarousel(),
              Padding(
                padding: EdgeInsets.all(appPadding.mediumPadding.iY),
                child: Column(
                  children: [
                    controller.getMediaGalleryEntriesLength() > 0
                        ? const Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              AllProductImages(),
                            ],
                          )
                        : const SizedBox(),
                    SizedBox(height: appPadding.mediumPadding.iY),
                    SizedBox(
                      width: Get.width,
                      child: CommonTextPoppins(
                        '${LanguageConstants.sku.tr}: ${controller.product.value.sku}',
                        fontSize: appFontSize.subTitleTextSize.iY,
                        textAlign: TextAlign.start,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).textTheme.titleLarge!.color,
                        height: 2.iY,
                      ),
                    ),
                    SizedBox(height: appPadding.mediumPadding.iY),
                    controller.product.value.getBrandName().isNotEmpty
                        ? SizedBox(
                            width: Get.width,
                            child: CommonTextPoppins(
                              controller.product.value.getBrandName(),
                              fontSize: appFontSize.titleTextSize.iY,
                              textAlign: TextAlign.start,
                              fontWeight: FontWeight.w500,
                              color:
                                  Theme.of(context).textTheme.titleLarge!.color,
                              height: 2.iY,
                            ),
                          )
                        : const SizedBox.shrink(),
                    controller.product.value.getBrandName().isNotEmpty
                        ? SizedBox(height: appPadding.mediumPadding.iY)
                        : const SizedBox.shrink(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          child: CommonTextPoppins(
                            controller.product.value.name ??
                                '', //controller.product.value.name ??
                            fontSize: appFontSize.titleTextSize.iY,
                            maxLine: 3,
                            overflow: TextOverflow.ellipsis,
                            fontWeight: FontWeight.w500,
                            color:
                                Theme.of(context).textTheme.titleLarge!.color,
                            height: 2.iY,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: appPadding.mediumPadding.iY),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonTextPoppins(
                          "${controller.getConvertRegularPriceFromConfigurableProduct()}",
                          fontSize: appFontSize.titleTextSize.iY,
                          color: appColors.primaryColor,
                        ),
                        GestureDetector(
                          onTap: () {
                            CommonDialog.showFormCommonDialog(
                              endpoint: AppConstants.apiEndPointLogin,
                              context,
                              title: 'Create Missing Size Request Ticket',
                              formType: 'price_match',
                              buttonColor: appColors.primaryColor,
                              textStyle: AppTextStyle.textStyleUtils600(
                                  color: Theme.of(Get.context!)
                                      .textTheme
                                      .titleLarge!
                                      .color),
                              projectName: APP_NAME,
                            );
                          },
                          child: CommonTextPoppins(
                            LanguageConstants.priceMatch.tr,
                            decoration: TextDecoration.underline,
                            fontWeight: FontWeight.w400,
                            color: Theme.of(context).primaryColor,
                            fontSize: appFontSize.subTitleTextSize.iY,
                          ),
                        )
                      ],
                    ),
                    SizedBox(height: appPadding.mediumPadding.iY),

                    SizedBox(height: appPadding.mediumPadding.iY),
                    ExpantionWidget(
                      title: LanguageConstants.description.tr,
                      child: CommonTextPoppins(
                        controller.product.value.getProductDescription(),
                        fontSize: appFontSize.subTitleTextSize.iY,
                        maxLine: 3,
                        fontWeight: FontWeight.w400,
                        color: Theme.of(context).textTheme.titleLarge!.color,
                        height: 2.iY,
                      ),
                    ),
                    SizedBox(
                      height: appPadding.mediumPadding.iY,
                    ),
                    ExpantionWidget(
                      title: LanguageConstants.details.tr,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CommonTextPoppins(
                            "${LanguageConstants.composition.tr} : ${controller.product.value.getComposition()}",
                            fontSize: appFontSize.subTitleTextSize.iY,
                            fontWeight: FontWeight.w400,
                            color:
                                Theme.of(context).textTheme.titleLarge!.color,
                            height: 2.iY,
                          ),
                          SizedBox(height: appPadding.smallPadding.iY),
                          CommonTextPoppins(
                            "${LanguageConstants.color.tr} : ${controller.product.value.getColor()}",
                            fontSize: appFontSize.subTitleTextSize.iY,
                            fontWeight: FontWeight.w400,
                            color: blackColor,
                          ),
                          // SizedBox(height: 10.h),
                          // const SizeChartWidget(),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: appPadding.mediumPadding.iY,
                    ),
                    SizedBox(
                      height: appPadding.smallPadding,
                    ),
                    Visibility(
                      visible: //true,
                          controller.product.value.typeId == 'configurable',
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CommonTextPoppins(
                                'Size',
                                fontSize: appFontSize.titleTextSize.iY,
                                textAlign: TextAlign.start,
                                fontWeight: FontWeight.normal,
                                color: Theme.of(context).primaryColor,
                                height: 2.iY,
                              ),
                              const SizeChartWidget(),
                            ],
                          ),
                          SizedBox(
                            height: appPadding.commonHorizontalPadding.iY,
                          ),
                          Row(
                            children: [
                              const Expanded(
                                child: ChosseAnOpenWidget(),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: appPadding.largePadding.iY),
                    const Row(
                      children: [
                        AddToCartButton(
                          projectName: APP_NAME,
                          routeName: RoutesConstants.cartScreen,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: appPadding.commonHorizontalPadding.iY,
                    ),
                    Row(
                      children: [
                        Semantics(
                          label: "add Wish list",
                          button: true,
                          child: AddToWishlistButton(
                              isWishListed:
                                  controller.product.value.isWishList.value,
                              onTap: () async {
                                if (controller.product.value.isWishList.value ==
                                    true) {
                                  Get.rawSnackbar(
                                      message: 'Already added to wishlist');
                                } else {
                                  await controller
                                      .postAddToWishlistData(
                                    context,
                                    controller.product,
                                  )
                                      .then((value) {
                                    if (value == true) {
                                      CommonDialog.showWishListDialog(
                                        context,
                                        () => {},
                                      );
                                    }
                                  });
                                }
                              }),
                        )
                      ],
                    ),
                    SizedBox(
                      height: appPadding.commonHorizontalPadding.iY,
                    ),
                    GestureDetector(
                      onTap: () {
                        CommonDialog.showFormCommonDialog(
                          endpoint: AppConstants.apiEndPointLogin,
                          context,
                          title: 'Create Missing Size Request Ticket',
                          formType: 'missing_size_request',
                          buttonColor: appColors.primaryColor,
                          // textStyle: AppTextStyle.textStyleUtils600(color: Theme.of(Get.context!).textTheme.titleLarge!.color),
                          projectName: APP_NAME,
                        );
                      },
                      child: CommonTextPoppins(
                        'Missing size',
                        fontSize: appFontSize.minorElementsTextSize.iY,
                        // fontWeight: FontWeight.w600,
                        color:
                            Theme.of(Get.context!).textTheme.titleLarge!.color,
                      ),
                    ),
                    SizedBox(
                      height: appPadding.commonHorizontalPadding.iY,
                    ),
                    if (controller.productIsInStock())
                      SizedBox(
                        width: Get.width,
                        child: CustomOutlinedButton(
                          onTap: controller.productIsInStock()
                              ? () async {
                                  await controller.buyNowOnTap();
                                }
                              : null,
                          text: controller.productIsInStock()
                              ? LanguageConstants.buyNow.tr
                              : LanguageConstants.soldOut.tr,
                          buttonTextStyle:
                              appButtonsDecoration.primaryButtonTextStyle,
                          decoration:
                              appButtonsDecoration.primaryButtonDecoration,
                        ), /*ElevatedButton(
                            style: ElevatedButton.styleFrom(
                                backgroundColor: primaryColor,
                                disabledBackgroundColor: primaryColor,
                                elevation: 0,
                                // padding: EdgeInsets.symmetric(vertical: 10),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(50),
                                )),
                            onPressed: controller.productIsInStock()
                                ? () async {
                                    await controller.buyNowOnTap();
                                  }
                                : null,
                            child: CommonTextPoppins(
                              controller.productIsInStock()
                                  ? LanguageConstants.buyNow.tr
                                  : LanguageConstants.soldOut.tr,
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                              color: whiteColor,
                            ),
                          ),*/
                      ),
                    // if (!controller.productIsInStock())
                    //   Padding(
                    //     padding: EdgeInsets.only(top: 15.iY),
                    //     child: InkWell(
                    //       onTap: () {
                    //         Get.to<dynamic>(() => const OutOfStock());
                    //         /*  await controller.notifyOnClick();*/
                    //       },
                    //       child: SizedBox(
                    //         width: 327.iX,
                    //         child: CommonTextPoppins(
                    //           LanguageConstants.notifyMeWhenThisProductIsInStock.tr,
                    //           decoration: TextDecoration.underline,
                    //           color: blackColor,
                    //           fontWeight: FontWeight.w400,
                    //           fontSize: 14.iY,
                    //           textAlign: TextAlign.center,
                    //           height: 2.iY,
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    controller.estimatedTime.value.contains(':')
                        ? Row(
                            children: [
                              SizedBox(
                                width: (MediaQuery.of(context).size.width / 2) +
                                    15,
                                child: CommonTextPoppins(
                                  LanguageConstants.estimatedDeliveryDate.tr,
                                  fontSize:
                                      appFontSize.minorElementsTextSize.iY,
                                  // fontWeight: FontWeight.w600,
                                  color: blackColor,
                                ),
                              ),
                              const Spacer(),
                              CommonTextPoppins(
                                controller.estimatedTime.value.split(':').last,
                                fontSize: appFontSize.minorElementsTextSize.iY,
                                // fontWeight: FontWeight.w600,
                                color: blackColor,
                              ),
                            ],
                          )
                        : CommonTextPoppins(
                            controller.estimatedTime.value,
                            fontSize: appFontSize.minorElementsTextSize.iY,
                            // fontWeight: FontWeight.w600,
                            color: blackColor,
                          ),
                    SizedBox(
                      height: appPadding.largePadding.iY,
                    ),
                  ],
                ),
              ),
              controller.itemsData.isEmpty
                  ? const SizedBox()
                  : Padding(
                      padding: EdgeInsets.only(left: appPadding.mediumPadding),
                      child: CommonTextPoppins(
                        LanguageConstants.recommendation.tr,
                        fontWeight: FontWeight.w600,
                        fontSize: appFontSize.titleTextSize.iY,
                        color: blackColor,
                        height: 2.iY,
                      ),
                    ),
              SizedBox(
                height: appPadding.mediumPadding.iY,
              ),
              SizedBox(
                  child: controller.itemsData.isEmpty
                      ? Container()
                      : controller.itemsData[0].imageUrl == null
                          ? Center(
                              child: Text(
                                  controller.itemsData[0].message.toString()),
                            )
                          : const RecommentationWidget()),
              SizedBox(
                height: appPadding.largePadding.iY,
              ),
              // Padding(
              //   padding: const EdgeInsets.symmetric(horizontal: 24.0),
              //   child: HomeBannerWidget(imageUrl: AppAsset.homeBanner),
              // ),
              SizedBox(
                height: appPadding.largePadding.iY,
              ),
            ],
          ),
        ),
        controller.isAddToCartLoading.value
            ? ScreenLoading(
                appColor: appColors.primaryColor,
              )
            : const SizedBox(),
      ],
    );
  }

  Future showPriceMatch(BuildContext context) {
    return showDialog<dynamic>(
      context: context,
      builder: (BuildContext context) {
        return const CommonBestPricePromiseDialog(
          radius: 0,
          backgroundColor: whiteColor,
          insetPadding: EdgeInsets.zero,
        );
        // return CommonAlertDialog(
        //   radius: 0,
        //   backgroundColor: whiteColor,
        //   insetPadding: EdgeInsets.zero,
        //   // contentPadding: EdgeInsets.all(20.iY),
        //   contentWidget: Column(
        //     mainAxisSize: MainAxisSize.max,
        //     children: [
        //       Padding(
        //         padding: EdgeInsets.only(top: 20.iY, left: 20.iY, right: 20.iY, bottom: 10.iY),
        //         child: Row(
        //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //           children: [
        //             Text(
        //               LanguageConstants.bestPricePromise.tr,
        //               style: AppStyle.textStyleUtils400(size: 21.0, color: blackColor),
        //             ),
        //             IconButton(
        //               onPressed: () {
        //                 Get.back<dynamic>();
        //               },
        //               icon: const Icon(
        //                 Icons.close,
        //                 color: greyColor,
        //               ),
        //             )
        //           ],
        //         ),
        //       ),
        //       Divider(
        //         color: lightGreyColor.withOpacity(0.1),
        //         height: 1,
        //         thickness: 1,
        //       ),
        //       Container(
        //         margin: EdgeInsets.only(left: 20.iY, right: 20.iY, bottom: 10.iY),
        //         child: Column(
        //           mainAxisSize: MainAxisSize.min,
        //           children: [
        //             SizedBox(
        //               height: 20.iY,
        //             ),
        //             Padding(
        //               padding: EdgeInsets.only(top: 20.iY),
        //               child: Text(
        //                 LanguageConstants.yourBestPriceAlways.tr,
        //                 style: AppStyle.textStyleUtils400(color: greyColor).copyWith(fontSize: 18),
        //               ),
        //             ),
        //             Padding(
        //               padding: EdgeInsets.only(top: 20.iY),
        //               child: Text(
        //                 LanguageConstants.ifYourPreferredItemIsLowerPricedText.tr,
        //                 style: AppStyle.textStyleUtils400(color: greyColor),
        //                 textAlign: TextAlign.center,
        //               ),
        //             ),
        //             Padding(
        //               padding: EdgeInsets.only(top: 20.iY),
        //               child: GestureDetector(
        //                   onTap: () {
        //                     // Get.back<dynamic>();
        //                     // showPriceMatchTicketDialog(false);
        //                   },
        //                   child: RichText(
        //                       text: TextSpan(children: [
        //                     TextSpan(
        //                         style: AppStyle.textStyleUtils400(color: greyColor, size: 20.iY),
        //                         text: LanguageConstants.simplyLetUsKnowByClickingHere.tr.replaceFirst(LanguageConstants.clickingHere.tr, ' ')),
        //                     TextSpan(
        //                         style: AppStyle.textStyleUtils400(color: primaryColor, size: 20.iY),
        //                         text: LanguageConstants.clickingHere.tr,
        //                         recognizer: TapGestureRecognizer()
        //                           ..onTap = () async {
        //                             Get.back<dynamic>();
        //                             showPriceMatchTicketDialog(false);
        //                           }),
        //                   ]))
        //                   // child: Text(
        //                   //   LanguageConstants.simplyLetUsKnowByClickingHere.tr,
        //                   //   style: AppStyle.textStyleUtils400(color: primaryColor, size: 20.iY)
        //                   //       .copyWith(decoration: TextDecoration.underline),
        //                   //   textAlign: TextAlign.center,
        //                   // ),
        //                   ),
        //             ),
        //             Padding(
        //               padding: EdgeInsets.only(top: 20.iY),
        //               child: GestureDetector(
        //                   onTap: () {
        //                     Get.back<dynamic>();
        //                     showPriceMatchTicketDialog(true);
        //                   },
        //                   child: RichText(
        //                       textAlign: TextAlign.center,
        //                       text: TextSpan(children: [
        //                         TextSpan(
        //                           style: AppStyle.textStyleUtils400(color: greyColor, size: 20.iY),
        //                           text: LanguageConstants.alreadyMadeYourPurchase.tr,
        //                         ),
        //                         TextSpan(
        //                           style: AppStyle.textStyleUtils400(color: greyColor, size: 20.iY),
        //                           text: LanguageConstants.dontWorryLetUsKnowByClickingMsg.tr,
        //                         ),
        //                         // TextSpan(
        //                         //     style: AppStyle.textStyleUtils400(color: primaryColor, size: 20.iY),
        //                         //     text: LanguageConstants.clickingHere.tr,
        //                         //     recognizer: TapGestureRecognizer()
        //                         //       ..onTap = () async {
        //                         //         Get.back<dynamic>();
        //                         //         showPriceMatchTicketDialog(false);
        //                         //       }),
        //                       ]))
        //                   // child: Text(
        //                   //   "${LanguageConstants.alreadyMadeYourPurchase.tr} ${LanguageConstants.dontWorryLetUsKnowByClickingMsg.tr}",
        //                   //   style: AppStyle.textStyleUtils400(color: primaryColor)
        //                   //       .copyWith(decoration: TextDecoration.underline),
        //                   //   textAlign: TextAlign.center,
        //                   // ),
        //                   ),
        //             ),
        //             // Padding(
        //             //   padding: EdgeInsets.only(top: 15.iY),
        //             //   child: GestureDetector(
        //             //     onTap: () {
        //             //       //Get.toNamed<dynamic>(RoutesConstants.contactUsScreen);
        //             //     },
        //             //     child: Text(
        //             //       LanguageConstants.dontWorryLetUsKnowByClickingMsg.tr,
        //             //       style: AppStyle.textStyleUtils400(color: greyColor)
        //             //           .copyWith(decoration: TextDecoration.underline),
        //             //       textAlign: TextAlign.center,
        //             //     ),
        //             //   ),
        //             // ),
        //             Padding(
        //               padding: EdgeInsets.only(top: 20.iY, bottom: 15.iY),
        //               child: GestureDetector(
        //                   onTap: () {
        //                     Get.toNamed<dynamic>(RoutesConstants.faqScreen);
        //                   },
        //                   child: RichText(
        //                       text: TextSpan(children: [
        //                     TextSpan(style: AppStyle.textStyleUtils400(color: greyColor, size: 20.iY), text: LanguageConstants.pleaseSeeFAQsByClickingHere.tr),
        //                     // TextSpan(
        //                     //     style: AppStyle.textStyleUtils400(color: primaryColor, size: 20.iY),
        //                     //     text: LanguageConstants.clickingHere.tr,
        //                     //     recognizer: TapGestureRecognizer()
        //                     //       ..onTap = () async {
        //                     //         Get.toNamed<dynamic>(RoutesConstants.faqScreen);
        //                     //       }),
        //                   ]))
        //                   // child: Text(
        //                   //   LanguageConstants.pleaseSeeFAQsByClickingHere.tr,
        //                   //   style: AppStyle.textStyleUtils400(color: greyColor)
        //                   //       .copyWith(decoration: TextDecoration.underline),
        //                   //   textAlign: TextAlign.center,
        //                   // ),
        //                   ),
        //             ),
        //           ],
        //         ),
        //       ),
        //     ],
        //   ),
        // );
      },
    );
  }

  void showPriceMatchTicketDialog(bool isPurchased) {
    controller.firstNameController.text =
        controller.localStore.userDetail.firstname ?? '';
    controller.lastNameController.text =
        controller.localStore.userDetail.lastname ?? '';
    controller.productNameController.text = controller.product.value.name ?? '';
    controller.styleController.text = controller.product.value.sku ?? '';
    showDialog<dynamic>(
      context: Get.context!,
      builder: (BuildContext context) {
        return CreateTicketForBestPriceMatchDialog(
          phoneNumberController: controller.phoneNumberController,
          emailController: TextEditingController(
              text: controller.localStore.userDetail.email),
          firstNameController: controller.firstNameController,
          lastNameController: controller.lastNameController,
          urlOfImageController: controller.urlOfImageController,
          remarksController: controller.remarksController,
          isProductAvailableController: controller.isProductAvailableController,
          priceController: controller.priceController,
          linkCheaperProductController: controller.linkCheaperProductController,
          urlOfWebsiteController: controller.urlOfWebsiteController,
          styleController: controller.styleController,
          productNameController: controller.productNameController,
          keywordController: controller.keywordController,
          onTap: () async {
            String msg =
                await controller.createMyTicketForCancelItemOrder(isPurchased);
            Get.back<dynamic>();
            // showTicketResponseDialog(Get.context!, msg);
            CommonDialog.showTicketResponseDialog(Get.context!, message: msg);
          },
        );
      },
    );
  }

  void showTicketResponseDialog(BuildContext context, String message) {
    showDialog<dynamic>(
      context: context,
      builder: (BuildContext context) {
        return ShowTicketResponse(
          message: message,
        );
      },
    );
  }

  Future showTitleDialog(BuildContext context) {
    return showDialog<dynamic>(
        context: context,
        builder: (BuildContext context) {
          return CommonAlertDialog(
            backgroundColor: darkBlueColor,
            insetPadding: const EdgeInsets.all(10),
            contentWidget: Stack(
              clipBehavior: Clip.none,
              children: <Widget>[
                Positioned(
                  left: 0,
                  right: 0,
                  top: -45.0,
                  child: CircleAvatar(
                    radius: 45,
                    backgroundColor: darkBlueColor,
                    child: Image.asset(
                      AppAsset.account,
                      color: Colors.white,
                      width: 40.iX,
                      height: 40.iY,
                    ),
                  ),
                ),
                Form(
                  key: controller.formKeyChat,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Container(
                        //height: 70,
                        margin: EdgeInsets.only(top: 60.iY),
                        width: MediaQuery.of(context).size.width,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Center(
                              child: Text(
                                LanguageConstants.welcometoChatText.tr,
                                textAlign: TextAlign.center,
                                style: AppStyle.textStyleUtils400_16(),
                              ),
                            ),
                            SizedBox(
                              height: 5.iY,
                            ),
                            Center(
                              child: Text(
                                LanguageConstants.fillTheFormText.tr,
                                textAlign: TextAlign.center,
                                style: AppStyle.textStyleUtils400(),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            left: 20.iX, right: 20.iX, top: 10.iY),
                        child: Column(
                          children: [
                            Container(
                              height: 50.iY,
                              width: Get.width,
                              decoration: BoxDecoration(
                                color: backgroundColor,
                                border: Border.all(
                                  color: Theme.of(context)
                                          .textTheme
                                          .displayMedium
                                          ?.color ??
                                      Colors.white,
                                  width: 1,
                                ),
                              ),
                              child: CommonNameTextField(
                                controller: controller.firstNameController,
                                hintText: LanguageConstants.nameChatText.tr,
                                textAlign: TextAlign.center,
                                validator: (value) =>
                                    Validators.validateRequired(
                                        value?.trim() ?? '',
                                        LanguageConstants.name.tr),
                              ),

                              ///COMBINE WIDGET
                              // child: NameTextField(
                              //   controller: controller.firstNameControllerChat,
                              //   hintText: LanguageConstants.nameChatText.tr,
                              //   textAlign: TextAlign.center,
                              //   validator: (value) => Validators.validateRequired(value?.trim() ?? '', LanguageConstants.name.tr),
                              // ),
                            ),
                            SizedBox(
                              height: 10.iY,
                            ),
                            Container(
                              height: 50.iY,
                              width: Get.width,
                              decoration: BoxDecoration(
                                color: backgroundColor,
                                border: Border.all(
                                  color: Theme.of(context)
                                          .textTheme
                                          .displayMedium
                                          ?.color ??
                                      Colors.white,
                                  width: 1,
                                ),
                              ),
                              child: CommonEmailTextField(
                                controller: controller.emailController,
                                hintText: LanguageConstants.emailIsRequired.tr,
                                textAlign: TextAlign.center,
                                validator: (value) => Validators.validateEmail(
                                    value?.trim() ?? ''),
                                contentPadding: const EdgeInsets.symmetric(
                                    vertical: 5.0, horizontal: 10.0),
                                isSubmitButtonPressed:
                                    controller.isSubmitButtonPressed,
                              ),

                              ///COMBINE WIDGET
                              // child: const EmailTextField(),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 20.iY, bottom: 20.iY),
                        child: const StartChatButton(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }
}
