import 'package:app_one/app_constants/app_constants.dart';
import 'package:base_controller/controllers/my_account_information_controller.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:presentation_common_ui/common_screens/common_my_account_page/common_my_account_info_screen.dart';

import '../../all_imports.dart';


class MyAccountInfoScreen extends GetView<MyAccountInformationController> {
  const MyAccountInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return
      // Obx(() =>
        CommonMyAccountInformationScreen(
          // formKey: controller.formKey,
          // firstNameController: controller.firstNameController,
          // lastNameController: controller.lastNameController,
          // emailController: controller.emailController,
          // phoneController: controller.telephoneController,
          // birthDateController: controller.dateOfBirthController,
          // mrms: controller.mrms,
          // titleText: LanguageConstants.accountInformationText.tr,
            // appBarTitleTextStyle: AppStyle.commonTextStyle500(size: 36,color: Theme.of(context).primaryColor),
          // fieldTitleTextStyle: AppStyle.commonTextStyle600(size: 14,color: Theme.of(context).textTheme.titleLarge?.color),

          // personalInfoText: LanguageConstants.personalInformationText.tr,
          // personalInfoTextStyle: AppStyle.commonTextStyle500(size: 20,color: Theme.of(context).textTheme.titleLarge?.color),

          // hintTextStyle: AppStyle.commonTextStyle500(size: 16,color: Theme.of(context).textTheme.titleLarge?.color?.withOpacity(0.5)),
          // fieldTextStyle: AppStyle.textStyleUtils500(size: 14, color: Theme.of(context).textTheme.titleLarge?.color),
          // buttonColor: Theme.of(context).primaryColor,
          // buttonTextStyle: AppStyle.commonTextStyle600(size: 16,color:whiteColor),
          // fieldBorderColor: Theme.of(context).inputDecorationTheme.border?.borderSide.color,
          // fieldBorderRadius: 4.0,
          // dropDownColor: greyColor,

          // buttonRadius: 50.0,
          // buttonWidth: 180,
          // onPressedSubmitButton: (){
          //   if(controller.formKey.currentState!.validate()){
          //     print("Done");
          //   }
          // },
          projectName: APP_NAME,
    )
     // )
    ;
  }
}

