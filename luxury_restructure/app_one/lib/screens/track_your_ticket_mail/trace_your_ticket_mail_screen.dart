import 'package:presentation/const/size_utils.dart';
import 'package:base_controller/controllers/track_your_ticket_mail_controller.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';
import '../../all_imports.dart';
import 'package:presentation_common_ui/common_screens/common_track_your_ticket_by_email_screen/common_track_your_ticket_by_email_screen.dart';

import '../../app_constants/app_constants.dart';

class TraceYourTicketMail extends GetView<TrackYourTicketEmailController> {
  const TraceYourTicketMail({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return CommonTrackYourRequestByEMail(
        projectName: APP_NAME,
        passwordTextFieldSuffixIcon: controller.isPasswordVisible.value
            ? AppAsset.visibilityOn
            : AppAsset.visibilityOff,
        isPasswordVisible: controller.isPasswordVisible,
        forgotPassTextStyle: AppStyle.textStyleUtils500(
            size: 14, color: Theme.of(context).primaryColor),
        subTitleColor:
            Theme.of(context).textTheme.titleLarge?.color?.withOpacity(0.5),
        registerCustomerTextPadding: 20,
        loginUpButtonWidth: MediaQuery.of(context).size.width,
      );
      /*CommonTrackYourRequestByEMail(
        formKey: controller.formKey,
        guestFormKey: controller.guestFormKey,

        onTapLogin: () {
          // controller.trackYourRequest();
          controller.login();
          if (controller.formKey.currentState!.validate()) {
            print('DONE');
          }
        },
        onTapLetsGo: () {
          if (controller.guestFormKey.currentState!.validate()) {
            debugPrint('Guest DONE');
          }
        },
        registeredUserEmailController: controller.registeredUserEmailController,
        guestUserEmailController: controller.guestUserEmailController,
        passwordController: controller.passwordController,

        /// main title
        trackYourRequestText: LanguageConstants.trackYourTicketByEmail.tr,
        titleTextAlignment: Alignment.center,
        titleTextStyle: AppStyle.textStyleUtils500(size:24, color: Theme.of(context).primaryColor),

        /// text field text
        fieldTextStyle: AppStyle.textStyleUtils400(color: Theme.of(context).textTheme.titleLarge?.color),


        /// register customer text and its sub text
        registerCustomerText: LanguageConstants.registeredCustomers.tr,
        registerCustomerTextStyle: AppStyle.textStyleUtils500(size: 26.iY, color: Theme.of(context).primaryColor),
        registerSubTitleText: LanguageConstants.ifYouHaveAnAccountSignInWithYourEmailAddress.tr,
        registerSubTitleTextStyle: AppStyle.textStyleUtils500(size:16.iY,color: Theme.of(context).textTheme.titleLarge?.color?.withOpacity(0.5)),
        registerCustomerTextPadding: 20,

        /// text field

        emailHintText: "${LanguageConstants.emailAddreessText.tr} *",
        passwordHintText: LanguageConstants.password.tr,
        textFieldTitleTextStyle: AppStyle.textStyleUtils600_14(color: Theme.of(context).textTheme.titleLarge?.color),
        hintTextStyle: AppStyle.textStyleUtils500(size: 14, color: Theme.of(context).textTheme.titleLarge?.color?.withOpacity(0.4)),
        isPasswordVisible: controller.isPasswordVisible,
        passwordTextFieldSuffixIcon: controller.isPasswordVisible.value ?  AppAsset.visibilityOn : AppAsset.visibilityOff,
        onTapSuffixIcon: () {
          controller.isPasswordVisible(!controller.isPasswordVisible.value);
        },


        /// forgot password text
        forgotPassText: LanguageConstants.forgotYourPasswordText.tr,
        forgotPassTextStyle: AppStyle.textStyleUtils500(size:14,color: Theme.of(context).primaryColor),

        /// login button
        loginButtonText: LanguageConstants.loginText.tr,
        loginButtonTextStyle: AppStyle.textStyleUtils700(size: 14, color: Theme.of(context).textTheme.bodySmall?.color),
        loginDecoration: BoxDecoration(borderRadius: BorderRadius.circular(50), color: Theme.of(context).primaryColor),

        loginButtonStyle: OutlinedButton.styleFrom(
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50.0))),
        loginUpButtonWidth: MediaQuery.of(context).size.width,

        /// guest user text
        guestUserText: LanguageConstants.guestUsers.tr,
        guestUserSubTitleText: LanguageConstants.pleaseEnterYourEmailToTrackYourOrder.tr,

        /// lets go button
        letsGoText: LanguageConstants.submitText.tr,
        textFieldDecoration: InputDecoration(
          border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Theme.of(context).dividerColor)
          ),
          enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Theme.of(context).dividerColor)
          ),
          disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Theme.of(context).dividerColor)
          ),
        ),

      );*/
    });
  }
}
