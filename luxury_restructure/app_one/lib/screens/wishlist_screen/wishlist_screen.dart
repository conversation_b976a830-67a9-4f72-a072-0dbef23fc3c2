import 'package:app_one/app_constants/app_constants.dart';
import 'package:base_controller/controllers/wishlist_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:presentation_common_ui/common_screens/common_wishlist_screen/common_wishlist_screen.dart';

class WishListScreen extends GetView<WishListController> {
  final Function? backToShop;

  const WishListScreen({Key? key, this.backToShop}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const CommonWishListScreen(
      projectName: APP_NAME,
    );
    // return Scaffold(
    //   backgroundColor: whiteColor,
    //   appBar: commonAppbar(
    //       backgroundColor: Colors.transparent,
    //       titleWidget: CommonTextPoppins(
    //         LanguageConstants.myWishlistText.tr,
    //         fontSize: 22.iY,
    //         fontWeight: FontWeight.w500,
    //         height: 2.iY,
    //         color: primaryColor,
    //       )),
    //   body: Obx(
    //     () => controller.isLoading.value
    //         ? Center(
    //             child: AbsorbPointer(
    //               absorbing: true,
    //               child: Center(
    //                 child: BackdropFilter(
    //                   filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
    //                   child: const ScreenLoading(appColor: darkBlueColor),
    //                 ),
    //               ),
    //             ),
    //           )
    //         : RefreshIndicator(
    //             onRefresh: () async {
    //               controller.onInit();
    //             },
    //             child: ListView(
    //               shrinkWrap: true,
    //               children: [
    //                 WishlistListItem(backToShop: backToShop),
    //                 if (!controller.isWishListEmptyOrNot())
    //                   Column(
    //                     children: [
    //                       SizedBox(
    //                         height: 30.iY,
    //                       ),
    //                       Row(
    //                         mainAxisAlignment: MainAxisAlignment.center,
    //                         children: [
    //                           ElevatedButton(
    //                               onPressed: () async {},
    //                               style: ElevatedButton.styleFrom(
    //                                 elevation: 0,
    //                                 backgroundColor: Colors.white,
    //                                 padding: EdgeInsets.symmetric(vertical: 16, horizontal: 20),
    //                                 shape: RoundedRectangleBorder(
    //                                   side: BorderSide(color: primaryColor, width: 2),
    //                                   borderRadius: BorderRadius.circular(50),
    //                                 ),
    //                               ),
    //                               child: CommonTextPoppins(
    //                                 // LanguageConstants.share.tr,
    //                                 "Share Wishlist",
    //                                 overflow: TextOverflow.ellipsis,
    //                                 fontSize: 14,
    //                                 fontWeight: FontWeight.w600,
    //                                 color: darkBlueColor,
    //                               )),
    //                         ],
    //                       ),
    //                       SizedBox(
    //                         height: 30.iY,
    //                       ),
    //                     ],
    //                   ),
    //                 controller.isScreenLoading.value
    //                     ? const Center(
    //                         child: ScreenLoading(appColor: darkBlueColor),
    //                       )
    //                     : const SizedBox.shrink()
    //               ],
    //             ),
    //           ),
    //   ),
    // );
  }
}
