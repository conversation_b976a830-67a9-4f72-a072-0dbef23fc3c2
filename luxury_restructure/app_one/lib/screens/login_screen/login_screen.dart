import 'package:app_one/app_constants/app_constants.dart';
import 'package:presentation/const/app_colors/presentation_colors.dart' as PresentationColors;
import 'package:base_controller/controllers/all_imports_controllers.dart';
import 'package:presentation_common_ui/common_screens/common_login_screen/common_login_screen.dart';
import '../../all_imports.dart';

class LoginScreen extends GetView<LoginController> {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const CommonLoginScreen(
      projectName: APP_NAME,
    );
  }
}
