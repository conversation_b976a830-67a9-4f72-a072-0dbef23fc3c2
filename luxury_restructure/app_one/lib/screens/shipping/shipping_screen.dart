import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:presentation/const/size_utils.dart';
// import 'package:presentation/widgets/common_app_bar/common_app_bar.dart';
// import '../../all_imports.dart';
import 'package:flutter/material.dart';
import 'package:base_controller/controllers/shipping_controller.dart';
import '../../app_constants/app_constants.dart';
import '../../theme/colors.dart';
import 'package:presentation_common_ui/widgets/shipping/app_one/shipping_pageview.dart';
import 'package:presentation_common_ui/common_screens/common_shipping_screen/common_shipping_screen.dart';

class ShippingScreen extends GetView<ShippingController> {
  const ShippingScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CommonShippingScreen(
      projectName: APP_NAME,
      appName: 'AppOne',
    );
    /*Scaffold(
      appBar: commonAppbar(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          titleWidget: CommonTextPoppins(
            LanguageConstants.shipping.tr,
            color: Theme.of(context).textTheme.displayMedium?.color,
            height: 2.iY,
          ),
          leadingWidget: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: Icon(
                Icons.arrow_back,
                color: Theme.of(context).textTheme.displayMedium!.color ??
                    blackColor,
              ))),
      body: Obx(
        () => controller.isLoading.value
            ? _buildLoader()
            : const ShippingPageView(),
      ),
    )*/
    ;
  }

  /*Center _buildLoader() {
    return const Center(
      child: SpinKitThreeBounce(
        color: darkBlueColor,
      ),
    );
  }*/
}
