import 'package:app_one/app_constants/app_constants.dart';
import 'package:app_one/app_pages/app_routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:presentation/const/app_colors/app_one_colors.dart';
import 'package:presentation/const/app_text_style/app_one_app_text_style.dart';
import 'package:presentation/const/size_utils.dart';
import 'package:presentation/widgets/common_alert_dialog/common_alert_dialog.dart';
import 'package:presentation/widgets/common_app_bar/common_app_bar.dart';
import 'package:presentation/widgets/common_text/common_text_poppins.dart';
import 'package:presentation/widgets/screen_loading/screen_loading.dart';
import 'package:base_controller/controllers/brand_list_detail_controller.dart';
import 'package:app_one/core/utils/lang_directory/language_constant.dart';
import 'package:presentation_common_ui/widgets/brand_list_detail_screen/app_one/product_list_textfields.dart';
import 'package:presentation_common_ui/widgets/brand_list_detail_screen/app_one/product_filter_widget.dart';
import 'package:presentation_common_ui/widgets/brand_list_detail_screen/app_one/products_widget.dart';
import 'package:presentation_common_ui/widgets/product_detail_screen/app_one/start_chat_button.dart';
// import 'package:app_one/theme/app_asset.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';
import 'package:meta_package/api/models/product/product_model.dart';
import 'package:meta_package/utils/validator.dart';
import 'package:presentation_common_ui/common_widget/new_common_widget/brand_list_detail_screen/common_name_text_field.dart';
import 'package:presentation_common_ui/common_screens/common_brand_list_details_screen/common_brand_list_details_screen.dart';



class BrandListDetailScreen extends GetView<BrandListDetailController> {
  const BrandListDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const CommonBrandListDetailsScreen(
      projectName: APP_NAME,
    );
    // return Obx(
    //   () => Scaffold(
    //     resizeToAvoidBottomInset: false,
    //     backgroundColor: whiteColor,
    //     appBar: commonAppbar(
    //       backgroundColor: Colors.transparent,
    //       titleWidget: CommonTextPoppins(
    //         controller.title.value,
    //         // height: 2.iY,
    //         fontSize: 22.iY,
    //         color: primaryColor,
    //       ),
    //       leadingWidget: IconButton(
    //           onPressed: () {
    //             Get.back();
    //           },
    //           icon: Icon(
    //             Icons.arrow_back,
    //             color: blackColor,
    //           )),
    //     ),
    //     body: Padding(
    //       padding: EdgeInsets.only(top: MediaQuery.of(context).size.height * .02),
    //       child: Obx(
    //         () => controller.isLoading.value
    //             ? const Center(
    //                 child: ScreenLoading(appColor: darkBlueColor),
    //               )
    //             : NotificationListener<ScrollNotification>(
    //                 onNotification: controller.scrollNotificationData,
    //                 child: Padding(
    //                   padding: EdgeInsets.symmetric(horizontal: 12.5.iY),
    //                   child: Center(
    //                     child: Column(
    //                       children: [
    //                         controller.productModel.value.items?.isNotEmpty ?? false
    //                             ? const ProductFilterWidget()
    //                             : const SizedBox(),
    //                         SizedBox(height: 15.iY),
    //                         // FilterDropDown(),
    //                         Expanded(
    //                           child: ProductsWidget(),
    //                         ),
    //                       ],
    //                     ),
    //                   ),
    //                 ),
    //               ),
    //       ),
    //     ),
    //   ),
    // );
  }

  Future showTitleDialog(BuildContext context) {
    return showDialog<dynamic>(
        context: context,
        builder: (BuildContext context) {
          return CommonAlertDialog(
            backgroundColor: backgroundColor,
            insetPadding: EdgeInsets.all(10.iY),
            contentWidget: Stack(
              clipBehavior: Clip.none,
              children: <Widget>[
                Positioned(
                  left: 0,
                  right: 0,
                  top: -45.iY,
                  child: CircleAvatar(
                    radius: 45,
                    backgroundColor: appColor,
                    child: Image.asset(
                      AppAsset.account,
                      color: Colors.white,
                      width: 40.iX,
                      height: 40.iY,
                    ),
                  ),
                ),
                Form(
                  key: controller.formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Container(
                        //height: 70,
                        margin: EdgeInsets.only(top: 60.iY),
                        width: MediaQuery.of(context).size.width,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Center(
                              child: Text(
                                LanguageConstants.welcometoChatText.tr,
                                textAlign: TextAlign.center,
                                style: AppStyle.textStyleUtils400_16(),
                              ),
                            ),
                            SizedBox(
                              height: 5.iY,
                            ),
                            Center(
                              child: Text(
                                LanguageConstants.fillTheFormText.tr,
                                textAlign: TextAlign.center,
                                style: AppStyle.textStyleUtils400(),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 20.iX, right: 20.iX, top: 10.iY),
                        child: Column(
                          children: [
                            Container(
                              height: 50.iY,
                              width: Get.width,
                              decoration: BoxDecoration(
                                color: backgroundColor,
                                border: Border.all(
                                  color: appColor,
                                  width: 1,
                                ),
                              ),
                              child: CommonNameTextField(
                                controller: controller.firstNameController,
                                hintText: LanguageConstants.nameChatText.tr,
                                textAlign: TextAlign.center,
                                validator: (value) => Validators.validateRequired(value?.trim() ?? '', LanguageConstants.name.tr),
                              ),
                              ///COMBINE WIDGET
                              // child: NameTextField(
                              //   controller: controller.firstNameController,
                              //   hintText: LanguageConstants.nameChatText.tr,
                              //   textAlign: TextAlign.center,
                              //   validator: (value) =>
                              //       Validators.validateRequired(value?.trim() ?? '', LanguageConstants.name.tr),
                              // ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            Container(
                              height: 50.iY,
                              width: Get.width,
                              decoration: BoxDecoration(
                                color: backgroundColor,
                                border: Border.all(
                                  color: appColor,
                                  width: 1,
                                ),
                              ),
                              child: EmailTextField(emailController: controller.emailController,),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 20.iY, bottom: 20.iY),
                        child: const StartChatButton(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }
}

extension StringExtension on String {
  String toCapitalized() => length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';

  String toTitleCase() => replaceAll(RegExp(' +'), ' ').split(' ').map((str) => str.toCapitalized()).join(' ');
}
