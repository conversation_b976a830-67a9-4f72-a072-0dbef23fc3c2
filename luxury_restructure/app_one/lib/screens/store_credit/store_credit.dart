import 'package:base_controller/controllers/store_credit_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:meta_package/meta_package.dart';
import 'package:presentation/const/app_text_style/app_one_app_text_style.dart';
import 'package:presentation/const/size_utils.dart';
import 'package:presentation_common_ui/common_screens/common_my_store_credit_screen/common_my_store_credit_screen.dart';
import 'package:presentation_common_ui/constants/app_constants.dart';
import 'package:presentation_common_ui/theme/app_one//app_asset.dart';

class StoreCreditScreen extends GetView<StoreCreditController> {
  const StoreCreditScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final List<String> addressList = [LanguageConstants.addressBook.tr];

    StoreCreditController controller = Get.find<StoreCreditController>();
    return CommonStoreCreditScreen(
      projectName: APP_ONE_NAME,
      // shoppingBilling: controller.shoopingbiling.value,
      // noData: controller.nodata.value,
      // messageData: controller.messageData.value,
      // getStoreCreditList: controller.getStoreCreditList?.value??[],
      // addressList: addressList,
      // outerListContainerColor: Theme.of(context).dividerColor.withOpacity(0.2),
      // contentText: 'Our website give you the opportunity to spend store credit in addition to real currency. you can use Store Discount for future  purchases at our store',
      storeCreditIcon: AppAsset.storeCredit,
      // myStoreCreditTitleTextStyle: AppStyle.textStyleUtils600(size: 28.iY,color: Theme.of(context).primaryColor),
      // contentTextStyle:  AppStyle.textStyleUtils400(size: 18.iY,color:  Theme.of(context).textTheme.titleLarge?.color?.withOpacity(0.5)),
      // dropDownTextStyle:  AppStyle.textStyleUtils500(size: 20.iY,color: Theme.of(context).primaryColor),
      // keyTextStyle:  AppStyle.textStyleUtils600(size: 14.iY,color: Theme.of(context).textTheme.titleLarge?.color),
      // valueTextStyle:  AppStyle.textStyleUtils400(size: 14.iY,color:  Theme.of(context).textTheme.titleLarge?.color?.withOpacity(0.5)),
      // emptyListMessage: 'You have made no transaction',
      // emptyListMessageStyle: AppStyle.textStyleUtils400(size: 24.iY,color:  Theme.of(context).textTheme.titleLarge?.color?.withOpacity(0.5)),
    );
  }
}
