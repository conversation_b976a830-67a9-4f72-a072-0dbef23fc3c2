import 'package:app_one/core/consts/constants.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:app_one/all_imports.dart';
import 'package:base_controller/controllers/add_address_pop_up_screen_controller.dart';
import 'package:app_one/screens/add_address_pop_up_screen/add_new_dialog.dart';
// import 'package:app_one/theme/app_asset.dart';
import 'package:presentation/const/app_text_style/presentation_app_text_style.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';
class AddAddressPopUp extends GetView<AddAddressPopUpScreenController> {
  const AddAddressPopUp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
    return Obx(() => Scaffold(
          key: scaffoldKey,
          backgroundColor: backgroundColor,
          // appBar: AppBar(
          //   backgroundColor: backgroundColor,
          //   elevation: 0,
          //   iconTheme: const IconThemeData(color: Colors.black),
          //   centerTitle: true,
          //   leading: InkWell(
          //     onTap: () {
          //       scaffoldKey.currentState?.openDrawer();
          //     },
          //     child: Padding(
          //       padding: const EdgeInsets.all(16.0),
          //       child: SvgPicture.asset(ImageConstant.menuIcon),
          //     ),
          //   ),
          //   actions: [
          //     InkWell(
          //       onTap: () {},
          //       child: Padding(
          //         padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
          //         child: SvgPicture.asset(ImageConstant.searchIcon),
          //       ),
          //     ),
          //     InkWell(
          //       onTap: () {},
          //       child: Padding(
          //         padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
          //         child: SvgPicture.asset(ImageConstant.heartIcon),
          //       ),
          //     ),
          //     InkWell(
          //       onTap: () {},
          //       child: Padding(
          //         padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
          //         child: SvgPicture.asset(ImageConstant.shoppingCartIcon, color: Colors.black),
          //       ),
          //     ),
          //   ],
          //   title: Image.asset(AppAsset.logo, width: 110),
          // ),
          appBar: commonAppbar(
              backgroundColor: backgroundColor,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.black),
            centerTitle: true,
            leadingWidget: InkWell(
              onTap: () {
                scaffoldKey.currentState?.openDrawer();
              },
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: SvgPicture.asset(ImageConstant.menuIcon),
              ),
            ),
            action: [
              InkWell(
                onTap: () {},
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
                  child: SvgPicture.asset(ImageConstant.searchIcon),
                ),
              ),
              InkWell(
                onTap: () {},
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
                  child: SvgPicture.asset(ImageConstant.heartIcon),
                ),
              ),
              InkWell(
                onTap: () {},
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
                  child: SvgPicture.asset(ImageConstant.shoppingCartIcon, color: Colors.black),
                ),
              ),
            ],
            titleWidget: Image.asset(AppAsset.logo, width: 110),
          ),
          drawer: const Drawer(),
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        addNewDialog(context, controller);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(10.0),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: appColor,
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                        child: Text(LanguageConstants.addNew.tr,
                            style: AppTextStyle.textStyleLabel(
                                color: Colors.white, fontWeight: FontWeight.w500, size: 16)),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
  }
}
