import 'package:app_two/app_constants/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:presentation_common_ui/common_screens/common_exchange_and_return_screen/common_exchange_and_return_screen.dart';

class ExchangeAndReturnScreen extends StatelessWidget {
  const ExchangeAndReturnScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const CommonExchangeAndReturnScreen(
      projectName: APP_NAME,
    );
  }
}
