import 'package:presentation/const/app_colors/app_one_colors.dart';
import 'package:presentation/const/app_text_style/presentation_app_text_style.dart';
import 'package:presentation/const/size_utils.dart';
import '../../all_imports.dart';

class AboutUsScreen extends StatelessWidget {
  const AboutUsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: commonAppbar(
        titleWidget: CommonTextPoppins(LanguageConstants.aboutUsText.tr,
            color:
                Theme.of(context).textTheme.displayMedium?.color ?? blackColor),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        leadingWidget: IconButton(
          onPressed: () {
            Get.back();
          },
          icon: Icon(
            Icons.arrow_back_outlined,
            color: Theme.of(context).textTheme.titleLarge!.color,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            children: [
              const SizedBox(height: 30),
              Text(firstCapitalize(LanguageConstants.aboutUsTitle.tr),
                  textAlign: TextAlign.center,
                  style: AppTextStyle.textStyleUtils500(
                    size: 16.iX,
                    color: Theme.of(context).textTheme.titleLarge!.color,
                  )),
              SizedBox(height: 10.iY),
              Text(firstCapitalize(LanguageConstants.aboutUsContain.tr),
                  textAlign: TextAlign.center,
                  style: AppTextStyle.textStyleUtils400(
                    size: 16.iX,
                    color: Theme.of(context).textTheme.titleLarge!.color,
                  )),
              SizedBox(height: 10.iY),
              Text(firstCapitalize(LanguageConstants.aboutUsContain1.tr),
                  textAlign: TextAlign.center,
                  style: AppTextStyle.textStyleUtils500(
                    size: 16.iX,
                    color: Theme.of(context).textTheme.titleLarge!.color,
                  )),
              SizedBox(height: 27.iY),
              Text(firstCapitalize(LanguageConstants.aboutUsBrandsSecond.tr),
                  textAlign: TextAlign.center,
                  style: AppTextStyle.textStyleUtils400(
                    color: Theme.of(context).textTheme.titleLarge!.color,
                  )),
              SizedBox(height: 20.iY),
              Text(firstCapitalize(LanguageConstants.aboutUsContain3.tr),
                  textAlign: TextAlign.center,
                  style: AppTextStyle.textStyleUtils400(
                    color: Theme.of(context).textTheme.titleLarge!.color,
                  )),
              SizedBox(height: 20.iY),
              Text(firstCapitalize(LanguageConstants.aboutUsBrandsFourth.tr),
                  textAlign: TextAlign.center,
                  style: AppTextStyle.textStyleUtils400(
                    color: Theme.of(context).textTheme.titleLarge!.color,
                  )),
              SizedBox(height: 20.iY),
              Text(firstCapitalize(LanguageConstants.aboutUsContain5.tr),
                  textAlign: TextAlign.center,
                  style: AppTextStyle.textStyleUtils400(
                    color: Theme.of(context).textTheme.titleLarge!.color,
                  )),
              SizedBox(height: 20.iY),
              Text(firstCapitalize(LanguageConstants.aboutUsBrandsSixth.tr),
                  textAlign: TextAlign.center,
                  style: AppTextStyle.textStyleUtils400(
                    color: Theme.of(context).textTheme.titleLarge!.color,
                  )),
              SizedBox(height: 20.iY),
              Text(firstCapitalize(LanguageConstants.aboutUsBrandsSeventh.tr),
                  textAlign: TextAlign.center,
                  style: AppTextStyle.textStyleUtils400(
                    color: Theme.of(context).textTheme.titleLarge!.color,
                  )),
              SizedBox(height: 90.iY),
            ],
          ),
        ),
      ),
    );
  }

  String firstCapitalize(String name) {
    if (name != "") {
      return name.split(" ").map((str) => str.capitalize).join(" ");
    } else {
      return "";
    }
  }
}
