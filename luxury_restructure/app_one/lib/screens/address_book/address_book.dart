import 'package:presentation_common_ui/common_screens/common_address_book_screen/common_address_book_screen.dart';
import 'package:presentation_common_ui/common_ui/common_address_book_screen/brand_lable_address_book_screen.dart';

import '../../all_imports.dart';
import '../../app_pages/app_routes.dart';
import 'package:base_controller/controllers/address_book_controller.dart';
import 'package:presentation_common_ui/theme/app_two/app_asset.dart';
import 'package:app_one/app_constants/app_constants.dart';

class AddressBookScreen extends GetView<AddressBookController> {
  const AddressBookScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get.lazyPut(() => AddressBookController(
    //     getAddressListUseCase: GetAddressList(
    //         addAddressService:
    //         AddAddressService(AppConstants.apiEndPointLogin)),
    //     addAddressUsecase: AddAddress(
    //         addAddressService:
    //         AddAddressService(AppConstants.apiEndPointLogin)),
    //      deleteAddressUsecase: DeleteAddress(
    //         addAddressService:
    //             AddAddressService(AppConstants.apiEndPointLogin)),
    //     updateAddressUsecase: UpdateAddress(
    //         addAddressService:
    //             AddAddressService(AppConstants.apiEndPointLogin)),
    //     billingAddressUsecase: BillingAddress(
    //         addAddressService:
    //             AddAddressService(AppConstants.apiEndPointLogin)),
    //         ));
    return CommonAddressBookScreen(
      nothingToShowAnimationPath: AppAsset.nothigToShowAnimation,
      clickOnAddAddressButton: () async {
        dynamic result = await Get.toNamed<dynamic>(
          RoutesConstants.addAdressScreen,
          arguments: {
            "myAccountDetails": controller.getAdressList.value,
            "address": "",
            "flag": 0,
          },
        );
        if (result != null && result is bool && result) {
          await controller.getAddressList();
        }
      },
      userIcon: AppAsset.navProfile,
      locationIcon: AppAsset.location,
      phoneIcon: AppAsset.phone,
      editIcon: AppAsset.editPenIcon,
      deleteIcon: AppAsset.deleteIcon,
    );
/*     Obx(() =>
  BrandLabelAddressBookScreen(
  scaffoldKey: controller.scaffoldKey.value,
  isLoading: controller.isLoading.value,
  isDefaultLoading: controller.isDefaultLoading.value,
  getAdressList: controller.getAdressList.value,
  clickOnAddAddressButton: ()async{
  dynamic result = await Get.toNamed<dynamic>(RoutesConstants.addAdressScreen,
  arguments: [controller.getAdressList.value, 0, 0]);
  if (result != null && result is bool && result) {
  controller.getAddressList();
  }
  }));*/
  }
}
