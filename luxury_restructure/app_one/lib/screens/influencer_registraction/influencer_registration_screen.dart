import 'package:app_one/app_constants/app_constants.dart';
import 'package:presentation_common_ui/common_screens/common_influencer_registration/common_influencer_registration.dart';
import 'package:app_one/all_imports.dart';
import 'package:base_controller/controllers/influencer_registration_controller.dart';

class InfluencerRegistrationScreen
    extends GetView<InfluencerRegistrationController> {
  const InfluencerRegistrationScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CommonInfluenceRegistrationScreen(
      // onPressSubmitButton: () {
      //   if (controller.formKey.value.currentState!.validate()) {
      //     print('done');
      //   }
      // },
      // mrms: controller.mrms,
      // formKey: controller.formKey.value,
      // registerAsInfluenceTitleText:
      // LanguageConstants.bloggerRegistrationText.tr,
      // registerAsInfluenceTitleTextStyle: AppStyle.textStyleUtils500(
      //     size: 36,
      //     color: Theme.of(context)
      //         .textTheme
      //         .titleLarge
      //         ?.color
      //         ?.withOpacity(0.6)),
      // registerAsInfluenceSubTitleText:
      // LanguageConstants.pleaseFillTheBelowFormDetailsText.tr,
      // registerAsInfluenceSubTitleTextStyle: AppStyle.textStyleUtils500(
      //     size: 20,
      //     color: Theme.of(context)
      //         .textTheme
      //         .titleLarge
      //         ?.color
      //         ?.withOpacity(0.6)),
      // registerAsInfluenceFormTitleText:
      // LanguageConstants.pleaseFillTheBelowFormDetailsText.tr,
      // registerAsInfluenceFormTitleTextStyle: AppStyle.textStyleUtils500(
      //     size: 20,
      //     color: Theme.of(context)
      //         .textTheme
      //         .titleLarge
      //         ?.color
      //         ?.withOpacity(0.6)),
      // baseContainerBorderColor: Colors.transparent,
      // whoWeAreLookingTitleTextStyle: AppStyle.textStyleUtils700(
      //     size: 24, color: Theme.of(context).primaryColor),
      // whoWeAreLookingDescriptionTextStyle: AppStyle.textStyleUtils500(
      //     size: 20,
      //     color: Theme.of(context)
      //         .textTheme
      //         .titleLarge
      //         ?.color
      //         ?.withOpacity(0.5)),
      // profileTextAlignment: Alignment.centerLeft,
      // profileTextStyle: AppStyle.textStyleUtils700(
      //     size: 24, color: Theme.of(context).primaryColor),
      // firstNameFieldTitleText: "",
      // lastNameFieldTitleText: "",
      // emailFieldTitleText: "",
      // phoneFieldTitleText: "",
      // webUrlFieldTitleText: "",
      // cityFieldTitleText: "",
      // countryFieldTitleText: "",
      // pinCodeFieldTitleText: "",
      // facebookFieldTitleText: "",
      // instagramFieldTitleText: "",
      // twitterFieldTitleText: "",
      // youTubeFieldTitleText: "",
      // linkedInFieldTitleText: "",
      // pinterestFieldTitleText: "",
      // projectsFieldTitleText: "",
      // firstNameFieldHintText: "${LanguageConstants.name.tr} *",
      // lastNameFieldHintText: "${LanguageConstants.lastName.tr} *",
      // emailFieldHintText: "${LanguageConstants.emailAddreessText.tr} *",
      // phoneFieldHintText: "${LanguageConstants.phoneNumberText.tr} *",
      // webUrlFieldHintText: LanguageConstants.websiteUrlText.tr,
      // pinterestFieldHintText: LanguageConstants.pinterestText.tr,
      // cityFieldHintText: LanguageConstants.city.tr,
      // countryFieldHintText: LanguageConstants.country.tr,
      // facebookFieldHintText: LanguageConstants.facebookText.tr,
      // instagramFieldHintText: LanguageConstants.instagramText.tr,
      // twitterFieldHintText: 'Twitter',
      // youTubeFieldHintText: LanguageConstants.youtubeText.tr,
      // linkedInFieldHintText: LanguageConstants.linkendinText.tr,
      // projectsFieldHintText: 'Projects worked on',
      // pinCodeFieldHintText: LanguageConstants.postCodeText.tr,
      // submitButtonTextStyle:
      //     AppStyle.textStyleUtils700(size: 14, color: Colors.white),
      // submitButtonDecoration: BoxDecoration(
      //     borderRadius: BorderRadius.circular(50),
      //     color: Theme.of(context).primaryColor),
      // submitButtonText: LanguageConstants.submitText.tr,
      // submitButtonStyle: OutlinedButton.styleFrom(
      //     backgroundColor: Theme.of(context).primaryColor,
      //     shape: RoundedRectangleBorder(
      //         borderRadius: BorderRadius.circular(50))),
      // textFieldInputStyle: AppStyle.textStyleUtils600(
      //     size: 14, color: Theme.of(context).textTheme.titleLarge?.color),
      // textFieldTitleTextStyle: AppStyle.textStyleUtils400(
      //     size: 14, color: Theme.of(context).textTheme.titleLarge?.color),
      // textFieldHintTextStyle: AppStyle.textStyleUtils500(
      //     size: 14,
      //     color: Theme.of(context)
      //         .textTheme
      //         .titleLarge
      //         ?.color
      //         ?.withOpacity(0.4)),
      // textFieldInputDecoration: InputDecoration(
      //   hintStyle: AppStyle.textStyleUtils500(
      //       size: 16,
      //       color: Theme.of(context)
      //           .inputDecorationTheme
      //           .hintStyle
      //           ?.color
      //           ?.withOpacity(0.7)),
      //   enabledBorder: Theme.of(context).inputDecorationTheme.enabledBorder,
      //   border: Theme.of(context).inputDecorationTheme.enabledBorder,
      //   focusedBorder: Theme.of(context).inputDecorationTheme.enabledBorder,
      // ),
      // firstNameController: controller.firstNameController.value,
      // lastNameController: controller.lastNameController.value,
      // emailController: controller.emailController.value,
      // contactNoController: controller.contactNoController.value,
      // websiteUrlController: controller.websiteUrlController.value,
      // cityController: controller.cityController.value,
      // countryController: controller.countryController.value,
      // postCodeController: controller.postCodeController.value,
      // faceBookController: controller.faceBookController.value,
      // instagramController: controller.instagramController.value,
      // twitterController: controller.twitterController.value,
      // youtubeController: controller.youtubeController.value,
      // linkedinController: controller.linkedinController.value,
      // pinterestController: controller.pinterestController.value,
      // faceBookFollowerController: controller.faceBookFollowerController.value,
      // instagramFollowerController:
      //     controller.instagramFollowerController.value,
      // twitterFollowerController: controller.twitterFollowerController.value,
      // youtubeFollowerController: controller.youtubeFollowerController.value,
      // linkedinFollowerController: controller.linkedinFollowerController.value,
      // pinterestFollowerController:
      //     controller.pinterestFollowerController.value,
      // projectWorkController: controller.projectWorkController.value,
      // country: controller.country?.value,
      projectName: APP_NAME,
    );
  }
}
