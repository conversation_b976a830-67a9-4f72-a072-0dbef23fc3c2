import 'package:base_controller/controllers/tems_condition_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:presentation_common_ui/common_screens/common_terms_and_condition/common_terms_and_condition.dart';

import '../../app_constants/app_constants.dart';

class TermAndConditionScreen extends GetView<TermAndConditionController> {
  const TermAndConditionScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
          key: controller.scaffoldKey.value,
          /*   appBar: commonAppbar(
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            titleWidget: CommonTextPoppins(
              LanguageConstants.termAndConditionHeadingText.tr,
              color: Theme.of(context).textTheme.displayMedium?.color ??
                  blackColor,
              fontSize: 18.iY,
            ),
            leadingWidget: IconButton(
                onPressed: () {
                  Get.back();
                },
                icon: Icon(
                  Icons.arrow_back,
                  color: Theme.of(context).textTheme.displayMedium!.color ??
                      blackColor,
                ))),*/
          body: CommonTermsAndConditionScreen(
            projectName: APP_NAME,
          ) /*controller.isLoading.value
            ? const Center(
                child: SpinKitThreeBounce(
                  color: darkBlueColor,
                  // size: 50.0,
                ),
              )
            : Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12.5),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      controller.termsAndConditionsModel.value.content == null
                          ? Text(
                              LanguageConstants.noDataFound.tr,
                            )
                          : Center(
                              child: Html(
                                data: controller.termsAndConditionsModel.value
                                        .content ??
                                    '',
                                style: {
                                  "body": Style(
                                      color: Theme.of(context)
                                          .textTheme
                                          .titleLarge!
                                          .color),
                                },
                              ),
                            ),
                      const SizedBox(
                        height: 10,
                      ),
                    ],
                  ),
                ),
              ),*/
          ),
    );
  }
}
