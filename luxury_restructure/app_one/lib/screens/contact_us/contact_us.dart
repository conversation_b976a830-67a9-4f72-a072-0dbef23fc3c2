import 'package:app_one/all_imports.dart';
import 'package:app_one/app_constants/app_constants.dart';
import 'package:base_controller/controllers/contact_us/app_one_contact_us_controller.dart';
import 'package:app_one/theme/colors.dart';
import 'package:presentation/app_constants/constants.dart';
import 'package:presentation/const/app_text_style/presentation_app_text_style.dart';
import 'package:presentation_common_ui/common_contact_us/common_contact_us_screen.dart';
import 'package:presentation_common_ui/common_screens/common_contactus_screen/common_contactus_screen.dart';

class ContactUs extends StatelessWidget {
  const ContactUs({super.key});

  @override
  Widget build(BuildContext context) {
    return const CommonContactusScreen(
      projectName: APP_NAME,
    );
    // return commonContactusScreen(
    //   titletext: 'General & Order Enquiries',
    //   subtitletext: 'Whatever Your Query. Use The Contact From Below To Get In Touch-Our Team Is Ready To Help 24*7',
    //   titletextcolor: Theme.of(context).textTheme.titleLarge!.color ?? appColor,
    //   titletextsize: 20,
    //   fontFamily: AppConstants.fontMontserrat,
    //   subtitletextsize: 14,
    //   firstnametitletext: 'First Name *',
    //   lastnametitletext: 'Last Name *',
    //   textfieldText: 'Email Address *',
    //   phonenotextfieldText: 'Phone Number *',
    //   subjecttextfieldText: 'Subject *',
    //   messagetextfieldText: 'Message',
    //   textfielddecoration: InputDecoration(border: InputBorder.none, hintStyle: AppTextStyle.textStyleUtils500(size: 16, color: Colors.grey), hintText: LanguageConstants.enterFirstName.tr),
    //   submitbuttontext: 'Continue',
    //   chatemailtext: 'Email Address *',
    //   chatphonenotitletext: 'Phone Number *',
    //   typeofenquirytextfieldText: 'Type Of Enquiry *',
    //   submitbuttonTextStyle: AppTextStyle.textStyleUtils600(size: 16, color: Theme.of(context).textTheme.titleSmall!.color),
    //   submitbuttondecoration: BoxDecoration(borderRadius: BorderRadius.circular(26), color: Theme.of(context).textTheme.titleLarge!.color),
    //   firstnamedecoration: BoxDecoration(borderRadius: BorderRadius.circular(12), border: Border.all(color: greyColor ?? whiteColor, width: 1)),
    // );
  }
}
