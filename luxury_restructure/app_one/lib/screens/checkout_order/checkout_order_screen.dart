import 'dart:ui';
import 'package:app_one/app_constants/app_constants.dart';
import 'package:base_controller/controllers/checkout_order_controller.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:presentation_common_ui/common_screens/common_checkout_screen/common_ckeckout_screen.dart';

class CheckoutOrderScreen extends GetView<CheckoutOrderController> {
  CheckoutOrderScreen({Key? key}) : super(key: key);

  final CheckoutOrderController controller = Get.find();
  //final HomeScreenController homeController = Get.find();

  @override
  Widget build(BuildContext context) {
    return const CommonCheckoutScreen(
      projectName: APP_NAME,
    );
  }
}
