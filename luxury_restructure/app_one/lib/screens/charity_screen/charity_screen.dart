import 'package:app_one/core/consts/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:meta_package/translations/translations.dart';
import 'package:presentation/const/app_colors/app_one_colors.dart';
import 'package:base_controller/controllers/charity_screen_controller.dart';
import 'package:presentation_common_ui/widgets/charity_screen/app_one/show_dialog.dart';
// import 'package:app_one/theme/app_asset.dart';
import 'package:presentation/const/app_text_style/app_one_app_text_style.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';

class CharityScreen extends GetView<CharityScreenController> {
  const CharityScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: controller.scaffoldKey,
      backgroundColor: backgroundColor,
      appBar: _buildAppBar(context),
      drawer: const Drawer(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 8.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              InkWell(
                onTap: () {
                  addNewShowDialog(context);
                },
                child: Container(
                    padding: const EdgeInsets.all(10.0),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: appColor,
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                    child: Text(
                      LanguageConstants.addNew.tr,
                      style: AppStyle.textStyleUtils500_16(color: Colors.white),
                    )),
              ),
            ],
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: backgroundColor,
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.black),
      centerTitle: true,
      leading: InkWell(
        onTap: () {
          controller.scaffoldKey.currentState?.openDrawer();
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: SvgPicture.asset(ImageConstant.menuIcon),
        ),
      ),
      actions: [
        InkWell(
          onTap: () {},
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
            child: SvgPicture.asset(ImageConstant.searchIcon),
          ),
        ),
        InkWell(
          onTap: () {},
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
            child: SvgPicture.asset(ImageConstant.heartIcon),
          ),
        ),
        InkWell(
          onTap: () {},
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
            child: SvgPicture.asset(ImageConstant.shoppingCartIcon),
          ),
        ),
      ],
      title: Image.asset(AppAsset.logo, width: 110),
    );
  }
}
