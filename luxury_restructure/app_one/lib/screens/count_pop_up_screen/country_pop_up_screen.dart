import 'package:presentation/const/size_utils.dart';
import 'package:base_controller/controllers/country_pop_up_screen_controller.dart';
import 'package:presentation_common_ui/constants/app_constants.dart';
import '../../all_imports.dart';
import 'package:presentation_common_ui/common_screens/common_country_pop_up_screen/common_country_pop_up_screen.dart';

class CountryPopUpScreen extends GetView<CountryPopUpScreenController> {
  CountryPopUpScreen({Key? key}) : super(key: key);

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return CommonCountryPopUpScreen(
      projectName: APP_ONE_NAME,
    );
    // return Obx(() => Scaffold(
    //       key: scaffoldKey,
    //       backgroundColor: backgroundColor,
    //       // appBar: AppBar(
    //       //   backgroundColor: backgroundColor,
    //       //   elevation: 0,
    //       //   iconTheme: const IconThemeData(color: Colors.black),
    //       //   centerTitle: true,
    //       //   title: Text(
    //       //     LanguageConstants.myAccountText.tr,
    //       //     style: AppStyle.textStyleUtils400(color: appColor),
    //       //   ),
    //       //   bottom: PreferredSize(
    //       //     preferredSize: const Size.fromHeight(1.0),
    //       //     child: Container(height: 1.0, width: double.infinity, color: appColor),
    //       //   ),
    //       // ),
    //       appBar: commonAppbar(
    //             backgroundColor: backgroundColor,
    //         elevation: 0,
    //         iconTheme: const IconThemeData(color: Colors.black),
    //         centerTitle: true,
    //         titleWidget: Text(
    //           LanguageConstants.myAccountText.tr,
    //           style: AppStyle.textStyleUtils400(color: appColor),
    //         ),
    //         bottom: PreferredSize(
    //           preferredSize: const Size.fromHeight(1.0),
    //           child: Container(height: 1.0, width: double.infinity, color: appColor),
    //         ),
    //       ),
    //       body: Padding(
    //         padding: EdgeInsets.symmetric(horizontal: 14.iX, vertical: 8.iY),
    //         child: Column(
    //           mainAxisAlignment: MainAxisAlignment.start,
    //           children: [
    //             SizedBox(height: 20.iY),
    //             InkWell(
    //               onTap: () {
    //                 controller.showCountry(context);
    //               },
    //               child: Row(
    //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //                 children: [
    //                   Text(
    //                     LanguageConstants.myAccountText.tr.toUpperCase(),
    //                     style: AppStyle.textStyleUtils400(),
    //                   ),
    //                   const Icon(
    //                     Icons.keyboard_arrow_right,
    //                     color: Colors.black,
    //                   ),
    //                 ],
    //               ),
    //             ),
    //             SizedBox(height: 10.iY),
    //             Row(
    //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //               children: [
    //                 Text(
    //                   LanguageConstants.myOrdersText.tr.toUpperCase(),
    //                   style: AppStyle.textStyleUtils400(),
    //                 ),
    //                 const Icon(
    //                   Icons.keyboard_arrow_right,
    //                   color: Colors.black,
    //                 ),
    //               ],
    //             ),
    //             SizedBox(height: 10.iY),
    //             Row(
    //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //               children: [
    //                 Text(
    //                   LanguageConstants.myWidhListText.tr.toUpperCase(),
    //                   style: AppStyle.textStyleUtils400(),
    //                 ),
    //                 const Icon(
    //                   Icons.keyboard_arrow_right,
    //                   color: Colors.black,
    //                 ),
    //               ],
    //             ),
    //             SizedBox(height: 10.iY),
    //             Row(
    //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //               children: [
    //                 Text(
    //                   LanguageConstants.addressBookText.tr.toUpperCase(),
    //                   style: AppStyle.textStyleUtils400(),
    //                 ),
    //                 const Icon(
    //                   Icons.keyboard_arrow_right,
    //                   color: Colors.black,
    //                 ),
    //               ],
    //             ),
    //             SizedBox(height: 10.iY),
    //             Row(
    //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //               children: [
    //                 Text(
    //                   LanguageConstants.accountInfoText.tr.toUpperCase(),
    //                   style: AppStyle.textStyleUtils400(),
    //                 ),
    //                 const Icon(
    //                   Icons.keyboard_arrow_right,
    //                   color: Colors.black,
    //                 ),
    //               ],
    //             ),
    //             SizedBox(height: 10.iY),
    //             Row(
    //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //               children: [
    //                 Text(
    //                   LanguageConstants.storePaymentText.tr.toUpperCase(),
    //                   style: AppStyle.textStyleUtils400(),
    //                 ),
    //                 const Icon(
    //                   Icons.keyboard_arrow_right,
    //                   color: Colors.black,
    //                 ),
    //               ],
    //             ),
    //             SizedBox(height: 10.iY),
    //             Row(
    //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //               children: [
    //                 Text(
    //                   LanguageConstants.newsletterSubText.tr.toUpperCase(),
    //                   style: AppStyle.textStyleUtils400(),
    //                 ),
    //                 const Icon(
    //                   Icons.keyboard_arrow_right,
    //                   color: Colors.black,
    //                 ),
    //               ],
    //             ),
    //             SizedBox(height: 10.iY),
    //           ],
    //         ),
    //       ),
    //     ));
  }
}
