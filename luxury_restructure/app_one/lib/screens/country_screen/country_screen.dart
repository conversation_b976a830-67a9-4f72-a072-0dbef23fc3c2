import 'package:app_one/all_imports.dart';
import 'package:app_one/app_constants/app_constants.dart';
import 'package:presentation_common_ui/common_screens/common_country_screen/common_country_screen.dart';

class CountryScreen extends StatelessWidget{
   CountryScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const CommonCountryScreen(
      projectName: APP_NAME,
    );
      // return CommonCountryScreen(
      //   hintStyleColor: Theme.of(context).hintColor,
      //   fillColor: Theme.of(context).scaffoldBackgroundColor,
      //   elevatedButtonBackgroundColor: Theme.of(context).scaffoldBackgroundColor,
      //   onElevationButtonPress: (){
      //     debugPrint("pressed elevated button");
      //      Get.offAndToNamed<dynamic>(RoutesConstants.dashboardScreen);
      //   }
      // );
  }
}