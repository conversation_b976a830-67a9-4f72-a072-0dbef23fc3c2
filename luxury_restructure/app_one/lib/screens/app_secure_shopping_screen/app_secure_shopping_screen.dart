import 'package:presentation/const/size_utils.dart';
import 'package:presentation/const/app_text_style/presentation_app_text_style.dart';
import 'package:app_one/all_imports.dart';
import 'package:base_controller/controllers/app_secure_shopping_controller.dart';
// import 'package:app_one/theme/app_asset.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';


class AppSecureShoppingScreen extends GetView<AppSecureShoppingController> {
  const AppSecureShoppingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: _buildScreen(context),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return commonAppbar(
        titleWidget: CommonTextPoppins(
      LanguageConstants.secureShoppingText.tr,
    ));
  }

  Widget _buildScreen(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.iX),
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: 39.iY),
            Text(LanguageConstants.shopWithoutWorry.tr,
                style: AppTextStyle.textStyleUtils400(
                  size: 20,
                  color: Theme.of(context).textTheme.displayMedium!.color,
                ).copyWith(
                    decorationColor: appColor,
                    decorationThickness: 1.5,
                    decoration: TextDecoration.underline)),
            SizedBox(height: 22.iY),
            Container(
              width: Get.width,
              height: 199.iY,
              decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(AppAsset.brandListLogo),
                    fit: BoxFit.fill),
              ),
            ),
            SizedBox(height: 18.iY),
            Text(
              LanguageConstants.fillYourCartText.tr,
              textAlign: TextAlign.justify,
              style: AppTextStyle.textStyleUtils400(
                size: 16,
                color: Theme.of(context).textTheme.displayMedium!.color,
              ),
            ),
            SizedBox(height: 20.iY),
            Text(
              LanguageConstants.weAimToSatisfyText.tr,
              textAlign: TextAlign.justify,
              style: AppTextStyle.textStyleUtils400(
                size: 16,
                color: Theme.of(context).textTheme.displayMedium!.color,
              ),
            ),
            SizedBox(height: 20.iY),
            Text(
              LanguageConstants.ifYouPayWithPayPal.tr,
              textAlign: TextAlign.justify,
              style: AppTextStyle.textStyleUtils400(
                size: 16,
                color: Theme.of(context).textTheme.displayMedium!.color,
              ),
            ),
            SizedBox(height: 20.iY),
            Text(
              LanguageConstants.inAdditionAllOtherPaymentsAreProcessed.tr,
              textAlign: TextAlign.justify,
              style: AppTextStyle.textStyleUtils400(
                size: 16,
                color: Theme.of(context).textTheme.displayMedium!.color,
              ),
            ),
            SizedBox(height: 91.iY),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     Image(
            //       image: AssetImage(AppAsset.payPal),
            //     ),
            //     Image(
            //       image: AssetImage(AppAsset.moneyBank),
            //     ),
            //     Image(
            //       image: AssetImage(AppAsset.googleStore),
            //     ),
            //   ],
            // ),
            SizedBox(height: 91.iY),
          ],
        ),
      ),
    );
  }
}
