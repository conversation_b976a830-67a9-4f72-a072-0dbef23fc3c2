import 'package:app_one/all_imports.dart';
import 'package:app_one/app_constants/app_constants.dart';
import 'package:app_one/app_pages/app_routes.dart';
import 'package:flutter/material.dart';
import 'package:presentation_common_ui/common_screens/common_search_screen/common_search_screen.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:presentation/const/app_colors/app_one_colors.dart';
import 'package:presentation/navigation/navigation.dart';
import 'package:presentation/widgets/common_app_bar/common_app_bar.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';

class BrandsAndLabelSearchScreen extends StatelessWidget {
  const BrandsAndLabelSearchScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return /* SearchScreen(
      hintTextColor: greyColor,
      prefixColor: greyColor,
      buttonTextColor: Colors.white,
      appBorderColor: greyColor,
      appBar: commonAppbar(leadingWidget: IconButton(
          onPressed: () {
            Get.find<Navigation>().getBack<void>(null);
          },
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).iconTheme.color,
          ))),
      loaderColor: darkBlueColor,
      backGroundColor: Theme.of(context).colorScheme.background,
      secondaryColor: backgroundColor,
      nothingToShowAnimationPath: AppAsset.nothigToShowAnimation,
      cartAsset: AppAsset.cart,
      logoAsset: AppAsset.logo,
      searchAssetsPath: AppAsset.search,
      brandDetailScreenRoute: RoutesConstants.brandDetailsScreen,
      productDetailScreenRoute: RoutesConstants.productDetailsScreen,
      specialRequestScreenRoute: '', //RoutesConstants.specialRequestScreen,
      appColor: darkBlueColor,
      colorButton1: darkBlueColor,
      colorButton2: darkBlueColor,
    );*/
        CommonSearchScreen(
      projectName: APP_NAME,
      appColor: darkBlueColor,
      // appBar: commonAppbar(
      //     leadingWidget: IconButton(
      //         onPressed: () {
      //           Get.find<Navigation>().getBack<void>(null);
      //         },
      //         icon: Icon(
      //           Icons.arrow_back,
      //           color: Theme.of(context).iconTheme.color,
      //         ))),
      loaderColor: primaryColor,
      backGroundColor: Theme.of(context).colorScheme.background,
      secondaryColor: backgroundColor,
      productDetailScreenRoute: RoutesConstants.productDetailsScreen,
      nothingToShowAnimationPath: AppAsset.nothigToShowAnimation,
      cartAsset: AppAsset.cart,
      logoAsset: AppAsset.logo,
      searchAssetsPath: AppAsset.search,
    );
  }
}
