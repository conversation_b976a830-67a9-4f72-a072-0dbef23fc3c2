import 'package:app_one/app_pages/app_routes.dart';
import 'package:base_controller/controllers/dashboard_controller.dart';
import 'package:app_one/screens/brands_and_label_search_screen/brands_and_label_search_screen.dart';
import 'package:presentation/navigation/navigation.dart';
import 'package:presentation_common_ui/theme/color_helper.dart';
import 'package:presentation_common_ui/widgets/cart_screen/app_three/show_title_dialog.dart';
import 'package:presentation_common_ui/widgets/dashboard/app_one/navbar_widget.dart';
import 'package:app_one/screens/designer_screen/designer_screen.dart';
import 'package:app_one/screens/home/<USER>';
import 'package:domain/arguments/app_three/brand_list_detail_argument.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:meta_package/api/models/home/<USER>';
import 'package:app_one/app_constants/app_constants.dart';
import 'package:presentation/const/app_text_style/presentation_app_text_style.dart';
import 'package:presentation/const/size_utils.dart';
import 'package:presentation/widgets/no_nerwork_widget/no_network_widget.dart';
import 'package:presentation_common_ui/common_screens/common_my_account_page/common_my_account_page.dart';
import 'package:presentation_common_ui/widgets/dashboard/app_one/get_drawer.dart';
import '../../all_imports.dart';
import '../cart_screen/cart_screen.dart';
import '../my_account_screen/theme_change_toggle.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';
import 'package:meta_package/api/services/local_store_service.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/my_account_controller.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/search_controller.dart';
import 'package:domain/usecases/my_account_usecase/get_account_detail_response_usecase.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_choose_in_size_list.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_generate_cart_api_response.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_product_detail_api.dart';
import 'package:domain/usecases/recommended_products_api_usecase/get_recommended_product_response.dart';
import 'package:domain/usecases/recommended_products_api_usecase/guest_post_add_to_cart_product_response.dart';
import 'package:domain/usecases/recommended_products_api_usecase/post_add_to_cart_product_response.dart';
import 'package:domain/usecases/search_api_usecase/get_search_product_api_response.dart';
import 'package:domain/usecases/search_api_usecase/get_searchapi_response.dart';
import 'package:domain/usecases/wish_list_api_usecase/add_to_wish_list.dart';
import 'package:domain/usecases/menu_usecase/get_menu_api_search_response_usecase.dart';
import 'package:app_one/core/consts/app_constants.dart';

class DashboardScreen extends GetView<DashboardController> {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ColorModal appColors = getColorModal(APP_NAME);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.scrollControllerOne.hasClients) {
        controller.scrollControllerOne.jumpTo(0.0);
      }
      if (controller.scrollControllerTwo.hasClients) {
        controller.scrollControllerTwo.jumpTo(0.0);
      }
      if (controller.scrollControllerThree.hasClients) {
        controller.scrollControllerThree.jumpTo(0.0);
      }
    });
    return Obx(() => controller.isConnected.value
        ? Scaffold(
            body: Scaffold(
                key: controller.scaffoldKey.value,
                backgroundColor: appColors.backgroundColor,
                // drawer: getDrawer(context),
                appBar: AppBar(
                  automaticallyImplyLeading: false,
                  flexibleSpace: Obx(
                        () => CommonAppBar(
                      appBarColor: whiteColor,
                      appBarLogo: AppAsset.appLogoSvg,
                      appBarTextColor: blackColor,
                      menuIconPath: AppAsset.appMenuIcon,
                      iconBgColor: Colors.black,
                      logoColor: primaryColor,
                      isHomePage: (controller.appbarTitle.value.isEmpty) ? true : false,
                      controller: controller,
                      appbarTitle: controller.appbarTitle.value,
                    ),
                  ),
                ),
                drawer: const GetDrawer(),
                resizeToAvoidBottomInset: true,
                body: TabBarView(
                  controller: controller.tabController.value,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    const HomeScreen(),
                    const BrandsAndLabelSearchScreen(),
                    const DesignerScreen(),
                    CartScreen(),
                    CommonMyAccountMenuPage(
                      sectionTitleColor: Colors.black,
                      myAccountPageNavigation: MyAccountPageNavigation(
                          contactUsScreenPath: RoutesConstants.contactUsScreen,
                          loginScreenRoute: RoutesConstants.loginScreen,
                          signupScreenRoute: RoutesConstants.signUpScreen,
                          myWishListScreenRoute: RoutesConstants.wishlistScreen,
                          addressBookScreenRoute: RoutesConstants.addressBookScreen,
                          accountInformationScreenRoute: RoutesConstants.myAccountScreen,
                          myStoreCreditScreenRoute: RoutesConstants.storeCreditScreen,
                          myCouponsScreenRoute: RoutesConstants.myCouponsScreen,
                          trackOrderScreenRoute: RoutesConstants.trackYourOrderScreen,
                          notificationScreenRoute: RoutesConstants.notificationScreen,
                          secureShoppingScreenRoute: RoutesConstants.appSecureShoppingScreen,
                          trackYourOrderByGuestScreenRoute: RoutesConstants.guestReturnsScreen,
                          hopeScreenRoute: RoutesConstants.charityScreen,
                          affiliateProgramScreenRoute: RoutesConstants.affiliateProgramScreen,
                          influenceRegistrationScreenRoute: RoutesConstants.influencerRegistrationScreen,
                          exchangeAndReturnsScreenRoute: RoutesConstants.exchangeAndReturnsScreen,
                          privacyPolicyScreenRoute: RoutesConstants.privacyPolicyScreen,
                          termsAndConditionsScreenRoute: RoutesConstants.termsConditionScreen,
                          shippingScreenRoute: RoutesConstants.shippingScreen,
                          trackYourTicketByEmailScreenRoute: RoutesConstants.traceYourTicketMail,
                          trackTicketScreenRoute: RoutesConstants.trackTicketScreen,
                          aboutUsScreenRoute: '',
                          referFriendScreenRoute: RoutesConstants.referFriendScreen,
                          returnsAndRefundScreenRoute: RoutesConstants.returnsAndRefundsScreen,
                          faqScreenRoute: RoutesConstants.faqScreen,
                          liveChatScreenRoute: '',
                          myOrdersScreen: RoutesConstants.myOrderScreen,
                          countryRoute: RoutesConstants.countryScreen,
                          myTicketScreen: RoutesConstants.myTicketsScreen,
                          advancedsearch: '',
                          giftvouchers: '',
                          splashScreenRoute: RoutesConstants.dashboardScreen),
                      showTitleDialog: showChatDialog(context),
                      themeChangeToggle: const ThemeChangeToggle(),
                      //const ThemeChangeToggle()
                      appTileBackgroundColor: whitePrimary50Color,
                      projectName: APP_NAME,
                    ),
                    /*       MyAccountsPage(
                        MyAccountPageNavigation(
                            contactUsScreenPath: RoutesConstants.contactUsScreen,
                            loginScreenRoute: RoutesConstants.loginScreen,
                            signupScreenRoute: RoutesConstants.signUpScreen,
                            myWishListScreenRoute: RoutesConstants.wishlistScreen,
                            addressBookScreenRoute: RoutesConstants.addressBookScreen,
                            accountInformationScreenRoute: RoutesConstants.myAccountScreen,
                            myStoreCreditScreenRoute: RoutesConstants.storeCreditScreen,
                            myCouponsScreenRoute: RoutesConstants.myCouponsScreen,
                            trackOrderScreenRoute: RoutesConstants.trackYourOrderScreen,
                            notificationScreenRoute: RoutesConstants.notificationScreen,
                            secureShoppingScreenRoute: '',
                            //RoutesConstants.appSecureShoppingScreen,
                            trackYourOrderByGuestScreenRoute: RoutesConstants.guestReturnsScreen,
                            hopeScreenRoute: RoutesConstants.charityScreen,
                            affiliateProgramScreenRoute: RoutesConstants.affiliateProgramScreen,
                            influenceRegistrationScreenRoute: RoutesConstants.influencerRegistrationScreen,
                            exchangeAndReturnsScreenRoute: '',
                            privacyPolicyScreenRoute: RoutesConstants.privacyPolicyScreen,
                            termsAndConditionsScreenRoute: RoutesConstants.termsConditionScreen,
                            shippingScreenRoute: RoutesConstants.shippingScreen,
                            trackYourTicketByEmailScreenRoute: RoutesConstants.traceYourTicketMail,
                            aboutUsScreenRoute: '',
                            referFriendScreenRoute: RoutesConstants.referFriendScreen,
                            returnsAndRefundScreenRoute: RoutesConstants.returnsAndRefundsScreen,
                            faqScreenRoute: RoutesConstants.faqScreen,
                            liveChatScreenRoute: '',
                            myOrdersScreen: RoutesConstants.myOrderScreen,
                            countryRoute: RoutesConstants.countryScreen,
                            myTicketScreen: RoutesConstants.myTicketsScreen,
                            advancedsearch: '',
                            giftvouchers: ''),
                        const ThemeChangeToggle(),
                        Container()
                        // showChatDialog(context)
                        ),*/
                  ],
                ),

                bottomNavigationBar: const NavbarWidget()),
          )
        : NoNetworkWidget(
            animationName: AppAsset.noInternetAnimation, appbarLogoImageName: AppAsset.logo, noInternetText: LanguageConstants.noInternet.tr));
  }

  Widget appBar(BuildContext ctx) {
    return AnimatedContainer(
        height: 60.iY,
        duration: const Duration(milliseconds: 500),
        color: Theme.of(ctx).colorScheme.background,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            IconButton(
              onPressed: () {
                if (!(controller.scaffoldKey.value.currentState?.isDrawerOpen ?? false)) {
                  controller.scaffoldKey.value.currentState?.openDrawer();
                }
              },
              icon: SvgPicture.asset(
                AppAsset.appMenuIcon,
                color: Theme.of(ctx).textTheme.displayMedium!.color,
              ),
            ),
            controller.tabController.value.index == 0
                ? SizedBox(
                    height: 36.iY,
                    child: SvgPicture.asset(
                      AppAsset.appLogoSvg,
                      color: Theme.of(ctx).textTheme.displayMedium!.color,
                    ),
                  )
                : Center(
                    child: Text(
                      controller.appbarTitle.value,
                      style: AppTextStyle.normalSemiBold16.copyWith(
                        color: Theme.of(ctx).primaryColorDark,
                      ),
                    ),
                  ),
            SizedBox(
              width: 30.iX,
            )
          ],
        ));
  }

  Widget headerLogo() {
    return Row(
      children: [
        SizedBox(
          height: 10.iY,
          child: VerticalDivider(
            color: appColor,
            thickness: 3.iY,
            indent: 0,
            endIndent: 0,
            width: 20.iX,
          ),
        ),
        SizedBox(height: 5.iY),
        SizedBox(
          height: 48.iY,
          child: Image.network(
            controller.logoModel.value.logoUrl.toString(),
            height: 48.iY,
          ),
        ),
      ],
    );
  }

  Widget getDrawerView(BuildContext context) {
    if (controller.menuModel?.value.childrenData == null) {
      print('brand null data ======> ');
      return Container(
        color: Theme.of(context).scaffoldBackgroundColor,
      );
    }
    return Container(
      // color: Colors.white,
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Stack(
        children: [
          Column(
            children: [
              Container(
                // color: Colors.white,
                color: Theme.of(context).scaffoldBackgroundColor,
                margin: EdgeInsets.only(top: 50.iY, left: 10.iX, right: 20.iX, bottom: 8.iY),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(LanguageConstants.category.tr,
                        style: AppTextStyle.textStyleUtils600(
                            size: 16.iY, color: Theme.of(context).textTheme.displayMedium?.color, fontWeight: FontWeight.w600)),
                    InkWell(
                      onTap: () {
                        Get.back<void>();
                      },
                      child: Image.asset(
                        AppAsset.close,
                        height: 20.iY,
                        color: Theme.of(context).textTheme.displayMedium?.color,
                        width: 20.iX,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                height: 2.iY,
                color: borderGreyColor,
              )
            ],
          ),
          Container(
            margin: EdgeInsets.only(top: 85.iY),
            child: ListView(
              physics: const ClampingScrollPhysics(),
              shrinkWrap: true,
              primary: true,
              padding: EdgeInsets.zero,
              children: [
                Column(
                  children: [
                    SizedBox(
                      height: 10.iY,
                    ),
                    ListView.builder(
                        controller: controller.scrollControllerOne,
                        // Important: Remove any padding from the ListView.
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        itemCount: controller.menuModel?.value.childrenData?.length,
                        itemBuilder: (context, index) {
                          ChildrenData? itemLevel1 = controller.menuModel?.value.childrenData?[index];
                          return itemLevel1?.isActive ?? false
                              ? Container(
                                  margin: EdgeInsets.only(bottom: 5.iY, right: 10.iX),
                                  padding: EdgeInsets.symmetric(horizontal: 10.iX),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      textWithIcon(
                                        name: itemLevel1?.name ?? '',
                                        style: AppTextStyle.textStyleUtils400(
                                          size: 14.iY,
                                          color: Theme.of(context).textTheme.titleLarge!.color,
                                        ),
                                        isIcon: itemLevel1?.childrenData?.isEmpty ?? false ? true : false,
                                        isExpand: itemLevel1?.isExpand.value ?? false ? true : false,
                                        onTapExpand: () {
                                          itemLevel1?.isExpand.value = true;
                                          controller.menuModel?.refresh();
                                          if (itemLevel1?.childrenData?.isEmpty ?? false) {
                                            final args =
                                                BrandListDetailArgument(argument: "dash", id: itemLevel1?.id.toString(), title: itemLevel1?.name);
                                            // Get.toNamed<void>(RoutesConstants.brandDetailsScreen, arguments: args);
                                            Get.find<Navigation>().navigateToNextPage(route: RoutesConstants.brandDetailsScreen, arguments: args);
                                          }
                                        },
                                        onTapCollapse: () {
                                          itemLevel1?.isExpand.value = false;
                                          controller.menuModel!.refresh(); // for (var element in itemLevel1.childrenData!) {
                                        },
                                        iconColor: Theme.of(context).iconTheme.color,
                                      ),
                                      SizedBox(
                                        width: Get.width / 1.3,
                                        child: itemLevel1?.isExpand.value ?? false
                                            ? ListView.builder(
                                                controller: controller.scrollControllerTwo,
                                                padding: EdgeInsets.zero,
                                                shrinkWrap: true,
                                                physics: const NeverScrollableScrollPhysics(),
                                                itemCount: itemLevel1?.childrenData?.length,
                                                itemBuilder: (context, index1) {
                                                  ChildrenData? itemLevel2 = itemLevel1?.childrenData?[index1];
                                                  return childItem(itemLevel2, itemLevel1, context);
                                                })
                                            : Container(),
                                      ),
                                    ],
                                  ),
                                )
                              : Container();
                        }),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget childItem(ChildrenData? itemLevel2, ChildrenData? itemLevel1, BuildContext context) {
    return itemLevel2?.isActive ?? false
        ? Container(
            margin: EdgeInsets.only(left: 10.iX),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                textWithIcon(
                  name: itemLevel2?.name ?? '',
                  style: AppTextStyle.textStyleUtils400(
                    size: 13.iY,
                    color: Theme.of(context).textTheme.titleLarge!.color,
                  ),
                  isIcon: (itemLevel2?.childrenData?.isEmpty ?? false) ? true : false,
                  isExpand: (itemLevel2?.isExpand.value ?? false) ? true : false,
                  onTapExpand: () {
                    if (itemLevel2?.childrenData?.isEmpty ?? false) {
                      final args = BrandListDetailArgument(argument: "dash", id: itemLevel1?.id.toString(), title: itemLevel1?.name);

                      // Get.toNamed<void>(RoutesConstants.brandDetailsScreen, arguments: args);
                      Get.find<Navigation>().navigateToNextPage(route: RoutesConstants.brandDetailsScreen, arguments: args);
                    }
                    itemLevel2?.isExpand.value = true;
                    controller.menuModel?.refresh();
                  },
                  onTapCollapse: () {
                    itemLevel2?.isExpand.value = false;
                    controller.menuModel?.refresh(); // for (var element in itemLevel1.childrenData!) {
                  },
                  iconColor: Theme.of(context).iconTheme.color,
                ),
                Column(
                  children: [
                    SizedBox(
                      height: 8.iY,
                    ),
                    itemLevel2?.isExpand.value ?? false
                        ? ListView.builder(
                            controller: controller.scrollControllerThree,
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: itemLevel2?.childrenData?.length,
                            itemBuilder: (context, index2) {
                              ChildrenData? itemLevel3 = itemLevel2?.childrenData?[index2];
                              return itemLevel3?.isActive ?? false
                                  ? GestureDetector(
                                      onTap: () {
                                        final args = BrandListDetailArgument(
                                            argument: "dash", id: itemLevel3?.id.toString(), title: "${itemLevel1?.name} ${itemLevel3?.name}");
                                        Get.find<Navigation>().navigateToNextPage(route: RoutesConstants.brandDetailsScreen, arguments: args);
                                        // Get.toNamed<void>(RoutesConstants.brandDetailsScreen, arguments: args);
                                      },
                                      child: Container(
                                        margin: EdgeInsets.only(left: 10.iX, top: 3.iY, bottom: 3.iY),
                                        child: Text(itemLevel3?.name ?? '',
                                            overflow: TextOverflow.ellipsis,
                                            style: AppTextStyle.normalRegular14.copyWith(
                                              fontFamily: AppConstants.fontOpenSans,
                                              fontSize: 16,
                                              fontWeight: FontWeight.w400,
                                              color: Theme.of(context).textTheme.titleLarge!.color,
                                            )),
                                      ),
                                    )
                                  : Container();
                            })
                        : Container(),
                  ],
                )
              ],
            ),
          )
        : Container();
  }

  Widget getDrawer(BuildContext context) {
    return SafeArea(
      child: Drawer(
        child: SizedBox(
          width: Get.width - 40.0.iY,
          child: getDrawerView(context),
        ),
      ),
    );
  }

  Widget headerMenu() {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            if (!(controller.scaffoldKey.value.currentState?.isDrawerOpen ?? false)) {
              controller.scaffoldKey.value.currentState?.openDrawer();
            }
          },
          child: Image.asset(
            AppAsset.menu,
            height: 16.iY,
            width: 16.iX,
          ),
        ),
        const SizedBox(
          width: 20.0,
        ),
        InkWell(
          onTap: () {
            // Get.toNamed<void>(RoutesConstants.cartScreen);
            Get.find<Navigation>().navigateToNextPage(
              route: RoutesConstants.cartScreen,
            );
          },
          child: Image.asset(
            AppAsset.cart1,
            height: 16.iY,
            width: 16.iX,
          ),
        ),
        SizedBox(width: 10.iX)
      ],
    );
  }

  Widget textWithIcon({
    String? name,
    TextStyle? style,
    bool isIcon = false,
    bool isExpand = false,
    GestureTapCallback? onTapExpand,
    GestureTapCallback? onTapCollapse,
    Color? iconColor,
  }) {
    return InkWell(
      onTap: !isExpand ? onTapExpand : onTapCollapse,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 5.iY),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(name!, overflow: TextOverflow.ellipsis, style: style),
            isIcon
                ? Container()
                : Image.asset(
                    !isExpand ? AppAsset.plus : AppAsset.minus,
                    height: 14.iY,
                    color: iconColor,
                    width: 14.iX,
                  ),
          ],
        ),
      ),
    );
  }

  void bindDependency() {
    const baseUrl = WEBSITE;
    Get.lazyPut(
      () => MySearchController(
        localStore: LocalStore(),
        getChooseInSizeListUseCase: GetChooseInSizeList(baseUrl: baseUrl),
        getMenuApiSearchResponseUseCase: GetMenuApiSearchResponse(baseUrl: baseUrl),
        getProductDetailApiUseCase: GetProductDetailApi(baseUrl: baseUrl),
        getGenerateCartApiResponseUseCase: GetGenerateCartApiResponse(baseUrl: baseUrl),
        getRecommendedProductResponseUseCase: GetRecommendedProductResponse(baseUrl: baseUrl),
        postAddToCartProductResponseUseCase: PostAddToCartProductResponse(baseUrl: baseUrl),
        guestPostAddToCartProductResponseUseCase: GuestPostAddToCartProductResponse(baseUrl: baseUrl),
        getSearchApiResponseUseCase: GetSearchApiResponse(baseUrl: baseUrl),
        addToWishListUseCase: AddToWishList(baseUrl: baseUrl),
        getSearchProductApiResponseUseCase: GetSearchProductApiResponse(baseUrl: baseUrl),
      ),
    );
    Get.lazyPut(() => GetAccountDetailResponse(baseUrl: AppConstants.apiEndPointLogin));
    Get.lazyPut(() => MyAccountController(getAccountDetailResponse: Get.find()));
  }
}
