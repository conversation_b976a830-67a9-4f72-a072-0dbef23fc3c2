// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:base_controller/controllers/home_controller.dart';
import 'package:presentation/const/app_text_style/presentation_app_text_style.dart';
// import 'package:presentation/const/app_text_style/presentation_app_text_style.dart';
import 'package:presentation_common_ui/widgets/home/<USER>/home_best_seller_brand.dart';
import 'package:presentation_common_ui/widgets/home/<USER>/home_header_slider.dart';
import 'package:presentation_common_ui/widgets/home/<USER>/home_trending_tile_widget.dart';
// import 'package:app_one/theme/app_asset.dart';
import 'package:app_one/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:presentation/const/size_utils.dart';
import '../../all_imports.dart';
import 'package:presentation_common_ui/widgets/home/<USER>/home_banner_widget.dart';
import 'package:presentation_common_ui/widgets/home/<USER>/home_brand_list_widget.dart';
import 'package:presentation_common_ui/widgets/home/<USER>/home_most_popular_widget.dart';
import 'package:presentation_common_ui/widgets/home/<USER>/home_tile_widget.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';
import 'package:presentation_common_ui/common_widget/common_message_dialog/common_message_dialog.dart';

class HomeScreen extends GetView<HomeScreenController> {
  const HomeScreen({Key? key}) : super(key: key);

  static List<String> homeTileIMG = [
    AppAsset.men,
    AppAsset.women,
    AppAsset.kids,
    AppAsset.accessories,
    AppAsset.shoes,
  ];
  static List<String> homeTileTitle = [
    'men',
    'women',
    'kids',
    'shoes',
    'accessories',
  ];

  static List<String> homeTrendingImages = [
    AppAsset.baggy,
    AppAsset.trendy,
    AppAsset.hipHop,
    AppAsset.funky,
  ];
  static List<String> homeTrendingTitle = [
    'BAGGY',
    'TRENDY',
    'HIP-GOP',
    'FUNKY',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: controller.scaffoldkey,
      backgroundColor: whiteColor,
      body: Container(
        margin: EdgeInsets.only(top: MediaQuery.of(context).viewPadding.top, left: 16, right: 16),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Column(
                children: [
                  // SizedBox(height: 60.iY),
                  GestureDetector(
                    onTap: () {
                      DialogUtils.showCustomDialog(context,
                          title: "What is Lorem Ipsum?",
                          msg: "What is Lorem Ipsum?",
                          titalTextStyle: AppTextStyle.textStyleUtils600(
                            color: Theme.of(Get.context!).textTheme.titleLarge!.color,
                            size: 16,
                          ),
                          msgTextStyle: AppTextStyle.textStyleUtils400(color: greyColor, size: 14),
                          buttonColor: darkBlueColor,
                          isSingleButton: true,
                          okBtnFunction: () =>
                              {} /* call method in which you have write your logic and save process  */
                          );
                    },
                    child: HomeHeaderSlider(
                      imageUrlList: [
                        AppAsset.homeSlide1,
                        AppAsset.homeSlide2,
                        AppAsset.homeSlide3,
                      ],
                    ),
                  ),
                  SizedBox(height: 20.iY),
                ],
              ),
            ),
            SliverToBoxAdapter(
              child: SizedBox(
                width: Get.width,
                height: Get.height * 0.75,
                child: Center(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Wrap(
                      spacing: 20.iY,
                      runSpacing: 20.iX,
                      direction: Axis.vertical,
                      children: [
                        for (int index = 0; index < homeTrendingTitle.length; index++)
                          HomeTrendingTileWidget(
                            imageUrl: homeTrendingImages[index],
                            title: homeTrendingTitle[index].toUpperCase(),
                            onTap: () {
                              ///tile onTap action
                            },
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(child: SizedBox(height: 10.iY)),
            const SliverToBoxAdapter(child: HomeBrandLIstWidget()),
            SliverToBoxAdapter(child: SizedBox(height: 20.iY)),
            SliverToBoxAdapter(
              child: HomeBannerWidget(
                imageUrl: AppAsset.productsBanner,
              ),
            ),
            SliverToBoxAdapter(child: SizedBox(height: 20.iY)),
            SliverToBoxAdapter(
              child: HomeBannerWidget(imageUrl: AppAsset.freeShippingBanner),
            ),
            SliverToBoxAdapter(child: SizedBox(height: 20.iY)),
            SliverToBoxAdapter(
              child: HomeBannerWidget(imageUrl: AppAsset.taxInfoBanner),
            ),
            SliverToBoxAdapter(child: SizedBox(height: 20.iY)),
            SliverToBoxAdapter(
              child: MediaQuery.of(context).size.width < 750
                  ? ListView.builder(
                      padding: EdgeInsets.all(16.0),
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: homeTileIMG.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: EdgeInsets.only(bottom: 16.0),
                          child: HomeTileWidget(
                            imageUrl: homeTileIMG[index],
                            title: homeTileTitle[index].toUpperCase(),
                            onTap: () {
                              // Tile onTap action
                            },
                          ),
                        );
                      },
                    )
                  : GridView.builder(
                      padding: EdgeInsets.all(16.0),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 16.0,
                        mainAxisSpacing: 16.0,
                        childAspectRatio: 3 / 2,
                      ),
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: homeTileIMG.length,
                      itemBuilder: (context, index) {
                        return HomeTileWidget(
                          imageUrl: homeTileIMG[index],
                          title: homeTileTitle[index].toUpperCase(),
                          onTap: () {
                            // Tile onTap action
                          },
                        );
                      },
                    ),
            ),
            SliverToBoxAdapter(child: SizedBox(height: 20.iY)),
            Obx(() => SliverToBoxAdapter(
                  child: controller.isLoading.value
                      ? Center(
                          child: SpinKitThreeBounce(
                            color: primaryColor,
                            size: 20.iX,
                          ),
                        )
                      : Column(
                          children: List.generate(
                            controller.homeTileModel.length,
                            (index) => controller.menuModel.value.childrenData![index].isActive ?? false
                                ? HomeMostPopularWidget(
                                    id: controller.menuModel.value.childrenData![index].id.toString(),
                                    title:
                                        controller.menuModel.value.childrenData![index].name.toString(),
                                    productModel: controller.homeTileModel[index],
                                    isEmpty: controller.homeTileModel[index]!.totalCount == 0,
                                  )
                                : Container(),
                          ),
                        ),
                )),
            SliverToBoxAdapter(child: SizedBox(height: 20.iY)),
            SliverToBoxAdapter(child: const HomeBestSellerBrand()),
            SliverToBoxAdapter(child: SizedBox(height: 20.iY)),
            SliverToBoxAdapter(
              child: HomeBannerWidget(imageUrl: AppAsset.newsLetterBanner),
            ),
            SliverToBoxAdapter(child: SizedBox(height: 20.iY)),
            SliverToBoxAdapter(
              child: HomeBannerWidget(imageUrl: AppAsset.homeBanner),
            ),
            SliverToBoxAdapter(child: SizedBox(height: 40.iY)),
          ],
        ),
      ),
    );
  }
}
