import 'package:app_one/all_imports.dart';
import 'package:base_controller/controllers/splash_controllers/app_one_splash_controller.dart';
import 'package:app_one/gen/assets.gen.dart';
// import 'package:app_one/theme/app_asset.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';


class SplashScreen extends GetView<SplashController> {
  const SplashScreen({Key? key}) : super(key: key);
  // final Color backgroundColorValue = const Color(0xffFBECE5);

  @override
  Widget build(BuildContext context) {
    debugPrint("SplashScreen  ${AppAsset.svgLogo}");
    final mq = MediaQuery.of(context).size;
    return SafeArea(
      child: Scaffold(
        body: Container(
          key: Key("AppLogoSplashScreen"),
          height: mq.height,
          width: mq.width,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: controller.backgroundColorValue.value,
          ),
          child: SvgPicture.asset(
            AppAsset.svgLogo,
            fit: BoxFit.fitWidth,
          ),
        ),
      ),
    );
  }
}
