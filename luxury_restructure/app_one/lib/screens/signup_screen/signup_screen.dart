import 'package:app_one/app_constants/app_constants.dart';
import 'package:base_controller/controllers/all_imports_controllers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:presentation_common_ui/common_screens/common_sign_up_screen/common_sign_up_screen.dart';

class SignupScreen extends GetView<SignupScreenController> {
  const SignupScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: const CommonSignUpScreen(projectName: APP_NAME),
    );
  }
}
