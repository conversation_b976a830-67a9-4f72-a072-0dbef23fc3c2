import 'package:app_one/app_pages/app_routes.dart';
import 'package:presentation/widgets/common_buttons/common_theme_button.dart';

import '../../all_imports.dart';
import '../../core/consts/app_constants.dart';
import '../../theme/colors.dart';

class TrackYourRequestNegative extends StatelessWidget {
  const TrackYourRequestNegative({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              LanguageConstants.weCannotFindAnyOrdersEmail.tr,
              textAlign: TextAlign.center,
              style: const TextStyle(fontFamily: AppConstants.fontOpenSans, color: Colors.black, fontSize: 14),
            ),
            const SizedBox(height: 10),
            Text(
              LanguageConstants.pleaseTryWithAnotherEmailAddress.tr,
              textAlign: TextAlign.center,
              style: const TextStyle(fontFamily: AppConstants.fontOpenSans, color: Colors.black, fontSize: 14),
            ),
            const SizedBox(height: 16),
            CommonThemeButton(
              onTap: () {
                Get.offAllNamed<dynamic>(RoutesConstants.dashboardScreen);
              },
              title: LanguageConstants.continueShopping.tr,
            ),
          ],
        ),
      ),
    );
  }
}
