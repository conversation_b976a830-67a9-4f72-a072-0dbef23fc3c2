import 'package:app_one/app_constants/app_constants.dart';
import 'package:base_controller/controllers/track_your_order_controller.dart';
import 'package:presentation/navigation/navigation.dart';
import 'package:presentation_common_ui/common_screens/common_track_your_order_screen/common_track_your_order_screen.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';
import '../../all_imports.dart';
import 'package:app_one/app_pages/app_routes.dart';

class TrackYourOrderScreen extends GetView<TrackYourOrderController> {
  const TrackYourOrderScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return CommonTrackYourOrder(
        projectName:APP_NAME,
        // formKey: controller.formKey,
        onTapOrderReferenceText: () {},
        // onTapLogin: () {
        //   if (controller.formKey.currentState!.validate()) {
        //     print('DONE');
        //   }
        // },
        onTapLetsGo: () {},
        // registeredUserEmailController: controller.registeredUserEmailController,
        // guestUserEmailController: controller.guestUserEmailController,
        // passwordController: controller.passwordController,
        // orderNumberController: controller.orderNumberController,
        // projectName: 'app_one',
        // titleTextAlignment: Alignment.center,
        //
        // passwordTextFieldText: '${LanguageConstants.password.tr} *',
        // isPasswordVisible: controller.isPasswordVisible,
        passwordTextFieldSuffixIcon: controller.isPasswordVisible.value ?  AppAsset.visibilityOn : AppAsset.visibilityOff,
        // onTapSuffixIcon: () {
        //   controller.isPasswordVisible(!controller.isPasswordVisible.value);
        // },

        onForgotPasswordButtonTap: () {
          Get.find<Navigation>().navigateToNextPage(
            route: RoutesConstants.forgotPasswordScreen,
          );
          // Get.toNamed<dynamic>(RoutesConstants.forgotPasswordScreen);
        },
        // loginDecoration: BoxDecoration(borderRadius: BorderRadius.circular(12), color: Theme.of(context).primaryColor),
        // loginButtonStyle: OutlinedButton.styleFrom(
        //     shape: RoundedRectangleBorder(
        //         borderRadius: BorderRadius.circular(12.0))),
        // loginUpButtonWidth: MediaQuery.of(context).size.width,
        // orderReferenceTextStyle: AppStyle.textStyleUtilsUnderLine14(color:Theme.of(context).primaryColor),

        // textFieldDecoration: InputDecoration(
        //   border: OutlineInputBorder(
        //       borderRadius: BorderRadius.circular(10),
        //       borderSide: BorderSide(color: Theme.of(context).dividerColor)
        //   ),
        //   enabledBorder: OutlineInputBorder(
        //       borderRadius: BorderRadius.circular(10),
        //       borderSide: BorderSide(color: Theme.of(context).dividerColor)
        //   ),
        //   disabledBorder: OutlineInputBorder(
        //       borderRadius: BorderRadius.circular(10),
        //       borderSide: BorderSide(color: Theme.of(context).dividerColor)
        //   ),
        // ),

      );
    });
  }
}
