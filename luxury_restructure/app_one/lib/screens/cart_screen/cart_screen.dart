import 'package:app_one/app_constants/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:presentation_common_ui/common_screens/common_cart_screen/common_cart_screen.dart';

class CartScreen extends StatefulWidget {
  CartScreen({Key? key}) : super(key: key);

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {

  @override
  Widget build(BuildContext context) {
    return const CommonCartScreen(
      projectName: APP_NAME,
    );
    // return Scaffold(
    //   // appBar: commonAppbar(
    //   //     titleWidget: CommonTextPoppins(LanguageConstants.cartDetails.tr,
    //   //         height: 1.5.iY)),
    //   body: Obx(
    //     () => controller.isMainLoading.value
    //         ? const Center(
    //             child: ScreenLoading(appColor: darkBlueColor),
    //           )
    //         : Padding(
    //             padding: EdgeInsets.only(
    //                 top: MediaQuery.of(context).size.height * .05),
    //             child: _buildBody(context),
    //           ),
    //   ),
    // );
  }

  // Widget _buildBody(BuildContext context) {
  //   final ButtonsDecorationModal appButtonsDecoration =
  //       getButtonDecorationModal(APP_NAME);
  //   return Stack(
  //     children: [
  //       ListView(
  //         physics: const BouncingScrollPhysics(),
  //         children: [
  //           const SizedBox(
  //             height: 20,
  //           ),
  //           //Item part
  //
  //           _buildItemContainer(context),
  //           SizedBox(
  //             height: 10.iY,
  //           ),
  //           //Common coupon applied
  //           if (controller.showAppliedCoupons.value)
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //               children: [
  //                 CommonTextPoppins(
  //                   controller.getTotalsData.value.getDiscountName() == null
  //                       ? '${LanguageConstants.discount.tr}(${controller.promoCodeController.text})'
  //                       : controller.getTotalsData.value.getDiscountName()!,
  //                   fontSize: 14.iY,
  //                   height: 1.iY,
  //                   color: Theme.of(context).textTheme.displayMedium?.color,
  //                   fontWeight: FontWeight.w600,
  //                   textAlign: TextAlign.left,
  //                 ),
  //                 CommonTextPoppins(
  //                   controller.getTotalsData.value.getDiscount() ?? '',
  //                   fontSize: 14.iY,
  //                   textAlign: TextAlign.left,
  //                   height: 1.5.iY,
  //                   color: Theme.of(context).textTheme.displayMedium?.color,
  //                 ),
  //               ],
  //             ),
  //
  //           const SizedBox(
  //             height: 14,
  //           ),
  //
  //           Obx(() => controller.isInValid.value == true
  //               ? _handleError(context, controller.promoCodeController.text)
  //               : Container()),
  //           SizedBox(
  //             height: 8.iY,
  //           ),
  //
  //           //list of coupon if present
  //           // if (controller.cartModel.value.items?.isNotEmpty ?? false)
  //           //   GestureDetector(
  //           //     onTap: () {
  //           //       showAllCouponsList(controller);
  //           //     },
  //           //     child: Align(
  //           //       alignment: Alignment.center,
  //           //       child: CommonTextPoppins(LanguageConstants.viewCouponList.tr,
  //           //           fontWeight: FontWeight.w400,
  //           //           color: Theme.of(context).textTheme.displayMedium?.color,
  //           //           fontSize: 14.iY,
  //           //           height: 1.5.iY),
  //           //     ),
  //           //   ),
  //           (controller.cartModel.value.items?.isEmpty ?? false)
  //               ? Padding(
  //                   padding: EdgeInsets.only(
  //                     bottom: 30.iY,
  //                     top: 20.iY,
  //                   ),
  //                   child: Text(
  //                     LanguageConstants.youHaveNoItemsInYourShoppingCart.tr,
  //                     textAlign: TextAlign.center,
  //                     style: AppStyle.textStyleUtils400(
  //                         size: 16,
  //                         color:
  //                             Theme.of(context).textTheme.displayMedium?.color),
  //                   ),
  //                 )
  //               : const SizedBox.shrink(),
  //           const SizedBox(
  //             height: 20,
  //           ),
  //           // const Divider(color: regularGrey),
  //           (controller.cartModel.value.items?.isNotEmpty ?? false)
  //               ? Padding(
  //                   padding: EdgeInsets.symmetric(horizontal: 15.iX),
  //                   child: Container(
  //                     width: Get.width,
  //                     decoration: BoxDecoration(
  //                         color: Theme.of(context).scaffoldBackgroundColor,
  //                         // border: Border.all(color: Theme.of(context).textTheme.displayMedium?.color ?? blackColor),
  //                         borderRadius: BorderRadius.circular(10)),
  //                     child: Column(
  //                       crossAxisAlignment: CrossAxisAlignment.start,
  //                       children: [
  //                         Container(
  //                           height: 45.iY,
  //                           width: double.infinity,
  //                           decoration: BoxDecoration(
  //                             color: Theme.of(context).scaffoldBackgroundColor,
  //                             borderRadius: BorderRadius.only(
  //                               topLeft: Radius.circular(10.iY),
  //                               topRight: Radius.circular(10.iY),
  //                             ),
  //                           ),
  //                           child: Align(
  //                             alignment: Alignment.centerLeft,
  //                             child: Padding(
  //                               padding: EdgeInsets.only(left: 15.iX),
  //                               child: CommonTextPoppins(
  //                                 "${LanguageConstants.price.tr} (${controller.cartModel.value.items?.length} ${LanguageConstants.itemText.tr})",
  //                                 fontSize: 16.iY,
  //                                 fontWeight: FontWeight.w600,
  //                                 height: 1.5.iY,
  //                                 color: Theme.of(context)
  //                                     .textTheme
  //                                     .displayMedium
  //                                     ?.color,
  //                               ),
  //                             ),
  //                           ),
  //                         ),
  //                         Padding(
  //                           padding: EdgeInsets.only(
  //                               left: 18.iX,
  //                               right: 20.iX,
  //                               top: 15.iY,
  //                               bottom: 15.iY),
  //                           child: Column(
  //                             children: [
  //                               /// SUB TOTAL
  //                               Row(
  //                                 mainAxisAlignment:
  //                                     MainAxisAlignment.spaceBetween,
  //                                 children: [
  //                                   Text(
  //                                     LanguageConstants.cartSubTotalText.tr,
  //                                     style: AppStyle.textStyleUtils400(
  //                                         size: 16.0,
  //                                         color: Theme.of(context)
  //                                             .textTheme
  //                                             .displayMedium
  //                                             ?.color),
  //                                   ),
  //                                   Text(
  //                                     controller.getTotalsData.value
  //                                             .getSubTotal() ??
  //                                         "0.0",
  //                                     style: AppStyle.textStyleUtils400(
  //                                         size: 16.0,
  //                                         color:
  //                                             Theme.of(context).primaryColor),
  //                                   ),
  //                                 ],
  //                               ),
  //                               SizedBox(
  //                                 height: 15.iY,
  //                               ),
  //
  //                               /// Shipping
  //                               Row(
  //                                 children: [
  //                                   Align(
  //                                     alignment: Alignment.center,
  //                                     child: Text(
  //                                       LanguageConstants.shipping.tr,
  //                                       style: AppStyle.textStyleUtils400(
  //                                         size: 16.0,
  //                                         color: Theme.of(context)
  //                                             .textTheme
  //                                             .displayMedium
  //                                             ?.color,
  //                                       ),
  //                                     ),
  //                                   ),
  //                                   const Spacer(),
  //                                   Align(
  //                                     alignment: Alignment.center,
  //                                     child: Text(
  //                                       controller.getTotalsData.value
  //                                               .getShipping() ??
  //                                           '',
  //                                       style: AppStyle.textStyleUtils400(
  //                                         size: 16.0,
  //                                         color: Theme.of(context)
  //                                             .textTheme
  //                                             .displayMedium
  //                                             ?.color,
  //                                       ),
  //                                     ),
  //                                   ),
  //                                 ],
  //                               ),
  //                               SizedBox(
  //                                 height: 15.iY,
  //                               ),
  //
  //                               /// Discount
  //                               Row(
  //                                 children: [
  //                                   Align(
  //                                     alignment: Alignment.center,
  //                                     child: Text(
  //                                       LanguageConstants.discount.tr,
  //                                       style: AppStyle.textStyleUtils400(
  //                                         size: 16.0,
  //                                         color: Theme.of(context)
  //                                             .textTheme
  //                                             .displayMedium
  //                                             ?.color,
  //                                       ),
  //                                     ),
  //                                   ),
  //                                   const Spacer(),
  //                                   Align(
  //                                     alignment: Alignment.center,
  //                                     child: Text(
  //                                       controller.getTotalsData.value
  //                                               .getDiscount() ??
  //                                           '',
  //                                       style: AppStyle.textStyleUtils400(
  //                                         size: 16.0,
  //                                         color: Theme.of(context)
  //                                             .textTheme
  //                                             .displayMedium
  //                                             ?.color,
  //                                       ),
  //                                     ),
  //                                   ),
  //                                 ],
  //                               ),
  //                               SizedBox(
  //                                 height: 15.iY,
  //                               ),
  //                             ],
  //                           ),
  //                         )
  //                       ],
  //                     ),
  //                   ),
  //                 )
  //               : Padding(
  //                   padding: EdgeInsets.symmetric(horizontal: 50.iX),
  //                   child: CustomOutlinedButton(
  //                     text: LanguageConstants.continueShopping.tr,
  //                     buttonTextStyle:
  //                         appButtonsDecoration.primaryButtonTextStyle,
  //                     decoration: appButtonsDecoration.primaryButtonDecoration,
  //                     onTap: () {
  //                       controller
  //                           .dashboardController?.tabController.value.index = 0;
  //                     },
  //                   ),
  //                 ),
  //           const SizedBox(
  //             height: 25,
  //           ),
  //           if (controller.cartModel.value.items?.isNotEmpty ?? false)
  //             _buildApplyCouponField(context),
  //           const SizedBox(
  //             height: 5,
  //           ),
  //
  //           /// Available Coupon List Text
  //           if (controller.cartModel.value.items?.isNotEmpty ?? false)
  //             GestureDetector(
  //               onTap: () {
  //                 showAllCouponsList(controller);
  //               },
  //               child: Align(
  //                 alignment: Alignment.center,
  //                 child: CommonTextPoppins(LanguageConstants.viewCouponList.tr,
  //                     fontWeight: FontWeight.w700,
  //                     letterSpacing: 0.24,
  //                     decoration: TextDecoration.underline,
  //                     color: Theme.of(context).primaryColor,
  //                     fontSize: 14.iX,
  //                     height: 1.5.iY),
  //               ),
  //             ),
  //           // ignore: prefer_const_constructors
  //           // if (controller.donationList.isNotEmpty) DonateDesign(),
  //
  //           /// Total Order
  //           if (controller.cartModel.value.items?.isNotEmpty ?? false)
  //             Padding(
  //               padding: EdgeInsets.only(
  //                   left: 18.iX, right: 20.iX, top: 15.iY, bottom: 15.iY),
  //               child: Row(
  //                 children: [
  //                   Align(
  //                     alignment: Alignment.center,
  //                     child: Text(
  //                       LanguageConstants.totalText.tr,
  //                       style: AppStyle.textStyleUtils400(
  //                         size: 16.0,
  //                         color:
  //                             Theme.of(context).textTheme.displayMedium?.color,
  //                       ),
  //                     ),
  //                   ),
  //                   const Spacer(),
  //                   Align(
  //                     alignment: Alignment.center,
  //                     child: Text(
  //                       controller.getTotalsData.value.getGrandTotal() ?? '0.0',
  //                       style: AppStyle.textStyleUtils700(
  //                         size: 16.0,
  //                         color:
  //                             Theme.of(context).textTheme.displayMedium?.color,
  //                       ),
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //             ),
  //
  //           /// Check Out Button
  //           if (controller.cartModel.value.items?.isNotEmpty ?? false)
  //             Padding(
  //               padding: EdgeInsets.only(
  //                   left: 18.iX, right: 20.iX, top: 15.iY, bottom: 15.iY),
  //               child: Align(
  //                 child: SizedBox(
  //                   height: 45.iX,
  //                   width: MediaQuery.of(context).size.width,
  //                   child: CommonButton(
  //                     buttonType: ButtonType.elevatedButton,
  //                     color: Theme.of(context).primaryColor,
  //                     borderRadius: 5.0,
  //                     onPressed: () async {
  //                       await Get.toNamed<dynamic>(
  //                         RoutesConstants.checkoutOrderScreen,
  //                         arguments: {'cartModel': controller.cartModel.value},
  //                       );
  //                       controller.getTotals(projectName: APP_NAME);
  //                     },
  //                     child: CommonTextPoppins(
  //                       LanguageConstants.checkOutText.tr,
  //                       fontWeight: FontWeight.w700,
  //                       fontSize: 16.iY,
  //                       height: 1.iY,
  //                       color: Colors.white,
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //             ),
  //
  //           /// Checkout with Multiple Address
  //           if (controller.cartModel.value.items?.isNotEmpty ?? false)
  //             Align(
  //               alignment: Alignment.center,
  //               child: CommonTextPoppins(
  //                   LanguageConstants.checkOutMultipleAddressText.tr,
  //                   fontWeight: FontWeight.w700,
  //                   letterSpacing: 0.24,
  //                   decoration: TextDecoration.underline,
  //                   color: Theme.of(context).primaryColor,
  //                   fontSize: 14.iX,
  //                   height: 1.5.iY),
  //             ),
  //         ],
  //       ),
  //       controller.isLoading.value
  //           ? const ScreenLoading(
  //               appColor: darkBlueColor,
  //             )
  //           : const SizedBox(),
  //     ],
  //   );
  // }
  //
  // Widget _buildApplyCouponField(BuildContext context) {
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 12),
  //     child: Container(
  //       height: 65.iY,
  //       width: MediaQuery.of(context).size.width,
  //       decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.iY)),
  //       child: Row(
  //         children: [
  //           Expanded(
  //             child: Form(
  //               key: controller.formKey,
  //               child: TextFormField(
  //                 cursorColor: Theme.of(context).scaffoldBackgroundColor,
  //                 controller: controller.promoCodeController,
  //                 validator: (value) {
  //                   if (Validators.validateRequired(value?.trim() ?? '', '*') !=
  //                       null) {
  //                     controller.isInValid.value = true;
  //                   } else {
  //                     controller.isInValid.value = false;
  //                   }
  //                   return null;
  //                 },
  //                 decoration: InputDecoration(
  //                   filled: true,
  //                   fillColor: Colors.transparent,
  //                   contentPadding: const EdgeInsets.only(
  //                     bottom: 10,
  //                     top: 10,
  //                     left: 10,
  //                   ),
  //                   hintText: LanguageConstants.enterDiscountCodeText.tr,
  //                   hintStyle: AppStyle.textStyleUtils400(
  //                     color: CommonColors.blackColor.withOpacity(0.6),
  //                   ),
  //                   labelStyle:
  //                       AppTextStyle.textStyleUtils400(color: Colors.black54),
  //                   errorStyle:
  //                       AppTextStyle.textStyleUtils400(color: Colors.red),
  //                   focusedBorder: OutlineInputBorder(
  //                     borderRadius: BorderRadius.circular(50),
  //                     borderSide: const BorderSide(color: lightGreyColor),
  //                   ),
  //                   enabledBorder: OutlineInputBorder(
  //                       borderRadius: BorderRadius.circular(50),
  //                       borderSide: const BorderSide(color: lightGreyColor)),
  //                   suffixIcon: Padding(
  //                     padding: const EdgeInsets.all(2.0),
  //                     child: SizedBox(
  //                       height: 55.iY,
  //                       // width: 80,
  //                       child: CommonButton(
  //                         buttonType: ButtonType.elevatedButton,
  //                         borderRadius: 50,
  //                         color: Theme.of(context).primaryColor,
  //                         onPressed: () {
  //                           if (controller.showAppliedCoupons.value) {
  //                             controller.deleteAppliedCoupon();
  //                           } else {
  //                             if ((controller.formKey.currentState
  //                                         ?.validate() ??
  //                                     false) &&
  //                                 controller.isInValid.value == false) {
  //                               controller.addCouponsToCartForField();
  //                             }
  //                           }
  //                         },
  //                         child: CommonTextPoppins(
  //                           controller.showAppliedCoupons.value
  //                               ? LanguageConstants.removeCoupon.tr
  //                               : LanguageConstants.applyText.tr,
  //                           height: 1.iY,
  //                           fontSize: 16.iY,
  //                           fontWeight: FontWeight.w700,
  //                           color: whiteColor,
  //                         ),
  //                       ),
  //                     ),
  //                   ),
  //                   border: OutlineInputBorder(
  //                     borderSide: BorderSide(
  //                         color: Theme.of(context)
  //                                 .textTheme
  //                                 .displayMedium
  //                                 ?.color ??
  //                             lightGreyColor),
  //                     borderRadius: BorderRadius.circular(50),
  //                   ),
  //                 ),
  //               ),
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }
  //
  // Widget _buildItemContainer(BuildContext context) {
  //   return CommonItemCartContainer(
  //     containerBackgroundColor: Theme.of(context).scaffoldBackgroundColor,
  //     containerBorderColor:
  //         Theme.of(context).textTheme.displayMedium?.color ?? lightGreyColor,
  //     isListEmpty: (controller.cartModel.value.items != null &&
  //             controller.cartModel.value.items!.isNotEmpty)
  //         ? false
  //         : true,
  //     containerHeadingTextColor:
  //         Theme.of(context).textTheme.displayMedium?.color,
  //     headingContainerBackgroundColor:
  //         Theme.of(context).scaffoldBackgroundColor,
  //     cartProductListView: ListView.builder(
  //       physics: const NeverScrollableScrollPhysics(),
  //       padding: EdgeInsets.zero,
  //       shrinkWrap: true,
  //       // itemCount: 3,
  //       itemCount: controller.cartModel.value.items?.length ?? 0,
  //       itemBuilder: (context, index) => Container(
  //         width: Get.width,
  //         margin: EdgeInsets.only(
  //           left: 20.iX,
  //           right: 20.iX,
  //         ),
  //         child: Column(
  //           children: [
  //             Stack(
  //               alignment: Alignment.bottomRight,
  //               children: [
  //                 InkWell(
  //                   onTap: () {
  //                     // Get.toNamed<void>(
  //                     //     RoutesConstants
  //                     //         .productDetailsScreen,
  //                     //     parameters: {
  //                     //       kRouteParameterProductSkuKey:
  //                     //       controller
  //                     //           .cartModel
  //                     //           .value
  //                     //           .items?[
  //                     //       index]
  //                     //           .sku ??
  //                     //           ''
  //                     //     })?.whenComplete(
  //                     //       () {
  //                     //     controller
  //                     //         .getGenerateCart();
  //                     //   },
  //                     // );
  //                   },
  //                   child: Row(
  //                     crossAxisAlignment: CrossAxisAlignment.start,
  //                     children: [
  //                       InkWell(
  //                         child: ClipRRect(
  //                           borderRadius: BorderRadius.circular(10),
  //                           child: Container(
  //                             width: 150.iX,
  //                             height: 150.iY,
  //                             color: Theme.of(context).primaryColor,
  //                             child: Center(
  //                               child: Stack(
  //                                 children: [
  //                                   // controller.getProductImage(index) != ""
  //                                   //     ?
  //                                   CachedNetworkImage(
  //                                     width: 97.iX,
  //                                     height: 91.iY,
  //                                     fit: BoxFit.cover,
  //                                     imageUrl:
  //                                         'controller.getProductImage(index)',
  //                                     //  placeholder: (context, url) => const CircularProgressIndicator(),
  //                                     progressIndicatorBuilder:
  //                                         (context, url, downloadProgress) =>
  //                                             Lottie.asset(
  //                                       AppAsset.logo,
  //                                       width: 65,
  //                                     ),
  //                                     errorWidget: (context, url, error) =>
  //                                         const Icon(
  //                                       Icons.error,
  //                                     ),
  //                                   )
  //                                   // :
  //                                   // const SizedBox()
  //                                   ,
  //                                 ],
  //                               ),
  //                             ),
  //                           ),
  //                         ),
  //                       ),
  //                       SizedBox(
  //                         width: 30.iX,
  //                       ),
  //                       Expanded(
  //                         child: Column(
  //                           crossAxisAlignment: CrossAxisAlignment.start,
  //                           children: [
  //                             Text(
  //                               // controller.getProductName(
  //                               'product name',
  //                               // ),
  //                               overflow: TextOverflow.ellipsis,
  //                               maxLines: 2,
  //                               style: AppStyle.textStyleUtils400(
  //                                   size: 16.0,
  //                                   color: Theme.of(context)
  //                                       .textTheme
  //                                       .displayMedium
  //                                       ?.color),
  //                             ),
  //                             SizedBox(
  //                               height: 10.iY,
  //                             ),
  //                             Text(
  //                               LanguageConstants.size32.tr,
  //                               overflow: TextOverflow.ellipsis,
  //                               maxLines: 2,
  //                               style: AppStyle.textStyleUtils400(
  //                                   size: 16.0,
  //                                   color: Theme.of(context)
  //                                       .textTheme
  //                                       .displayMedium
  //                                       ?.color),
  //                             ),
  //                             SizedBox(
  //                               height: 10.iY,
  //                             ),
  //                             Row(
  //                               children: [
  //                                 Text(
  //                                   LanguageConstants.colorBlack.tr,
  //                                   overflow: TextOverflow.ellipsis,
  //                                   maxLines: 2,
  //                                   style: AppStyle.textStyleUtils400(
  //                                       size: 16.0,
  //                                       color: Theme.of(context)
  //                                           .textTheme
  //                                           .displayMedium
  //                                           ?.color),
  //                                 ),
  //                                 const Spacer(),
  //                               ],
  //                             ),
  //                           ],
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               ],
  //             ),
  //             const SizedBox(
  //               height: 15,
  //             ),
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //               children: [
  //                 Row(
  //                   crossAxisAlignment: CrossAxisAlignment.center,
  //                   children: [
  //                     Text(
  //                       controller.firstCapitalize(
  //                         LanguageConstants.qtyText.tr,
  //                       ),
  //                       style: AppStyle.textStyleUtils600(
  //                           size: 16.iY,
  //                           color: Theme.of(context)
  //                               .textTheme
  //                               .displayMedium
  //                               ?.color),
  //                     ),
  //                     SizedBox(
  //                       width: 10.iX,
  //                     ),
  //                     InkWell(
  //                         onTap: () {
  //                           if (controller.cartModel.value.items?[index].qty ==
  //                               1) {
  //                             confirmationDialogForRemove(
  //                               index: index,
  //                               context: context,
  //                               dialogBackgroundColor: const Color(0xffFBECE5),
  //                               buttonColor: appColor,
  //                               textColor: appColor,
  //                               textStyle:
  //                                   AppTextStyle.textStyleUtils500(size: 15),
  //                               removeProductText:
  //                                   LanguageConstants.removeProduct.tr,
  //                               confirmationText: LanguageConstants
  //                                   .areYouSureWantToRemoveThisProductFromCart
  //                                   .tr,
  //                               yesText: LanguageConstants.yes.tr,
  //                               noText: LanguageConstants.no.tr,
  //                               onDelete: (index, type) {
  //                                 controller.deleteCartProductContent(
  //                                     index, type);
  //                               },
  //                             );
  //
  //                             ///COMBINE WIDGET
  //                             // confirmationDialogForRemove(index, context);
  //                           } else {
  //                             controller.postRemoveFromCartData(
  //                               index,
  //                             );
  //                           }
  //                         },
  //                         child: SvgPicture.asset(AppAsset.minus)),
  //                     Icon(Icons.remove),
  //                     Container(
  //                       width: 30.iX,
  //                       height: 32.iY,
  //                       alignment: Alignment.center,
  //                       child: Text("${index}"),
  //                       // CartNumber(
  //                       //   index,
  //                       // ),
  //                     ),
  //                     Icon(Icons.add),
  //                     InkWell(
  //                         onTap: () {
  //                           controller.increaseProductQty(
  //                             index,
  //                           );
  //                           // controller.
  //                         },
  //                         child: SvgPicture.asset(AppAsset.plus)),
  //                   ],
  //                 ),
  //                 // CartPrice(index),
  //                 Text(
  //                   controller.localStore.getPriceWithSymbol(
  //                     '2323',
  //                   ),
  //                   style: AppStyle.textStyleUtils500(size: 16.iY),
  //                 ),
  //                 Row(
  //                   children: [
  //                     SvgPicture.asset(
  //                       AppAsset.heart,
  //                       color: Colors.black,
  //                       height: 15.iY,
  //                     ),
  //                     SizedBox(
  //                       width: 8.iX,
  //                     ),
  //                     InkWell(
  //                       onTap: () {
  //                         // Get.toNamed<void>(
  //                         //     RoutesConstants
  //                         //         .productDetailsScreen,
  //                         //     parameters: {
  //                         //       kRouteParameterProductSkuKey:
  //                         //       controller.cartModel.value.items?[index].sku ?? ''
  //                         //     });
  //                       },
  //                       child: Icon(
  //                         Icons.edit_outlined,
  //                         size: 15.iY,
  //                         color:
  //                             Theme.of(context).textTheme.displayMedium?.color,
  //                       ),
  //                     ),
  //                     SizedBox(
  //                       width: 8.iX,
  //                     ),
  //                     InkWell(
  //                       onTap: () {
  //                         // confirmationDialogForRemoveCartProduct(index, context);
  //                         CommonDialog.confirmationDialogForRemoveCartProduct(
  //                             context, index);
  //                       },
  //                       child: Icon(
  //                         Icons.delete_outline_outlined,
  //                         size: 15.iY,
  //                         color:
  //                             Theme.of(context).textTheme.displayMedium?.color,
  //                       ),
  //                     ),
  //                     /*   SizedBox(
  //                       width: 100.iX,
  //                       child: Align(
  //                         alignment: Alignment.centerRight,
  //                         child: Text(
  //                           controller.firstCapitalize(
  //                             '${LanguageConstants.subTotal.tr}   2323',
  //                           ),
  //                           overflow: TextOverflow.ellipsis,
  //                           style: AppStyle.textStyleUtils600(size: 14.iY, color: Theme.of(context).textTheme.displayMedium?.color),
  //                         ),
  //                       ),
  //                     ),*/
  //                   ],
  //                 ),
  //               ],
  //             ),
  //             SizedBox(
  //               height: 20.iY,
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }
  //
  // Widget _handleError(BuildContext context, String value) {
  //   if (!controller.isInValid.value) {
  //     return Container();
  //   }
  //
  //   return Align(
  //     alignment: Alignment.centerLeft,
  //     child: Padding(
  //       padding: EdgeInsets.symmetric(horizontal: 20.iX),
  //       child: Text(
  //         Validators.validateRequired(value.trim(), '*') as String,
  //         style: AppTextStyle.textStyleUtils400(size: 12.iY, color: Colors.red),
  //         textAlign: TextAlign.left,
  //       ),
  //     ),
  //   );
  // }
}
