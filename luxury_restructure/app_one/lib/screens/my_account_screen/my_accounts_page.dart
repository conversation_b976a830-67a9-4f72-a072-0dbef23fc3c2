import 'package:app_one/screens/my_account_screen/theme_change_toggle.dart';
import 'package:flutter/cupertino.dart';
import 'package:presentation_common_ui/common_screens/common_my_account_page/common_my_account_page.dart';
import 'package:base_controller/controllers/presentation_common_ui_controller/my_account_controller.dart';
import 'package:presentation_common_ui/constants/app_constants.dart';
import '../../all_imports.dart';
import '../../app_pages/app_routes.dart';

class MyAccountsPage extends GetView<MyAccountController> {
  // final MyAccountPageNavigation myAccountPageNavigation;
  // final ThemeChangeToggle themeChangeToggle;
  // final showTitleDialog;

  const MyAccountsPage(
      /*this.myAccountPageNavigation, this.themeChangeToggle,
      this.showTitleDialog,*/
      {Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CommonMyAccountMenuPage(
      projectName: APP_ONE_NAME,
      myAccountPageNavigation: MyAccountPageNavigation(
        contactUsScreenPath: RoutesConstants.contactUsScreen,
        loginScreenRoute: RoutesConstants.loginScreen,
        signupScreenRoute: RoutesConstants.signUpScreen,
        myWishListScreenRoute: RoutesConstants.wishlistScreen,
        addressBookScreenRoute: '',
        //RoutesConstants.addressBookScreen,
        accountInformationScreenRoute: RoutesConstants.myAccountScreen,
        myStoreCreditScreenRoute: RoutesConstants.storeCreditScreen,
        myCouponsScreenRoute: RoutesConstants.myCouponsScreen,
        trackOrderScreenRoute: RoutesConstants.trackYourOrderScreen,
        notificationScreenRoute: RoutesConstants.notificationScreen,
        secureShoppingScreenRoute:RoutesConstants.appSecureShoppingScreen,
        //RoutesConstants.appSecureShoppingScreen,
        trackYourOrderByGuestScreenRoute: RoutesConstants.guestReturnsScreen,
        hopeScreenRoute: RoutesConstants.charityScreen,
        affiliateProgramScreenRoute: RoutesConstants.affiliateProgramScreen,
        influenceRegistrationScreenRoute:
            RoutesConstants.influencerRegistrationScreen,
        exchangeAndReturnsScreenRoute: RoutesConstants.exchangeAndReturnsScreen,
        privacyPolicyScreenRoute: RoutesConstants.privacyPolicyScreen,
        termsAndConditionsScreenRoute: RoutesConstants.termsConditionScreen,
        shippingScreenRoute: RoutesConstants.shippingScreen,
        trackYourTicketByEmailScreenRoute: RoutesConstants.traceYourTicketMail,
        trackTicketScreenRoute: RoutesConstants.trackTicketScreen,
        aboutUsScreenRoute: '',
        referFriendScreenRoute: RoutesConstants.referFriendScreen,
        returnsAndRefundScreenRoute: RoutesConstants.returnsAndRefundsScreen,
        faqScreenRoute: RoutesConstants.faqScreen,
        liveChatScreenRoute: '',
        myOrdersScreen: RoutesConstants.myOrderScreen,
        countryRoute: RoutesConstants.countryScreen,
        myTicketScreen: RoutesConstants.myTicketsScreen,
        advancedsearch: '',
        giftvouchers: '',
        splashScreenRoute: RoutesConstants.dashboardScreen,
      ),
      themeChangeToggle: const ThemeChangeToggle(),
      appTileBackgroundColor: tileBackgroundColor,
    );
    // GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
    // return Scaffold(
    //     key: scaffoldKey,
    //     // appBar: AppBar(
    //     //   elevation: 0,
    //     //   backgroundColor: Theme.of(context).scaffoldBackgroundColor,
    //     //   bottom: PreferredSize(
    //     //     preferredSize: Size(Get.width, 10.iX),
    //     //     child: const SizedBox(),
    //     //   ),
    //     //   iconTheme: const IconThemeData(color: appColor),
    //     // ),
    //     appBar: commonAppbar(
    //       elevation: 0,
    //       backgroundColor: Theme.of(context).scaffoldBackgroundColor,
    //       bottom: PreferredSize(
    //         preferredSize: Size(Get.width, 10.iX),
    //         child: const SizedBox(),
    //       ),
    //       iconTheme: const IconThemeData(color: appColor),
    //     ),
    //     body: Obx(
    //       () => controller.isLoading.value
    //           ? const Center(
    //               child: SpinKitThreeBounce(color: appColor),
    //             )
    //           : SingleChildScrollView(
    //               physics: const BouncingScrollPhysics(),
    //               child: Column(
    //                 crossAxisAlignment: CrossAxisAlignment.start,
    //                 children: [
    //                   if (localStore.customerToken.toString() != "" &&
    //                       controller.accountDetail?.value.firstname != null)
    //                     Padding(
    //                       padding: EdgeInsets.symmetric(horizontal: 24.iX, vertical: 13.iY),
    //                       child: Row(
    //                         children: [
    //                           Text(
    //                             "Inside my account",
    //                             style: AppStyle.textStyleUtils600(
    //                               color: Theme.of(context).textTheme.titleLarge!.color,
    //                               size: 20.iY,
    //                             ),
    //                           ),
    //                           Text(
    //                             controller.accountDetail?.value.firstname ?? '',
    //                             style: AppStyle.textStyleUtils700(
    //                               color: Theme.of(context).textTheme.titleLarge!.color,
    //                               size: 20.iY,
    //                             ),
    //                           ),
    //                         ],
    //                       ),
    //                     ),
    //                   if (localStore.customerToken.toString() != "")
    //                     Padding(
    //                       padding: EdgeInsets.symmetric(horizontal: 24.iX),
    //                       child: Text(
    //                         controller.accountDetail?.value.email ?? '',
    //                         style: AppStyle.textStyleUtils600(
    //                           color: Theme.of(context).textTheme.titleLarge!.color,
    //                         ),
    //                       ),
    //                     ),
    //                   Align(
    //                     alignment: Alignment.centerLeft,
    //                     child: Padding(
    //                       padding: EdgeInsets.symmetric(horizontal: 24.iX, vertical: 13.iY),
    //                       child: Text(
    //                         LanguageConstants.myAccountText.tr,
    //                         style: AppStyle.textStyleUtils600(
    //                             color: Theme.of(context).textTheme.titleLarge!.color, size: 20.iY),
    //                       ),
    //                     ),
    //                   ),
    //                   localStore.customerToken.toString() != ""
    //                       ? Container()
    //                       : Center(
    //                           child: Container(
    //                             padding: const EdgeInsets.fromLTRB(20, 10, 20, 30),
    //                             child: Column(
    //                               children: [
    //                                 Text(
    //                                   LanguageConstants.accessYourAccountDetailsText.tr,
    //                                   style: AppStyle.textStyleUtils400(
    //                                     color: Theme.of(context).textTheme.titleLarge!.color,
    //                                     size: 16.0,
    //                                   ),
    //                                 ),
    //                                 SizedBox(height: 15.iY),
    //                                 Row(
    //                                   mainAxisAlignment: MainAxisAlignment.center,
    //                                   children: [
    //                                     CommonButton(
    //                                       padding: const EdgeInsets.symmetric(horizontal: 5.0),
    //                                       buttonType: ButtonType.outlinedButton,
    //                                       onPressed: () {
    //                                         Get.find<Navigation>()
    //                                             .navigateToNextPage(route: myAccountPageNavigation.signupScreenRoute);
    //                                         // Get.back<dynamic>();
    //                                       },
    //                                       elevation: 0.0,
    //                                       border: const BorderSide(color: darkBlueColor, width: 2),
    //                                       borderRadius: 5.0,
    //                                       child: CommonTextPoppins(
    //                                         LanguageConstants.signUpText.tr,
    //                                         height: 1.iY,
    //                                         fontSize: 14.0,
    //                                         fontWeight: FontWeight.w500,
    //                                         color: Theme.of(context).primaryColor,
    //                                       ),
    //                                     ),
    //                                     SizedBox(width: 10.iX),
    //                                     CommonButton(
    //                                       padding: const EdgeInsets.symmetric(horizontal: 5.0),
    //                                       buttonType: ButtonType.elevatedButton,
    //                                       onPressed: () {
    //                                         Get.find<Navigation>()
    //                                             .navigateToNextPage(route: myAccountPageNavigation.loginScreenRoute);
    //                                         // Get.back<dynamic>();
    //                                       },
    //                                       elevation: 0.0,
    //                                       color: darkBlueColor,
    //                                       borderRadius: 5.0,
    //                                       child: CommonTextPoppins(
    //                                         LanguageConstants.loginText.tr,
    //                                         height: 1.iY,
    //                                         fontSize: 14.0,
    //                                         fontWeight: FontWeight.w500,
    //                                         color: Colors.white,
    //                                       ),
    //                                     ),
    //                                   ],
    //                                 )
    //                               ],
    //                             ),
    //                           ),
    //                         ),
    //                   if (localStore.customerToken.toString() != "") const SizedBox(height: 20),
    //                   if (localStore.customerToken.toString() != "")
    //                     GestureDetector(
    //                       onTap: () {
    //                         Get.find<Navigation>().navigateToNextPage(route: myAccountPageNavigation.myOrdersScreen);
    //                       },
    //                       child: Container(
    //                           padding: const EdgeInsets.all(10.0),
    //                           width: double.infinity,
    //                           child: CommonTextPoppins(
    //                             LanguageConstants.myOrdersText.tr,
    //                             color: Theme.of(context).textTheme.displayMedium?.color ?? blackColor,
    //                             fontSize: 14.iY,
    //                             fontWeight: FontWeight.w500,
    //                             overflow: TextOverflow.ellipsis,
    //                             // fontFamily: fontStyle,
    //                           )),
    //                     ),
    //                   const Divider(color: tileBackgroundColor),
    //                   GestureDetector(
    //                     onTap: () {
    //                       Get.find<Navigation>()
    //                           .navigateToNextPage(route: myAccountPageNavigation.addressBookScreenRoute);
    //                     },
    //                     child: Container(
    //                       padding: const EdgeInsets.all(10.0),
    //                       width: double.infinity,
    //                       child: Text(
    //                         LanguageConstants.addressBookText.tr,
    //                         style: AppStyle.textStyleUtils500(
    //                           color: Theme.of(context).textTheme.displayMedium!.color,
    //                         ),
    //                       ),
    //                     ),
    //                   ),
    //                   const Divider(color: tileBackgroundColor),
    //                   GestureDetector(
    //                     onTap: () {
    //                       Get.find<Navigation>().navigateToNextPage(route: myAccountPageNavigation.myStoreCreditScreenRoute);
    //                     },
    //                     child: Container(
    //                       padding: const EdgeInsets.all(10.0),
    //                       width: double.infinity,
    //                       child: Text(LanguageConstants.storeCreditText.tr,
    //                         style: AppStyle.textStyleUtils500(color: Theme.of(context).textTheme.displayMedium!.color,
    //                         ),
    //                       ),
    //                     ),
    //                   ),
    //                   const Divider(color: tileBackgroundColor),
    //                   GestureDetector(
    //                     onTap: () {
    //                       Get.find<Navigation>()
    //                           .navigateToNextPage(route: myAccountPageNavigation.myCouponsScreenRoute);
    //                     },
    //                     child: Container(
    //                       padding: const EdgeInsets.all(10.0),
    //                       width: double.infinity,
    //                       child: Text(
    //                         LanguageConstants.myCouponsText.tr,
    //                         style: AppStyle.textStyleUtils500(
    //                           color: Theme.of(context).textTheme.displayMedium!.color,
    //                         ),
    //                       ),
    //                     ),
    //                   ),
    //                   const Divider(color: tileBackgroundColor),
    //                   GestureDetector(
    //                     onTap: () {
    //                       Get.find<Navigation>()
    //                           .navigateToNextPage(route: myAccountPageNavigation.myWishListScreenRoute);
    //                     },
    //                     child: Container(
    //                       padding: const EdgeInsets.all(10.0),
    //                       width: double.infinity,
    //                       child: Text(
    //                         LanguageConstants.myWishlistText.tr,
    //                         style: AppStyle.textStyleUtils500(
    //                           color: Theme.of(context).textTheme.displayMedium!.color,
    //                         ),
    //                       ),
    //                     ),
    //                   ),
    //                   const Divider(color: tileBackgroundColor),
    //                   GestureDetector(
    //                     onTap: () {
    //                       Get.find<Navigation>()
    //                           .navigateToNextPage(route: myAccountPageNavigation.accountInformationScreenRoute);
    //                     },
    //                     child: Container(
    //                       padding: const EdgeInsets.all(10.0),
    //                       width: double.infinity,
    //                       child: Text(
    //                         LanguageConstants.accountInformationText.tr,
    //                         style: AppStyle.textStyleUtils500(
    //                           color: Theme.of(context).textTheme.displayMedium!.color,
    //                         ),
    //                       ),
    //                     ),
    //                   ),
    //                   if (localStore.customerToken.toString() != "") const Divider(color: tileBackgroundColor),
    //                   if (localStore.customerToken.toString() != "")
    //                     GestureDetector(
    //                       onTap: () {
    //                         // Get.find<Navigation>()
    //                         //     .navigateToNextPage(
    //                         //     route:
    //                         //     myAccountPageNavigation
    //                         //         .myOrdersScreen);
    //                       },
    //                       child: Container(
    //                         padding: const EdgeInsets.all(10.0),
    //                         width: double.infinity,
    //                         child: Text(
    //                           LanguageConstants.myTicketsText.tr,
    //                           style: AppStyle.textStyleUtils500(
    //                             color: Theme.of(context).textTheme.displayMedium!.color,
    //                           ),
    //                         ),
    //                       ),
    //                     ),
    //                   const Divider(color: tileBackgroundColor),
    //                   Container(
    //                     padding: EdgeInsets.only(left: 20.iX, right: 20.iX),
    //                     width: double.infinity,
    //                     child: GestureDetector(
    //                       onTap: () {
    //                         Get.find<Navigation>().navigateToNextPage(
    //                           route: myAccountPageNavigation.countryRoute,
    //                         );
    //                         // Get.toNamed<dynamic>(myAccountPageNavigation.countryRoute);
    //                       },
    //                       child: Container(
    //                         padding: const EdgeInsets.all(10.0),
    //                         width: double.infinity,
    //                         child: Text(
    //                           controller.countryCurrency.value,
    //                           style:
    //                               AppStyle.textStyleUtils500(color: Theme.of(context).textTheme.displayMedium!.color),
    //                         ),
    //                       ),
    //                     ),
    //                   ),
    //                   Column(
    //                     crossAxisAlignment: CrossAxisAlignment.start,
    //                     children: [
    //                       Padding(
    //                         padding: EdgeInsets.only(left: 24.iX, right: 24.iX),
    //                         child: Column(
    //                           crossAxisAlignment: CrossAxisAlignment.start,
    //                           children: [
    //                             SizedBox(
    //                               width: double.infinity,
    //                               child: Text(
    //                                 LanguageConstants.theme.tr,
    //                                 style: AppStyle.textStyleUtils600_14(
    //                                   color: Theme.of(context).textTheme.titleLarge!.color,
    //                                 ),
    //                               ),
    //                             ),
    //                             SizedBox(
    //                               height: 10.iY,
    //                             ),
    //                             Container(
    //                               padding: const EdgeInsets.all(10.0),
    //                               width: double.infinity,
    //                               child: Row(
    //                                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //                                 children: [
    //                                   Text(
    //                                     LanguageConstants.darkMode.tr,
    //                                     style: AppStyle.textStyleUtils500(
    //                                       color: Theme.of(context).textTheme.displayMedium!.color,
    //                                     ),
    //                                   ),
    //                                   // themeChangeToggle,
    //                                   const ThemeChangeToggle(),
    //                                 ],
    //                               ),
    //                             ),
    //                             SizedBox(
    //                               height: 15.iY,
    //                             ),
    //                             SizedBox(
    //                               width: double.infinity,
    //                               child: Text(
    //                                 LanguageConstants.companyText.tr,
    //                                 style: AppStyle.textStyleUtils600_14(
    //                                   color: Theme.of(context).textTheme.titleLarge!.color,
    //                                 ),
    //                               ),
    //                             ),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.find<Navigation>()
    //                                     .navigateToNextPage(route: myAccountPageNavigation.returnsAndRefundScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.returnsText.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.find<Navigation>()
    //                                     .navigateToNextPage(route: myAccountPageNavigation.referFriendScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.referFriendMyAccountText.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.find<Navigation>()
    //                                     .navigateToNextPage(route: myAccountPageNavigation.shippingScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.shippingText.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             InkWell(
    //                               onTap: () {
    //                                 Get.find<Navigation>()
    //                                     .navigateToNextPage(route: myAccountPageNavigation.notificationScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.notifications.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             SizedBox(
    //                               height: 15.iY,
    //                             ),
    //
    //                             SizedBox(
    //                               width: double.infinity,
    //                               child: Text(
    //                                 LanguageConstants.socialMyAccountText.tr,
    //                                 style: AppStyle.textStyleUtils600_14(
    //                                   color: Theme.of(context).textTheme.titleLarge!.color,
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.find<Navigation>()
    //                                     .navigateToNextPage(route: myAccountPageNavigation.myWishListScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.myWishlistText.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.find<Navigation>()
    //                                     .navigateToNextPage(route: myAccountPageNavigation.affiliateProgramScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.affiliateProgramText.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.find<Navigation>().navigateToNextPage(
    //                                     route: myAccountPageNavigation.influenceRegistrationScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.influencerRegistrationText.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //
    //                             const Divider(color: tileBackgroundColor),
    //                             SizedBox(
    //                               width: double.infinity,
    //                               child: Text(
    //                                 LanguageConstants.contactMyAccountText.tr,
    //                                 style: AppStyle.textStyleUtils600_14(
    //                                   color: Theme.of(context).textTheme.titleLarge!.color,
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             InkWell(
    //                               onTap: () {
    //                                 Get.find<Navigation>()
    //                                     .navigateToNextPage(route: myAccountPageNavigation.trackOrderScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.trackOrderText.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.find<Navigation>().navigateToNextPage(
    //                                     route: myAccountPageNavigation.trackYourTicketByEmailScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.trackYourTicketByEmail.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.find<Navigation>().navigateToNextPage(
    //                                     route: myAccountPageNavigation.trackYourOrderByGuestScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.trackOrderGuest.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                       color: Theme.of(context).textTheme.displayMedium!.color),
    //                                 ),
    //                               ),
    //                             ),
    //
    //                             const Divider(color: tileBackgroundColor),
    //                             SizedBox(
    //                               width: double.infinity,
    //                               child: Text(
    //                                 LanguageConstants.aboutText.tr,
    //                                 style: AppStyle.textStyleUtils600_14(
    //                                   color: Theme.of(context).textTheme.titleLarge!.color,
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.to<dynamic>(() =>
    //                                 CommonAboutUsScreen(
    //                                       aboutUsDataList: [
    //                                         LanguageConstants.aboutUsContain1solo.tr,
    //                                         LanguageConstants.aboutUsContain2solo.tr,
    //                                         LanguageConstants.aboutUsContain3solo.tr,
    //                                         LanguageConstants.aboutUsContain4solo.tr,
    //                                         LanguageConstants.aboutUsContain5solo.tr,
    //                                         LanguageConstants.aboutUsContain6solo.tr,
    //                                         LanguageConstants.aboutUsContain7solo.tr,
    //                                       ],
    //                                       appStyle: AppTextStyle.textStyleUtils400(
    //                                           size: 15.iY, color: Theme.of(context).textTheme.displayMedium!.color),
    //                                       commonAppbar: commonAppbar(
    //                                          leadingWidget: IconButton(
    //                                           onPressed: () {
    //                                             Get.back();
    //                                           },
    //                                           icon: const Icon(
    //                                             Icons.arrow_back,
    //                                             color: blackColor,
    //                                           )),
    //                                           titleWidget: CommonTextPoppins(
    //                                         LanguageConstants.aboutUsText.tr,
    //                                         fontSize: 18.iY,
    //                                         color: Theme.of(context).textTheme.displayMedium?.color ?? blackColor,
    //                                       )),
    //                                     ));
    //                                 // Get.find<Navigation>().navigateToNextPage(
    //                                 //     route: myAccountPageNavigation
    //                                 //         .aboutUsScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.aboutUsMyAccountText.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.find<Navigation>().navigateToNextPage(
    //                                     route: myAccountPageNavigation.termsAndConditionsScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.termsConditionsText.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 if (localStore.customerToken.toString() == "") {
    //                                   showDialog<dynamic>(
    //                                       context: context,
    //                                       builder: (BuildContext context) {
    //                                         return showTitleDialog;
    //                                       });
    //                                 } else {
    //                                   // controller.clickChatEvent(context: context);
    //                                 }
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.liveChatText.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.find<Navigation>()
    //                                     .navigateToNextPage(route: myAccountPageNavigation.privacyPolicyScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.privacyPolicyText.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.find<Navigation>()
    //                                     .navigateToNextPage(route: myAccountPageNavigation.contactUsScreenPath);
    //                               },
    //                               child: Container(
    //                                 padding: const EdgeInsets.all(10.0),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.contactUsText.tr,
    //                                   style: AppStyle.textStyleUtils500(
    //                                     color: Theme.of(context).textTheme.displayMedium!.color,
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                             const Divider(color: tileBackgroundColor),
    //                             GestureDetector(
    //                               onTap: () {
    //                                 Get.find<Navigation>().navigateToNextPage(route: myAccountPageNavigation.faqScreenRoute);
    //                               },
    //                               child: Container(
    //                                 padding: EdgeInsets.only(left: 24.iX, top: 10.iY),
    //                                 width: double.infinity,
    //                                 child: Text(
    //                                   LanguageConstants.faqMyAccountText.tr,
    //                                   style: AppStyle.textStyleUtils500(color: Theme.of(context).textTheme.displayMedium!.color, ),
    //                                 ),
    //                               ),
    //                             ),
    //                             //       if (localStore.customerToken.toString() != "")
    //                             //         const SizedBox(height: 20),
    //                             //       if (localStore.customerToken.toString() != "")
    //                             //         GestureDetector(
    //                             //           onTap: () {
    //                             //             Get.find<Navigation>().navigateToNextPage(
    //                             //                 route: myAccountPageNavigation
    //                             //                     .myOrdersScreen);
    //                             //           },
    //                             //           child: Container(
    //                             //               padding: const EdgeInsets.all(10.0),
    //                             //               width: double.infinity,
    //                             //               child: CommonTextPoppins(
    //                             //                 LanguageConstants.myOrdersText.tr,
    //                             //                 color: Theme.of(context)
    //                             //                         .textTheme
    //                             //                         .titleLarge
    //                             //                         ?.color ??
    //                             //                     blackColor,
    //                             //                 fontSize: 14.iY,
    //                             //                 fontWeight: FontWeight.w500,
    //                             //                 overflow: TextOverflow.ellipsis,
    //                             //                 // fontFamily: fontStyle,
    //                             //               )),
    //                             //         ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .myWishListScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.myWishlistText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .addressBookScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.addressBookText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .accountInformationScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants
    //                             //                 .accountInformationText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       if (localStore.customerToken.toString() != "")
    //                             //         const Divider(color: tileBackgroundColor),
    //                             //       if (localStore.customerToken.toString() != "")
    //                             //         GestureDetector(
    //                             //           onTap: () {
    //                             //             // Get.find<Navigation>()
    //                             //             //     .navigateToNextPage(
    //                             //             //     route:
    //                             //             //     myAccountPageNavigation
    //                             //             //         .myOrdersScreen);
    //                             //           },
    //                             //           child: Container(
    //                             //             padding: const EdgeInsets.all(10.0),
    //                             //             width: double.infinity,
    //                             //             child: Text(
    //                             //               LanguageConstants.myTicketsText.tr,
    //                             //               style: AppStyle.textStyleUtils500(
    //                             //                 color: Theme.of(context)
    //                             //                     .textTheme
    //                             //                     .titleLarge!
    //                             //                     .color,
    //                             //               ),
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .myStoreCreditScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.storeCreditText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .myCouponsScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.myCouponsText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       InkWell(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .trackOrderScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.trackOrderText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       InkWell(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .notificationScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.notifications.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.toNamed<dynamic>(
    //                             //               myAccountPageNavigation.countryRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             controller.countryCurrency.value,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //     ],
    //                             //   ),
    //                             // ),
    //                             // // : Container(),
    //                             // SizedBox(
    //                             //   height: 10.iY,
    //                             // ),
    //                             // Padding(
    //                             //   padding: EdgeInsets.only(left: 24.iX, right: 24.iX),
    //                             //   child: Column(
    //                             //     crossAxisAlignment: CrossAxisAlignment.start,
    //                             //     children: [
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .contactUsScreenPath);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.contactUsText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .secureShoppingScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.secureShoppingText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .trackYourOrderByGuestScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.trackOrderGuest.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //                 color: Theme.of(context)
    //                             //                     .textTheme
    //                             //                     .titleLarge!
    //                             //                     .color),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //     ],
    //                             //   ),
    //                             // ),
    //                             // SizedBox(
    //                             //   height: 10.iY,
    //                             // ),
    //                             // Container(
    //                             //   color: appDivderGrey,
    //                             //   width: double.infinity,
    //                             //   child: Padding(
    //                             //     padding: EdgeInsets.only(
    //                             //         top: 15.iY, bottom: 15.iY, left: 25.iX),
    //                             //     child: Text(
    //                             //       LanguageConstants.socialMyAccountText.tr,
    //                             //       style: AppStyle.textStyleUtils600_14(
    //                             //         color: Theme.of(context)
    //                             //             .textTheme
    //                             //             .titleLarge!
    //                             //             .color,
    //                             //       ),
    //                             //     ),
    //                             //   ),
    //                             // ),
    //                             // SizedBox(
    //                             //   height: 10.iY,
    //                             // ),
    //                             // Padding(
    //                             //   padding: EdgeInsets.only(
    //                             //     left: 24.iX,
    //                             //     right: 24.iX,
    //                             //   ),
    //                             //   child: Column(
    //                             //     crossAxisAlignment: CrossAxisAlignment.start,
    //                             //     children: [
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .hopeScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.hopeText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .affiliateProgramScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.affiliateProgramText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .influenceRegistrationScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants
    //                             //                 .influencerRegistrationText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {},
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.exchangeText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //     ],
    //                             //   ),
    //                             // ),
    //                             // SizedBox(
    //                             //   height: 10.iY,
    //                             // ),
    //                             // Container(
    //                             //   width: double.infinity,
    //                             //   color: appDivderGrey,
    //                             //   child: Padding(
    //                             //     padding: EdgeInsets.only(
    //                             //         top: 15.iY, bottom: 15.iY, left: 25.iX),
    //                             //     child: Text(
    //                             //       LanguageConstants.contactMyAccountText.tr,
    //                             //       style: AppStyle.textStyleUtils600_14(
    //                             //         color: Theme.of(context)
    //                             //             .textTheme
    //                             //             .titleLarge!
    //                             //             .color,
    //                             //       ),
    //                             //     ),
    //                             //   ),
    //                             // ),
    //                             // SizedBox(
    //                             //   height: 10.iY,
    //                             // ),
    //                             // Padding(
    //                             //   padding: EdgeInsets.only(
    //                             //     left: 24.iX,
    //                             //     right: 24.iX,
    //                             //   ),
    //                             //   child: Column(
    //                             //     crossAxisAlignment: CrossAxisAlignment.start,
    //                             //     children: [
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .privacyPolicyScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.privacyPolicyText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .termsAndConditionsScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.termsConditionsText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .shippingScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.shippingText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .trackYourTicketByEmailScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants
    //                             //                 .trackYourTicketByEmail.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       localStore.customerToken.toString() != ""
    //                             //           ? GestureDetector(
    //                             //               onTap: () {
    //                             //                 Get.find<Navigation>()
    //                             //                     .navigateToNextPage(
    //                             //                         route: myAccountPageNavigation
    //                             //                             .trackOrderScreenRoute);
    //                             //               },
    //                             //               child: Container(
    //                             //                 padding: const EdgeInsets.all(10.0),
    //                             //                 width: double.infinity,
    //                             //                 child: Text(
    //                             //                   LanguageConstants.trackYourOrder.tr,
    //                             //                   style: AppStyle.textStyleUtils500(
    //                             //                     color: Theme.of(context)
    //                             //                         .textTheme
    //                             //                         .titleLarge!
    //                             //                         .color,
    //                             //                   ),
    //                             //                 ),
    //                             //               ),
    //                             //             )
    //                             //           : const SizedBox(),
    //                             //     ],
    //                             //   ),
    //                             // ),
    //                             // SizedBox(
    //                             //   height: 10.iY,
    //                             // ),
    //                             // Container(
    //                             //   width: double.infinity,
    //                             //   color: appDivderGrey,
    //                             //   child: Padding(
    //                             //     padding: EdgeInsets.only(
    //                             //         top: 15.iY, bottom: 15.iY, left: 25.iX),
    //                             //     child: Text(
    //                             //       LanguageConstants.aboutText.tr,
    //                             //       style: AppStyle.textStyleUtils600_14(
    //                             //         color: Theme.of(context)
    //                             //             .textTheme
    //                             //             .titleLarge!
    //                             //             .color,
    //                             //       ),
    //                             //     ),
    //                             //   ),
    //                             // ),
    //                             // SizedBox(
    //                             //   height: 10.iY,
    //                             // ),
    //                             // Padding(
    //                             //   padding: EdgeInsets.only(
    //                             //     left: 24.iX,
    //                             //     right: 24.iX,
    //                             //   ),
    //                             //   child: Column(
    //                             //     crossAxisAlignment: CrossAxisAlignment.start,
    //                             //     children: [
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.to<dynamic>(() => CommonAboutUsScreen(
    //                             //                 aboutUsDataList: [
    //                             //                   LanguageConstants
    //                             //                       .aboutUsContain1solo.tr,
    //                             //                   LanguageConstants
    //                             //                       .aboutUsContain2solo.tr,
    //                             //                   LanguageConstants
    //                             //                       .aboutUsContain3solo.tr,
    //                             //                   LanguageConstants
    //                             //                       .aboutUsContain4solo.tr,
    //                             //                   LanguageConstants
    //                             //                       .aboutUsContain5solo.tr,
    //                             //                   LanguageConstants
    //                             //                       .aboutUsContain6solo.tr,
    //                             //                   LanguageConstants
    //                             //                       .aboutUsContain7solo.tr,
    //                             //                 ],
    //                             //                 appStyle:
    //                             //                     AppTextStyle.textStyleUtils400(
    //                             //                         size: 15.iY,
    //                             //                         color: Theme.of(context)
    //                             //                             .textTheme
    //                             //                             .displayMedium!
    //                             //                             .color),
    //                             //                 commonAppbar: commonAppbar(
    //                             //                     titleWidget: CommonTextPoppins(
    //                             //                   LanguageConstants.aboutUsText.tr,
    //                             //                   fontSize: 18.iY,
    //                             //                   color: Theme.of(context)
    //                             //                           .textTheme
    //                             //                           .displayMedium
    //                             //                           ?.color ??
    //                             //                       blackColor,
    //                             //                 )),
    //                             //               ));
    //                             //           // Get.find<Navigation>().navigateToNextPage(
    //                             //           //     route: myAccountPageNavigation
    //                             //           //         .aboutUsScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.aboutUsMyAccountText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .referFriendScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants
    //                             //                 .referFriendMyAccountText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .returnsAndRefundScreenRoute);
    //                             //           // Get.toNamed<dynamic>(RoutesConstants.guestReturnsScreen);
    //                             //           // Get.toNamed<dynamic>(
    //                             //           //   RoutesConstants.returnsAndRefundsScreen,
    //                             //           // );
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.returnsText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       Container(
    //                             //         padding: const EdgeInsets.all(10.0),
    //                             //         width: double.infinity,
    //                             //         child: Row(
    //                             //           mainAxisAlignment:
    //                             //               MainAxisAlignment.spaceBetween,
    //                             //           children: [
    //                             //             Text(
    //                             //               LanguageConstants.darkMode.tr,
    //                             //               style: AppStyle.textStyleUtils500(
    //                             //                 color: Theme.of(context)
    //                             //                     .textTheme
    //                             //                     .titleLarge!
    //                             //                     .color,
    //                             //               ),
    //                             //             ),
    //                             //             themeChangeToggle,
    //                             //           ],
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           Get.find<Navigation>().navigateToNextPage(
    //                             //               route: myAccountPageNavigation
    //                             //                   .faqScreenRoute);
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.faqMyAccountText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       const Divider(color: tileBackgroundColor),
    //                             //       GestureDetector(
    //                             //         onTap: () {
    //                             //           if (localStore.customerToken.toString() ==
    //                             //               "") {
    //                             //             showDialog<dynamic>(
    //                             //                 context: context,
    //                             //                 builder: (BuildContext context) {
    //                             //                   return showTitleDialog;
    //                             //                 });
    //                             //           } else {
    //                             //             // controller.clickChatEvent(context: context);
    //                             //           }
    //                             //         },
    //                             //         child: Container(
    //                             //           padding: const EdgeInsets.all(10.0),
    //                             //           width: double.infinity,
    //                             //           child: Text(
    //                             //             LanguageConstants.liveChatText.tr,
    //                             //             style: AppStyle.textStyleUtils500(
    //                             //               color: Theme.of(context)
    //                             //                   .textTheme
    //                             //                   .titleLarge!
    //                             //                   .color,
    //                             //             ),
    //                             //           ),
    //                             //         ),
    //                             //       ),
    //                             //       localStore.customerToken.toString() == ""
    //                             //           ? Container()
    //                             //           : const Divider(color: tileBackgroundColor),
    //                             //       localStore.customerToken.toString() == ""
    //                             //           ? Container()
    //                             //           : GestureDetector(
    //                             //               onTap: () async {
    //                             //                 // await showConfirmationDialog(context);
    //                             //               },
    //                             //               child: Container(
    //                             //                 padding: const EdgeInsets.all(10.0),
    //                             //                 width: double.infinity,
    //                             //                 child: Text(
    //                             //                   LanguageConstants.logOutText.tr,
    //                             //                   style: AppStyle.textStyleUtils500(
    //                             //                       color: Theme.of(context)
    //                             //                           .textTheme
    //                             //                           .titleLarge!
    //                             //                           .color),
    //                             //                 ),
    //                             //               ),
    //                             //             ),
    //                           ],
    //                         ),
    //                       ),
    //                       SizedBox(
    //                         height: 10.iY,
    //                       ),
    //                     ],
    //                   )
    //                 ],
    //               ),
    //             ),
    //     ));
  }
}
