import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:presentation/const/app_colors/app_one_colors.dart';
import 'package:presentation/theme/app_one_dark_light_theme.dart';
import 'package:presentation/theme/notifier/theme_notifier.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeChangeToggle extends StatelessWidget {
  // ThemeChangeToggle widget
  const ThemeChangeToggle({Key? key})
      : super(key: key); // Constructor for ThemeChangeToggle widget

  @override
  Widget build(BuildContext context) {
    // Build method for ThemeChangeToggle widget
    final themeNotifier = Provider.of<ThemeNotifier>(
        context); // Accessing theme notifier from provider
    return Consumer<ThemeNotifier>(builder: (context, theme, _) {
      return Switch(
        // Switch widget for toggling theme
        activeTrackColor: greyColor,
        activeColor: appColor,
        inactiveTrackColor: greyColor,
        inactiveThumbColor: appColor,
        value: theme.getTheme() == darkTheme,
        onChanged: (value) {
          // Callback function for switch value change
          onThemeChanged(value, themeNotifier);
        },
      );
    });
  }

  // Function to handle theme change
  void onThemeChanged(bool value, ThemeNotifier themeNotifier) async {
    (value)
        ? themeNotifier.setTheme(darkTheme)
        : themeNotifier.setTheme(lightTheme);
    var prefs = await SharedPreferences
        .getInstance(); // Get shared preferences instance
    // Store theme preference
    prefs.setBool('darkMode', value);
  }
}
