import 'dart:async';

import 'package:flutter_svg/svg.dart';
import 'package:presentation/const/size_utils.dart';
import 'package:presentation/const/app_text_style/presentation_app_text_style.dart';
import 'package:app_one/all_imports.dart';
import 'package:base_controller/controllers/brand_list_screen_controller.dart';
// import 'package:app_one/theme/app_asset.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';

class SearchFieldWidget extends GetView<BrandListScreenController> {
  const SearchFieldWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50.iY,
      width: 327.iX,
      child: TextFormField(
        controller: controller.searchController,
        cursorColor: appColor,
        decoration: InputDecoration(
          prefixIcon: SizedBox(
              height: 20.iY,
              width: 20.iX,
              child: SvgPicture.asset(
                AppAsset.search,
                fit: BoxFit.scaleDown,
              )),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.only(
            bottom: 12,
            top: 12,
            left: 12,
          ),
          hintText: LanguageConstants.findBrandsText.tr,
          hintStyle: AppTextStyle.textStyleUtils500(color: appColor.withOpacity(0.4)),
          errorStyle: AppTextStyle.textStyleError(),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(
              10.0,
            ),
            borderSide: const BorderSide(
              color: tileBackgroundColor,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(
              10.0,
            ),
            borderSide: const BorderSide(
              color: tileBackgroundColor,
              width: 1.0,
            ),
          ),
          isDense: true,
          border: OutlineInputBorder(
              borderSide: const BorderSide(
                color: tileBackgroundColor,
              ),
              borderRadius: BorderRadius.circular(10.0)),
        ),
        onChanged: (value) {
          if (value.isEmpty) {
            Timer(const Duration(milliseconds: 500), () {
              controller.filterSearchAllList.value = [];
              controller.isSearchEnable.value = false;
              controller.update();
              controller.getBrandList();
            });
          } else {
            controller.setSerchwithAlphabatic(
              value.toUpperCase(),
            );
          }
        },
      ),
    );
  }
}
