import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:meta_package/translations/translations.dart';
import 'package:presentation/const/app_colors/app_one_colors.dart';
import 'package:presentation/const/app_colors/presentation_colors.dart';
import 'package:presentation/const/app_colors/app_one_colors.dart';
import 'package:presentation/const/app_text_style/app_style.dart';
import 'package:presentation/const/size_utils.dart';
import 'package:presentation/widgets/common_animation/nothing_to_show_animation.dart';
import 'package:presentation/widgets/common_buttons/common_theme_button.dart';
import 'package:base_controller/controllers/brand_list_screen_controller.dart';
import 'package:presentation_common_ui/widgets/brand_list_screen/app_one/expand_detail.dart';
import 'package:presentation_common_ui/widgets/brand_list_screen/app_one/search_field_widget.dart';
// import 'package:app_one/theme/app_asset.dart';
import 'package:presentation/widgets/screen_loading/screen_loading.dart';
import 'package:presentation_common_ui/theme/app_one/app_asset.dart';

class BrandListScreen extends GetView<BrandListScreenController> {
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        key: controller.scaffoldKeyBrandListScreen,
        appBar: _buildAppBar(context),
        body: _buildBody(context),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      elevation: 0,
      iconTheme: IconThemeData(color: Theme.of(context).textTheme.displayMedium?.color),
      centerTitle: true,
      title: Image.asset(
        AppAsset.logo,
        width: 110,
        fit: BoxFit.fill,
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 20.iY),
        const SearchFieldWidget(),
        Expanded(
          child: controller.isLoading.value
              ? const Center(
                  child: ScreenLoading(
                    appColor: darkBlueColor,
                  ),
                )
              : (controller.isSearchEnable.value == true
                      ? controller.filterSearchAllList.isNotEmpty
                      : controller.filterSearchAllList.isNotEmpty)
                  ? Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.iX),
                      child: Theme(
                        data: ThemeData.light().copyWith(
                            scrollbarTheme: const ScrollbarThemeData().copyWith(
                          thickness: MaterialStateProperty.all(1),
                          trackBorderColor: MaterialStateProperty.all(Colors.transparent),
                          thumbColor: MaterialStateProperty.all(const Color(0xffCFCFCF)),
                        )),
                        child: _buildList(context),
                      ),
                    )
                  : _nothingToShow(context),
        ),
      ],
    );
  }

  Widget _buildList(BuildContext context) {
    return Scrollbar(
      controller: controller.scrollController,
      thumbVisibility: true,
      trackVisibility: true,
      interactive: true,
      thickness: 5.iX,
      radius: const Radius.circular(5),
      child: ListView.builder(
        controller: controller.scrollController,
        physics: const BouncingScrollPhysics(),
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(
          horizontal: 10,
        ),
        itemCount: controller.filterSearchAllList.isNotEmpty
            ? controller.filterSearchAllList.length
            : controller.filterSearchAllList.length,
        itemBuilder: (BuildContext context, int index) {
          final brandsListCapital = controller.filterSearchAllList.isNotEmpty
              ? controller.filterSearchAllList[index]
              : controller.filterSearchAllList[index];
          return ListTileTheme(
            contentPadding: EdgeInsets.zero,
            minVerticalPadding: 10,
            dense: true,
            horizontalTitleGap: 1.2,
            minLeadingWidth: 10,
            child: ExpandDetail(
              text: brandsListCapital.toString().toUpperCase(),
              value: index + 1,
              controller: controller,
            ),
          );
        },
      ),
    );
  }

  Widget _nothingToShow(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          NothingToShowAnimationWidget(
            nothingToShowAnimationPath: AppAsset.nothigToShowAnimation,
          ),
          Container(
            margin: const EdgeInsets.only(
              left: 20,
              right: 20,
            ),
            child: RichText(
              textAlign: TextAlign.center,
              softWrap: true,
              textScaleFactor: 1,
              text: TextSpan(
                text: LanguageConstants.itSeemsWeHaveNothingToShowFor.tr,
                style: AppStyle.textStyleUtils700(
                        size: 10.iY,
                      ),
                children: <TextSpan>[
                  TextSpan(
                    text: ' ',
                    style: AppStyle.textStyleUtils700(
                        size: 10.iY,
                      ),
                  ),
                  TextSpan(
                    text: controller.searchController.text,
                    style:AppStyle.textStyleUtils700(
                        size: 10.iY,
                      ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              top: 0.iY,
            ),
            child: RichText(
              textAlign: TextAlign.center,
              softWrap: true,
              textScaleFactor: 1,
              text: TextSpan(
                text: LanguageConstants.ifYouWouldLikeToHaveMoreInformationAbout.tr,
                style: AppStyle.textStyleUtils700(
                        size: 10.iY,
                        color: Theme.of(context).textTheme.displayMedium?.color,
                      ),
                children: <TextSpan>[
                  TextSpan(
                    text: ' ',
                    style: AppStyle.textStyleUtils700(
                        size: 10.iY,
                      ),
                  ),
                  TextSpan(
                    text: controller.searchController.text,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
              left: 20.iX,
              right: 20.iX,
            ),
            child: Text(
              LanguageConstants.thenPleaseCreateTicket.tr,
              style: AppStyle.textStyleUtils700(
                        size: 10.iY,
                      ),
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: 10,
            ),
            child: Column(
              children: [
                CommonThemeButton(
                  onTap: () {
                    // Get.find<SpecialRequestController>().brandName = Get.arguments[2];
                    // Get.offAllNamed<dynamic>(RoutesConstants.dashboardScreen);
                    // Get.toNamed<dynamic>(
                    //   RoutesConstants.specialRequesScreen,
                    //   arguments: [
                    //     controller.searchController.text,
                    //     "brand",
                    //   ],
                    // );
                  },
                  title: LanguageConstants.createTicket.tr,
                  buttonColor: darkBlueColor,
                  textColor: Colors.white,
                ),
                const SizedBox(
                  height: 2,
                ),
                CommonThemeButton(
                  onTap: () {
                    controller.continueShoppingTap();
                  },
                  title: LanguageConstants.continueShopping.tr,
                  buttonColor: darkBlueColor,
                  textColor: Colors.white,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
