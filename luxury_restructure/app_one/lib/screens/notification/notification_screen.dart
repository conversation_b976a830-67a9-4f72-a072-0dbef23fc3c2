import 'package:presentation_common_ui/constants/app_constants.dart';
import '../../all_imports.dart';
import 'package:base_controller/controllers/notification_controller.dart';
import 'package:presentation_common_ui/common_screens/common_notification_screen/common_notification_screen.dart';

class NotificationScreen extends GetView<NotificationController> {
  const NotificationScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const CommonNotificationScreen(
      projectName: APP_ONE_NAME,
    );
    // return Scaffold(
    //   appBar: commonAppbar(
    //     backgroundColor: Theme.of(context).scaffoldBackgroundColor,
    //     titleWidget: CommonTextPoppins(
    //       LanguageConstants.notifications.tr,
    //       height: 2.iY,
    //       fontWeight: FontWeight.w700,
    //       color: Theme.of(context).textTheme.displayMedium?.color,
    //     ),
    //   ),
    //   body: controller.obx(
    //     (state) => (ListView.separated(
    //       itemBuilder: (_, i) => NotificationTile(
    //         message: state!.notification[i],
    //       ),
    //       separatorBuilder: (_, i) => const Divider(
    //         height: 0,
    //       ),
    //       itemCount: state!.notification.length,
    //       shrinkWrap: true,
    //     )),
    //     onLoading: const Center(child: CircularProgressIndicator()),
    //     onEmpty: Center(
    //       child: Text(LanguageConstants.noDataFound.tr,
    //           style: AppStyle.commonTextStyle20600(
    //             color: Theme.of(context).textTheme.displayMedium!.color,
    //           )),
    //     ),
    //     onError: (e) => const SizedBox.shrink(),
    //   ),
    // );
  }
}
