import 'package:base_controller/controllers/returns_and_refunds_controller.dart';
import 'package:base_controller/utils/lang_directory/language_constant.dart';
import 'package:app_one/all_imports.dart';
import 'package:app_one/screens/brand_list_detail_screen/brand_list_detail_screen.dart';
//import 'package:presentation_common_ui/common_ui/common_return_and_refund/common_return_and_refunds_page.dart';

import '../../app_constants/app_constants.dart';
import 'package:presentation_common_ui/common_screens/common_return_refund_screen/common_return_and_refund_screen.dart';

import '../../core/consts/app_constants.dart';

class ReturnAndRefundScreen extends GetView<ReturnsAndRefundsController> {
  const ReturnAndRefundScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return /*Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: CommonReturnAndRefundScreen(
          firstExpTitle: LanguageConstants.returnPolicyTitle.tr,
          secondExpTitle: LanguageConstants.returnProcessTitle.tr,
          thirdExpTitle: LanguageConstants.refundsPolicyTitle.tr,
          fourthExpTitle: LanguageConstants.customisedTitleveralusso.tr,
          fifthExpTitle:LanguageConstants.howToReturnTitle.tr.toTitleCase(),
          titleStyle: AppStyle.textStyleUtils500(size: 24, color: Theme.of(context).colorScheme.primary),
          expTitleStyle: AppStyle.textStyleUtils600_16(color: Theme.of(context).colorScheme.primary),
          titleHeaderColor:Colors.transparent,
          borderColor: Theme.of(context).dividerColor,
          expDropdownIconColor: Theme.of(context).primaryColorDark,
          expBorderRadius: 16.0,
          themeAppCplor:darkBlueColor,
          noDataExpChild: Column(
            children: [
              Text('NO DATA FOUND !',
                  style: AppStyle.textStyleUtils400(size:16,color: Theme.of(context).textTheme.titleLarge?.color?.withOpacity(0.5)))],
          ),
        ));*/
        CommonReturnAndRefundScreen(
      projectName: APP_NAME,
      apiEndPoint: AppConstants.apiEndPointLogin,
      titleHeaderColor: Colors.transparent,
    );
  }
}
