import 'package:app_one/app_constants/app_constants.dart';
import 'package:app_one/app_pages/app_pages.dart';
import 'package:app_one/core/utils/lang_directory/translation_service.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:presentation/const/size_utils.dart';
import 'package:presentation/theme/notifier/theme_notifier.dart';

import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class BrandsLabelsApp extends StatefulWidget {
  const BrandsLabelsApp({Key? key}) : super(key: key);

  @override
  BrandsLabelsAppState createState() => BrandsLabelsAppState();
}

final GlobalKey<NavigatorState> navigatorKey =
    GlobalKey<NavigatorState>(debugLabel: "navigator");

class BrandsLabelsAppState extends State<BrandsLabelsApp> {
  @override
  Widget build(BuildContext context) {
    final themeNotifier = Provider.of<ThemeNotifier>(context);
    return GetMaterialApp(
      navigatorKey: navigatorKey,
      title: APP_NAME,
      debugShowCheckedModeBanner: false,
      theme: themeNotifier.getTheme(),
      getPages: AppPages.routes,
      navigatorObservers: [
        SentryNavigatorObserver(),
      ],
      locale: TranslationService.locale,
      builder: (context, child) {
        //initSizeUtils();
        initSizeUtils(size: MediaQuery.of(context).size);
        return child!;
      },
      fallbackLocale: TranslationService.fallbackLocale,
      translations: TranslationService(),
    );
  }

  @override
  void dispose() {
    stopSentry();
    super.dispose();
  }

  /// close sentry handling
  void stopSentry() async {
    await Sentry.close();
  }
}
