import 'dart:async';
import 'package:app_one/app.dart';
import 'package:app_one/core/config/env_configs.dart';
import 'package:app_one/core/consts/app_constants.dart';

import 'package:app_one/helper/deep_linking_service.dart';
import 'package:app_one/helper/notification_service.dart';
import 'package:app_one/notifier/theme_notifier.dart';
import 'package:app_one/theme/app_dark_light_theme.dart';
// import 'package:firebase_analytics/firebase_analytics.dart';
// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_crashlytics/firebase_crashlytics.dart';
// import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get_storage/get_storage.dart';
import 'package:meta_package/api/services/local_store_service.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:presentation/theme/app_one_dark_light_theme.dart';
import 'package:presentation/theme/notifier/theme_notifier.dart';
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';
import 'package:presentation_common_ui/theme/project_theme_config.dart';
import 'package:presentation_common_ui/theme/app_one/app_one_theme_config.dart';
import 'package:presentation_common_ui/theme/theme_config_manager.dart';

String? deviceModel;
LocalStore localStore = LocalStore();
const platform = MethodChannel('flutter.native/helper');

Future<void> mainCommon() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: "env");
  EnvSecretConfigs();

  await SentryFlutter.init(
    (options) {
      options.dsn = EnvSecretConfigs.instance.sentryDSNToken;
      options.tracesSampleRate = 1.0; // needed for Dio `networkTracing` feature
      options.sendDefaultPii = true;
    },
    appRunner: () async {
      // initMessaging();
      // backgroundNotification();
      await GetStorage.init();
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]).then((_) async {
        await runZonedGuarded(() async {
          // await Firebase.initializeApp();
          WidgetsFlutterBinding.ensureInitialized();
          // FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
          // deviceInfo = Platform.isAndroid
          //     ? await DeviceInfoPlugin().androidInfo
          //     : await DeviceInfoPlugin().iosInfo;
          // DynamicLinkingService.registerDynamicLinkHandler();
          // await FirebaseRemoteConfig.instance.setConfigSettings(RemoteConfigSettings(
          //   fetchTimeout: const Duration(minutes: 1),
          //   minimumFetchInterval: const Duration(minutes: 1),
          // ));
          packageInfo = await PackageInfo.fromPlatform();
          if (!kDebugMode) {
            // record exception when app is in release mode
            // FlutterError.onError =
            //     FirebaseCrashlytics.instance.recordFlutterError;
            // FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
          }
          SharedPreferences.getInstance().then((prefs) {
            // var darkModeOn = prefs.getBool('darkMode') ?? true;
            var darkModeOn = prefs.getBool('darkMode') ?? false;
            Get.put<ThemeConfigManager>(ThemeConfigManager());
            Get.find<ThemeConfigManager>().setConfig(appOneThemeConfig);
            runApp(
              ChangeNotifierProvider<ThemeNotifier>(
                create: (_) =>
                    ThemeNotifier(darkModeOn ? darkTheme : lightTheme),
                child: DefaultAssetBundle(
                  bundle: SentryAssetBundle(),
                  child: const BrandsLabelsApp(),
                ),
              ),
              // DefaultAssetBundle(
              //   bundle: SentryAssetBundle(),
              //   child: const BrandsLabelsApp(),
              // ),
            );
          });
        }, (exception, stackTrace) async {
          if (!kDebugMode) {
            // record exception when app is in release mode
            await Sentry.captureException(exception, stackTrace: stackTrace);
          }
        });
      });
    },
  );
}
