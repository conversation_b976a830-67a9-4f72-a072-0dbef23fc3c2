import 'package:flutter/material.dart';
import 'package:meta_package/api/api_client/api_exception.dart';
import 'package:meta_package/utils/validator.dart';
import 'package:presentation/widgets/error_widgets/error_data_widget.dart';

class ExceptionHandler {
  static void apiExceptionError(
      {required ApiException e,
      ValueChanged<String>? callBack,
      bool? showToast,
      bool? showAlert}) {
    Validators.apiExceptionError(
        e: e,
        showAlert: showAlert,
        showToast: showToast,
        callBack: callBack,
        errorAlert: (String value) {
          return ErrorDataWidget(
            error: value,
          );
        });
  }

  static void appCatchError({required dynamic error}) {
    Validators.appCatchError(
        error: error,
        errorAlert: (String value) {
          return ErrorDataWidget(
            error: value,
          );
        });
  }
}
