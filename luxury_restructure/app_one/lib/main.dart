// import 'package:app_one/app_pages/app_pages.dart';
// import 'package:app_one/core/config/flavor_config.dart';
// import 'package:app_one/core/consts/app_constants.dart';
// import 'package:app_one/core/utils/lang_directory/translation_service.dart';
// import 'package:app_one/screens/dashboard_screen/dashboard_screen.dart';
// import 'package:firebase_core/firebase_core.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:presentation/const/size_utils.dart';
// import 'package:presentation/theme/app_one_dark_light_theme.dart';
// import 'binding/dashboard_screen_binding.dart';

// Future<void> main() async {
//   debugPrint("running ci");
//   await WidgetsFlutterBinding.ensureInitialized();
//   Firebase.initializeApp();
//   var flavourValue = FlavorValues(baseUrl: AppConstants.baseUrlStaging);
//   FlavorConfig(flavor: Flavor.PRODUCTION, values: flavourValue);
//   initSizeUtils();
//   runApp(const MyApp());
// }

// class MyApp extends StatelessWidget {
//   const MyApp({super.key});

//   // This widget is the root of your application.
//   @override
//   Widget build(BuildContext context) {
//     return GetMaterialApp(
//       // navigatorKey: navigatorKey,
//       title: 'Solo Luxury',
//       locale: TranslationService.locale,
//       getPages: AppPages.routes,
//       theme: darkTheme,
//       fallbackLocale: TranslationService.fallbackLocale,
//       translations: TranslationService(),
//       debugShowCheckedModeBanner: false,
//       initialBinding: DashboardBindings(),
//       home: DashboardScreen(),
//     );
//   }
// }
