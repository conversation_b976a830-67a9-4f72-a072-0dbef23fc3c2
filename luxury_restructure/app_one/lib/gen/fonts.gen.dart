/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal

class FontFamily {
  FontFamily._();

  /// Font family: Montserrat
  static const String montserrat = 'Montserrat';

  /// Font family: OpenSans
  static const String openSans = 'OpenSans';

  /// Font family: Poppins
  static const String poppins = 'Poppins';
}
