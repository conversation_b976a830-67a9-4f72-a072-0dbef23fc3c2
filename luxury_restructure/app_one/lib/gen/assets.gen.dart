/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal

import 'package:flutter/widgets.dart';

class $AssetsAnimationsGen {
  const $AssetsAnimationsGen();

  /// File path: assets/animations/ComingSoon.json
  String get comingSoon => 'assets/animations/ComingSoon.json';

  /// File path: assets/animations/Error.json
  String get error => 'assets/animations/Error.json';

  /// File path: assets/animations/NoInternet.json
  String get noInternet => 'assets/animations/NoInternet.json';

  /// File path: assets/animations/NothingToShow.json
  String get nothingToShow => 'assets/animations/NothingToShow.json';

  /// List of all assets
  List<String> get values => [comingSoon, error, noInternet, nothingToShow];
}

class $AssetsFlagsGen {
  const $AssetsFlagsGen();

  /// File path: assets/flags/ad_flag.png
  AssetGenImage get adFlag => const AssetGenImage('assets/flags/ad_flag.png');

  /// File path: assets/flags/ae_flag.png
  AssetGenImage get aeFlag => const AssetGenImage('assets/flags/ae_flag.png');

  /// File path: assets/flags/af_flag.png
  AssetGenImage get afFlag => const AssetGenImage('assets/flags/af_flag.png');

  /// File path: assets/flags/ag_flag.png
  AssetGenImage get agFlag => const AssetGenImage('assets/flags/ag_flag.png');

  /// File path: assets/flags/ai_flag.png
  AssetGenImage get aiFlag => const AssetGenImage('assets/flags/ai_flag.png');

  /// File path: assets/flags/al_flag.png
  AssetGenImage get alFlag => const AssetGenImage('assets/flags/al_flag.png');

  /// File path: assets/flags/am_flag.png
  AssetGenImage get amFlag => const AssetGenImage('assets/flags/am_flag.png');

  /// File path: assets/flags/an_flag.png
  AssetGenImage get anFlag => const AssetGenImage('assets/flags/an_flag.png');

  /// File path: assets/flags/ao_flag.png
  AssetGenImage get aoFlag => const AssetGenImage('assets/flags/ao_flag.png');

  /// File path: assets/flags/aq_flag.png
  AssetGenImage get aqFlag => const AssetGenImage('assets/flags/aq_flag.png');

  /// File path: assets/flags/ar_flag.png
  AssetGenImage get arFlag => const AssetGenImage('assets/flags/ar_flag.png');

  /// File path: assets/flags/as_flag.png
  AssetGenImage get asFlag => const AssetGenImage('assets/flags/as_flag.png');

  /// File path: assets/flags/at_flag.png
  AssetGenImage get atFlag => const AssetGenImage('assets/flags/at_flag.png');

  /// File path: assets/flags/au_flag.png
  AssetGenImage get auFlag => const AssetGenImage('assets/flags/au_flag.png');

  /// File path: assets/flags/aw_flag.png
  AssetGenImage get awFlag => const AssetGenImage('assets/flags/aw_flag.png');

  /// File path: assets/flags/ax_flag.png
  AssetGenImage get axFlag => const AssetGenImage('assets/flags/ax_flag.png');

  /// File path: assets/flags/az_flag.png
  AssetGenImage get azFlag => const AssetGenImage('assets/flags/az_flag.png');

  /// File path: assets/flags/ba_flag.png
  AssetGenImage get baFlag => const AssetGenImage('assets/flags/ba_flag.png');

  /// File path: assets/flags/bb_flag.png
  AssetGenImage get bbFlag => const AssetGenImage('assets/flags/bb_flag.png');

  /// File path: assets/flags/bd_flag.png
  AssetGenImage get bdFlag => const AssetGenImage('assets/flags/bd_flag.png');

  /// File path: assets/flags/be_flag.png
  AssetGenImage get beFlag => const AssetGenImage('assets/flags/be_flag.png');

  /// File path: assets/flags/bf_flag.png
  AssetGenImage get bfFlag => const AssetGenImage('assets/flags/bf_flag.png');

  /// File path: assets/flags/bg_flag.png
  AssetGenImage get bgFlag => const AssetGenImage('assets/flags/bg_flag.png');

  /// File path: assets/flags/bh_flag.png
  AssetGenImage get bhFlag => const AssetGenImage('assets/flags/bh_flag.png');

  /// File path: assets/flags/bi_flag.png
  AssetGenImage get biFlag => const AssetGenImage('assets/flags/bi_flag.png');

  /// File path: assets/flags/bj_flag.png
  AssetGenImage get bjFlag => const AssetGenImage('assets/flags/bj_flag.png');

  /// File path: assets/flags/bl_flag.png
  AssetGenImage get blFlag => const AssetGenImage('assets/flags/bl_flag.png');

  /// File path: assets/flags/bm_flag.png
  AssetGenImage get bmFlag => const AssetGenImage('assets/flags/bm_flag.png');

  /// File path: assets/flags/bn_flag.png
  AssetGenImage get bnFlag => const AssetGenImage('assets/flags/bn_flag.png');

  /// File path: assets/flags/bo_flag.png
  AssetGenImage get boFlag => const AssetGenImage('assets/flags/bo_flag.png');

  /// File path: assets/flags/bq_flag.png
  AssetGenImage get bqFlag => const AssetGenImage('assets/flags/bq_flag.png');

  /// File path: assets/flags/br_flag.png
  AssetGenImage get brFlag => const AssetGenImage('assets/flags/br_flag.png');

  /// File path: assets/flags/bs_flag.png
  AssetGenImage get bsFlag => const AssetGenImage('assets/flags/bs_flag.png');

  /// File path: assets/flags/bt_flag.png
  AssetGenImage get btFlag => const AssetGenImage('assets/flags/bt_flag.png');

  /// File path: assets/flags/bv_flag.png
  AssetGenImage get bvFlag => const AssetGenImage('assets/flags/bv_flag.png');

  /// File path: assets/flags/bw_flag.png
  AssetGenImage get bwFlag => const AssetGenImage('assets/flags/bw_flag.png');

  /// File path: assets/flags/by_flag.png
  AssetGenImage get byFlag => const AssetGenImage('assets/flags/by_flag.png');

  /// File path: assets/flags/bz_flag.png
  AssetGenImage get bzFlag => const AssetGenImage('assets/flags/bz_flag.png');

  /// File path: assets/flags/ca_flag.png
  AssetGenImage get caFlag => const AssetGenImage('assets/flags/ca_flag.png');

  /// File path: assets/flags/cc_flag.png
  AssetGenImage get ccFlag => const AssetGenImage('assets/flags/cc_flag.png');

  /// File path: assets/flags/cd_flag.png
  AssetGenImage get cdFlag => const AssetGenImage('assets/flags/cd_flag.png');

  /// File path: assets/flags/cf_flag.png
  AssetGenImage get cfFlag => const AssetGenImage('assets/flags/cf_flag.png');

  /// File path: assets/flags/cg_flag.png
  AssetGenImage get cgFlag => const AssetGenImage('assets/flags/cg_flag.png');

  /// File path: assets/flags/ch_flag.png
  AssetGenImage get chFlag => const AssetGenImage('assets/flags/ch_flag.png');

  /// File path: assets/flags/ci_flag.png
  AssetGenImage get ciFlag => const AssetGenImage('assets/flags/ci_flag.png');

  /// File path: assets/flags/ck_flag.png
  AssetGenImage get ckFlag => const AssetGenImage('assets/flags/ck_flag.png');

  /// File path: assets/flags/cl_flag.png
  AssetGenImage get clFlag => const AssetGenImage('assets/flags/cl_flag.png');

  /// File path: assets/flags/cm_flag.png
  AssetGenImage get cmFlag => const AssetGenImage('assets/flags/cm_flag.png');

  /// File path: assets/flags/cn_flag.png
  AssetGenImage get cnFlag => const AssetGenImage('assets/flags/cn_flag.png');

  /// File path: assets/flags/co_flag.png
  AssetGenImage get coFlag => const AssetGenImage('assets/flags/co_flag.png');

  /// File path: assets/flags/cr_flag.png
  AssetGenImage get crFlag => const AssetGenImage('assets/flags/cr_flag.png');

  /// File path: assets/flags/cu_flag.png
  AssetGenImage get cuFlag => const AssetGenImage('assets/flags/cu_flag.png');

  /// File path: assets/flags/cv_flag.png
  AssetGenImage get cvFlag => const AssetGenImage('assets/flags/cv_flag.png');

  /// File path: assets/flags/cw_flag.png
  AssetGenImage get cwFlag => const AssetGenImage('assets/flags/cw_flag.png');

  /// File path: assets/flags/cx_flag.png
  AssetGenImage get cxFlag => const AssetGenImage('assets/flags/cx_flag.png');

  /// File path: assets/flags/cy_flag.png
  AssetGenImage get cyFlag => const AssetGenImage('assets/flags/cy_flag.png');

  /// File path: assets/flags/cz_flag.png
  AssetGenImage get czFlag => const AssetGenImage('assets/flags/cz_flag.png');

  /// File path: assets/flags/de_flag.png
  AssetGenImage get deFlag => const AssetGenImage('assets/flags/de_flag.png');

  /// File path: assets/flags/dj_flag.png
  AssetGenImage get djFlag => const AssetGenImage('assets/flags/dj_flag.png');

  /// File path: assets/flags/dk_flag.png
  AssetGenImage get dkFlag => const AssetGenImage('assets/flags/dk_flag.png');

  /// File path: assets/flags/dm_flag.png
  AssetGenImage get dmFlag => const AssetGenImage('assets/flags/dm_flag.png');

  /// File path: assets/flags/do_flag.png
  AssetGenImage get doFlag => const AssetGenImage('assets/flags/do_flag.png');

  /// File path: assets/flags/dz_flag.png
  AssetGenImage get dzFlag => const AssetGenImage('assets/flags/dz_flag.png');

  /// File path: assets/flags/ec_flag.png
  AssetGenImage get ecFlag => const AssetGenImage('assets/flags/ec_flag.png');

  /// File path: assets/flags/ee_flag.png
  AssetGenImage get eeFlag => const AssetGenImage('assets/flags/ee_flag.png');

  /// File path: assets/flags/eg_flag.png
  AssetGenImage get egFlag => const AssetGenImage('assets/flags/eg_flag.png');

  /// File path: assets/flags/eh_flag.png
  AssetGenImage get ehFlag => const AssetGenImage('assets/flags/eh_flag.png');

  /// File path: assets/flags/er_flag.png
  AssetGenImage get erFlag => const AssetGenImage('assets/flags/er_flag.png');

  /// File path: assets/flags/es_flag.png
  AssetGenImage get esFlag => const AssetGenImage('assets/flags/es_flag.png');

  /// File path: assets/flags/et_flag.png
  AssetGenImage get etFlag => const AssetGenImage('assets/flags/et_flag.png');

  /// File path: assets/flags/eu_flag.png
  AssetGenImage get euFlag => const AssetGenImage('assets/flags/eu_flag.png');

  /// File path: assets/flags/fi_flag.png
  AssetGenImage get fiFlag => const AssetGenImage('assets/flags/fi_flag.png');

  /// File path: assets/flags/fj_flag.png
  AssetGenImage get fjFlag => const AssetGenImage('assets/flags/fj_flag.png');

  /// File path: assets/flags/fk_flag.png
  AssetGenImage get fkFlag => const AssetGenImage('assets/flags/fk_flag.png');

  /// File path: assets/flags/fm_flag.png
  AssetGenImage get fmFlag => const AssetGenImage('assets/flags/fm_flag.png');

  /// File path: assets/flags/fo_flag.png
  AssetGenImage get foFlag => const AssetGenImage('assets/flags/fo_flag.png');

  /// File path: assets/flags/fr_flag.png
  AssetGenImage get frFlag => const AssetGenImage('assets/flags/fr_flag.png');

  /// File path: assets/flags/ga_flag.png
  AssetGenImage get gaFlag => const AssetGenImage('assets/flags/ga_flag.png');

  /// File path: assets/flags/gb-eng_flag.png
  AssetGenImage get gbEngFlag => const AssetGenImage('assets/flags/gb-eng_flag.png');

  /// File path: assets/flags/gb-nir_flag.png
  AssetGenImage get gbNirFlag => const AssetGenImage('assets/flags/gb-nir_flag.png');

  /// File path: assets/flags/gb-sct_flag.png
  AssetGenImage get gbSctFlag => const AssetGenImage('assets/flags/gb-sct_flag.png');

  /// File path: assets/flags/gb-wls_flag.png
  AssetGenImage get gbWlsFlag => const AssetGenImage('assets/flags/gb-wls_flag.png');

  /// File path: assets/flags/gb_flag.png
  AssetGenImage get gbFlag => const AssetGenImage('assets/flags/gb_flag.png');

  /// File path: assets/flags/gd_flag.png
  AssetGenImage get gdFlag => const AssetGenImage('assets/flags/gd_flag.png');

  /// File path: assets/flags/ge_flag.png
  AssetGenImage get geFlag => const AssetGenImage('assets/flags/ge_flag.png');

  /// File path: assets/flags/gf_flag.png
  AssetGenImage get gfFlag => const AssetGenImage('assets/flags/gf_flag.png');

  /// File path: assets/flags/gg_flag.png
  AssetGenImage get ggFlag => const AssetGenImage('assets/flags/gg_flag.png');

  /// File path: assets/flags/gh_flag.png
  AssetGenImage get ghFlag => const AssetGenImage('assets/flags/gh_flag.png');

  /// File path: assets/flags/gi_flag.png
  AssetGenImage get giFlag => const AssetGenImage('assets/flags/gi_flag.png');

  /// File path: assets/flags/gl_flag.png
  AssetGenImage get glFlag => const AssetGenImage('assets/flags/gl_flag.png');

  /// File path: assets/flags/gm_flag.png
  AssetGenImage get gmFlag => const AssetGenImage('assets/flags/gm_flag.png');

  /// File path: assets/flags/gn_flag.png
  AssetGenImage get gnFlag => const AssetGenImage('assets/flags/gn_flag.png');

  /// File path: assets/flags/gp_flag.png
  AssetGenImage get gpFlag => const AssetGenImage('assets/flags/gp_flag.png');

  /// File path: assets/flags/gq_flag.png
  AssetGenImage get gqFlag => const AssetGenImage('assets/flags/gq_flag.png');

  /// File path: assets/flags/gr_flag.png
  AssetGenImage get grFlag => const AssetGenImage('assets/flags/gr_flag.png');

  /// File path: assets/flags/gs_flag.png
  AssetGenImage get gsFlag => const AssetGenImage('assets/flags/gs_flag.png');

  /// File path: assets/flags/gt_flag.png
  AssetGenImage get gtFlag => const AssetGenImage('assets/flags/gt_flag.png');

  /// File path: assets/flags/gu_flag.png
  AssetGenImage get guFlag => const AssetGenImage('assets/flags/gu_flag.png');

  /// File path: assets/flags/gw_flag.png
  AssetGenImage get gwFlag => const AssetGenImage('assets/flags/gw_flag.png');

  /// File path: assets/flags/gy_flag.png
  AssetGenImage get gyFlag => const AssetGenImage('assets/flags/gy_flag.png');

  /// File path: assets/flags/hk_flag.png
  AssetGenImage get hkFlag => const AssetGenImage('assets/flags/hk_flag.png');

  /// File path: assets/flags/hm_flag.png
  AssetGenImage get hmFlag => const AssetGenImage('assets/flags/hm_flag.png');

  /// File path: assets/flags/hn_flag.png
  AssetGenImage get hnFlag => const AssetGenImage('assets/flags/hn_flag.png');

  /// File path: assets/flags/hr_flag.png
  AssetGenImage get hrFlag => const AssetGenImage('assets/flags/hr_flag.png');

  /// File path: assets/flags/ht_flag.png
  AssetGenImage get htFlag => const AssetGenImage('assets/flags/ht_flag.png');

  /// File path: assets/flags/hu_flag.png
  AssetGenImage get huFlag => const AssetGenImage('assets/flags/hu_flag.png');

  /// File path: assets/flags/id_flag.png
  AssetGenImage get idFlag => const AssetGenImage('assets/flags/id_flag.png');

  /// File path: assets/flags/ie_flag.png
  AssetGenImage get ieFlag => const AssetGenImage('assets/flags/ie_flag.png');

  /// File path: assets/flags/il_flag.png
  AssetGenImage get ilFlag => const AssetGenImage('assets/flags/il_flag.png');

  /// File path: assets/flags/im_flag.png
  AssetGenImage get imFlag => const AssetGenImage('assets/flags/im_flag.png');

  /// File path: assets/flags/in_flag.png
  AssetGenImage get inFlag => const AssetGenImage('assets/flags/in_flag.png');

  /// File path: assets/flags/io_flag.png
  AssetGenImage get ioFlag => const AssetGenImage('assets/flags/io_flag.png');

  /// File path: assets/flags/iq_flag.png
  AssetGenImage get iqFlag => const AssetGenImage('assets/flags/iq_flag.png');

  /// File path: assets/flags/ir_flag.png
  AssetGenImage get irFlag => const AssetGenImage('assets/flags/ir_flag.png');

  /// File path: assets/flags/is_flag.png
  AssetGenImage get isFlag => const AssetGenImage('assets/flags/is_flag.png');

  /// File path: assets/flags/it_flag.png
  AssetGenImage get itFlag => const AssetGenImage('assets/flags/it_flag.png');

  /// File path: assets/flags/je_flag.png
  AssetGenImage get jeFlag => const AssetGenImage('assets/flags/je_flag.png');

  /// File path: assets/flags/jm_flag.png
  AssetGenImage get jmFlag => const AssetGenImage('assets/flags/jm_flag.png');

  /// File path: assets/flags/jo_flag.png
  AssetGenImage get joFlag => const AssetGenImage('assets/flags/jo_flag.png');

  /// File path: assets/flags/jp_flag.png
  AssetGenImage get jpFlag => const AssetGenImage('assets/flags/jp_flag.png');

  /// File path: assets/flags/ke_flag.png
  AssetGenImage get keFlag => const AssetGenImage('assets/flags/ke_flag.png');

  /// File path: assets/flags/kg_flag.png
  AssetGenImage get kgFlag => const AssetGenImage('assets/flags/kg_flag.png');

  /// File path: assets/flags/kh_flag.png
  AssetGenImage get khFlag => const AssetGenImage('assets/flags/kh_flag.png');

  /// File path: assets/flags/ki_flag.png
  AssetGenImage get kiFlag => const AssetGenImage('assets/flags/ki_flag.png');

  /// File path: assets/flags/km_flag.png
  AssetGenImage get kmFlag => const AssetGenImage('assets/flags/km_flag.png');

  /// File path: assets/flags/kn_flag.png
  AssetGenImage get knFlag => const AssetGenImage('assets/flags/kn_flag.png');

  /// File path: assets/flags/kp_flag.png
  AssetGenImage get kpFlag => const AssetGenImage('assets/flags/kp_flag.png');

  /// File path: assets/flags/kr_flag.png
  AssetGenImage get krFlag => const AssetGenImage('assets/flags/kr_flag.png');

  /// File path: assets/flags/kw_flag.png
  AssetGenImage get kwFlag => const AssetGenImage('assets/flags/kw_flag.png');

  /// File path: assets/flags/ky_flag.png
  AssetGenImage get kyFlag => const AssetGenImage('assets/flags/ky_flag.png');

  /// File path: assets/flags/kz_flag.png
  AssetGenImage get kzFlag => const AssetGenImage('assets/flags/kz_flag.png');

  /// File path: assets/flags/la_flag.png
  AssetGenImage get laFlag => const AssetGenImage('assets/flags/la_flag.png');

  /// File path: assets/flags/lb_flag.png
  AssetGenImage get lbFlag => const AssetGenImage('assets/flags/lb_flag.png');

  /// File path: assets/flags/lc_flag.png
  AssetGenImage get lcFlag => const AssetGenImage('assets/flags/lc_flag.png');

  /// File path: assets/flags/li_flag.png
  AssetGenImage get liFlag => const AssetGenImage('assets/flags/li_flag.png');

  /// File path: assets/flags/lk_flag.png
  AssetGenImage get lkFlag => const AssetGenImage('assets/flags/lk_flag.png');

  /// File path: assets/flags/lr_flag.png
  AssetGenImage get lrFlag => const AssetGenImage('assets/flags/lr_flag.png');

  /// File path: assets/flags/ls_flag.png
  AssetGenImage get lsFlag => const AssetGenImage('assets/flags/ls_flag.png');

  /// File path: assets/flags/lt_flag.png
  AssetGenImage get ltFlag => const AssetGenImage('assets/flags/lt_flag.png');

  /// File path: assets/flags/lu_flag.png
  AssetGenImage get luFlag => const AssetGenImage('assets/flags/lu_flag.png');

  /// File path: assets/flags/lv_flag.png
  AssetGenImage get lvFlag => const AssetGenImage('assets/flags/lv_flag.png');

  /// File path: assets/flags/ly_flag.png
  AssetGenImage get lyFlag => const AssetGenImage('assets/flags/ly_flag.png');

  /// File path: assets/flags/ma_flag.png
  AssetGenImage get maFlag => const AssetGenImage('assets/flags/ma_flag.png');

  /// File path: assets/flags/mc_flag.png
  AssetGenImage get mcFlag => const AssetGenImage('assets/flags/mc_flag.png');

  /// File path: assets/flags/md_flag.png
  AssetGenImage get mdFlag => const AssetGenImage('assets/flags/md_flag.png');

  /// File path: assets/flags/me_flag.png
  AssetGenImage get meFlag => const AssetGenImage('assets/flags/me_flag.png');

  /// File path: assets/flags/mf_flag.png
  AssetGenImage get mfFlag => const AssetGenImage('assets/flags/mf_flag.png');

  /// File path: assets/flags/mg_flag.png
  AssetGenImage get mgFlag => const AssetGenImage('assets/flags/mg_flag.png');

  /// File path: assets/flags/mh_flag.png
  AssetGenImage get mhFlag => const AssetGenImage('assets/flags/mh_flag.png');

  /// File path: assets/flags/mk_flag.png
  AssetGenImage get mkFlag => const AssetGenImage('assets/flags/mk_flag.png');

  /// File path: assets/flags/ml_flag.png
  AssetGenImage get mlFlag => const AssetGenImage('assets/flags/ml_flag.png');

  /// File path: assets/flags/mm_flag.png
  AssetGenImage get mmFlag => const AssetGenImage('assets/flags/mm_flag.png');

  /// File path: assets/flags/mn_flag.png
  AssetGenImage get mnFlag => const AssetGenImage('assets/flags/mn_flag.png');

  /// File path: assets/flags/mo_flag.png
  AssetGenImage get moFlag => const AssetGenImage('assets/flags/mo_flag.png');

  /// File path: assets/flags/mp_flag.png
  AssetGenImage get mpFlag => const AssetGenImage('assets/flags/mp_flag.png');

  /// File path: assets/flags/mq_flag.png
  AssetGenImage get mqFlag => const AssetGenImage('assets/flags/mq_flag.png');

  /// File path: assets/flags/mr_flag.png
  AssetGenImage get mrFlag => const AssetGenImage('assets/flags/mr_flag.png');

  /// File path: assets/flags/ms_flag.png
  AssetGenImage get msFlag => const AssetGenImage('assets/flags/ms_flag.png');

  /// File path: assets/flags/mt_flag.png
  AssetGenImage get mtFlag => const AssetGenImage('assets/flags/mt_flag.png');

  /// File path: assets/flags/mu_flag.png
  AssetGenImage get muFlag => const AssetGenImage('assets/flags/mu_flag.png');

  /// File path: assets/flags/mv_flag.png
  AssetGenImage get mvFlag => const AssetGenImage('assets/flags/mv_flag.png');

  /// File path: assets/flags/mw_flag.png
  AssetGenImage get mwFlag => const AssetGenImage('assets/flags/mw_flag.png');

  /// File path: assets/flags/mx_flag.png
  AssetGenImage get mxFlag => const AssetGenImage('assets/flags/mx_flag.png');

  /// File path: assets/flags/my_flag.png
  AssetGenImage get myFlag => const AssetGenImage('assets/flags/my_flag.png');

  /// File path: assets/flags/mz_flag.png
  AssetGenImage get mzFlag => const AssetGenImage('assets/flags/mz_flag.png');

  /// File path: assets/flags/na_flag.png
  AssetGenImage get naFlag => const AssetGenImage('assets/flags/na_flag.png');

  /// File path: assets/flags/nc_flag.png
  AssetGenImage get ncFlag => const AssetGenImage('assets/flags/nc_flag.png');

  /// File path: assets/flags/ne_flag.png
  AssetGenImage get neFlag => const AssetGenImage('assets/flags/ne_flag.png');

  /// File path: assets/flags/nf_flag.png
  AssetGenImage get nfFlag => const AssetGenImage('assets/flags/nf_flag.png');

  /// File path: assets/flags/ng_flag.png
  AssetGenImage get ngFlag => const AssetGenImage('assets/flags/ng_flag.png');

  /// File path: assets/flags/ni_flag.png
  AssetGenImage get niFlag => const AssetGenImage('assets/flags/ni_flag.png');

  /// File path: assets/flags/nl_flag.png
  AssetGenImage get nlFlag => const AssetGenImage('assets/flags/nl_flag.png');

  /// File path: assets/flags/no_flag.png
  AssetGenImage get noFlag => const AssetGenImage('assets/flags/no_flag.png');

  /// File path: assets/flags/np_flag.png
  AssetGenImage get npFlag => const AssetGenImage('assets/flags/np_flag.png');

  /// File path: assets/flags/nr_flag.png
  AssetGenImage get nrFlag => const AssetGenImage('assets/flags/nr_flag.png');

  /// File path: assets/flags/nu_flag.png
  AssetGenImage get nuFlag => const AssetGenImage('assets/flags/nu_flag.png');

  /// File path: assets/flags/nz_flag.png
  AssetGenImage get nzFlag => const AssetGenImage('assets/flags/nz_flag.png');

  /// File path: assets/flags/om_flag.png
  AssetGenImage get omFlag => const AssetGenImage('assets/flags/om_flag.png');

  /// File path: assets/flags/pa_flag.png
  AssetGenImage get paFlag => const AssetGenImage('assets/flags/pa_flag.png');

  /// File path: assets/flags/pe_flag.png
  AssetGenImage get peFlag => const AssetGenImage('assets/flags/pe_flag.png');

  /// File path: assets/flags/pf_flag.png
  AssetGenImage get pfFlag => const AssetGenImage('assets/flags/pf_flag.png');

  /// File path: assets/flags/pg_flag.png
  AssetGenImage get pgFlag => const AssetGenImage('assets/flags/pg_flag.png');

  /// File path: assets/flags/ph_flag.png
  AssetGenImage get phFlag => const AssetGenImage('assets/flags/ph_flag.png');

  /// File path: assets/flags/pk_flag.png
  AssetGenImage get pkFlag => const AssetGenImage('assets/flags/pk_flag.png');

  /// File path: assets/flags/pl_flag.png
  AssetGenImage get plFlag => const AssetGenImage('assets/flags/pl_flag.png');

  /// File path: assets/flags/pm_flag.png
  AssetGenImage get pmFlag => const AssetGenImage('assets/flags/pm_flag.png');

  /// File path: assets/flags/pn_flag.png
  AssetGenImage get pnFlag => const AssetGenImage('assets/flags/pn_flag.png');

  /// File path: assets/flags/pr_flag.png
  AssetGenImage get prFlag => const AssetGenImage('assets/flags/pr_flag.png');

  /// File path: assets/flags/ps_flag.png
  AssetGenImage get psFlag => const AssetGenImage('assets/flags/ps_flag.png');

  /// File path: assets/flags/pt_flag.png
  AssetGenImage get ptFlag => const AssetGenImage('assets/flags/pt_flag.png');

  /// File path: assets/flags/pw_flag.png
  AssetGenImage get pwFlag => const AssetGenImage('assets/flags/pw_flag.png');

  /// File path: assets/flags/py_flag.png
  AssetGenImage get pyFlag => const AssetGenImage('assets/flags/py_flag.png');

  /// File path: assets/flags/qa_flag.png
  AssetGenImage get qaFlag => const AssetGenImage('assets/flags/qa_flag.png');

  /// File path: assets/flags/re_flag.png
  AssetGenImage get reFlag => const AssetGenImage('assets/flags/re_flag.png');

  /// File path: assets/flags/ro_flag.png
  AssetGenImage get roFlag => const AssetGenImage('assets/flags/ro_flag.png');

  /// File path: assets/flags/rs_flag.png
  AssetGenImage get rsFlag => const AssetGenImage('assets/flags/rs_flag.png');

  /// File path: assets/flags/ru_flag.png
  AssetGenImage get ruFlag => const AssetGenImage('assets/flags/ru_flag.png');

  /// File path: assets/flags/rw_flag.png
  AssetGenImage get rwFlag => const AssetGenImage('assets/flags/rw_flag.png');

  /// File path: assets/flags/sa_flag.png
  AssetGenImage get saFlag => const AssetGenImage('assets/flags/sa_flag.png');

  /// File path: assets/flags/sb_flag.png
  AssetGenImage get sbFlag => const AssetGenImage('assets/flags/sb_flag.png');

  /// File path: assets/flags/sc_flag.png
  AssetGenImage get scFlag => const AssetGenImage('assets/flags/sc_flag.png');

  /// File path: assets/flags/sd_flag.png
  AssetGenImage get sdFlag => const AssetGenImage('assets/flags/sd_flag.png');

  /// File path: assets/flags/se_flag.png
  AssetGenImage get seFlag => const AssetGenImage('assets/flags/se_flag.png');

  /// File path: assets/flags/sg_flag.png
  AssetGenImage get sgFlag => const AssetGenImage('assets/flags/sg_flag.png');

  /// File path: assets/flags/sh_flag.png
  AssetGenImage get shFlag => const AssetGenImage('assets/flags/sh_flag.png');

  /// File path: assets/flags/si_flag.png
  AssetGenImage get siFlag => const AssetGenImage('assets/flags/si_flag.png');

  /// File path: assets/flags/sj_flag.png
  AssetGenImage get sjFlag => const AssetGenImage('assets/flags/sj_flag.png');

  /// File path: assets/flags/sk_flag.png
  AssetGenImage get skFlag => const AssetGenImage('assets/flags/sk_flag.png');

  /// File path: assets/flags/sl_flag.png
  AssetGenImage get slFlag => const AssetGenImage('assets/flags/sl_flag.png');

  /// File path: assets/flags/sm_flag.png
  AssetGenImage get smFlag => const AssetGenImage('assets/flags/sm_flag.png');

  /// File path: assets/flags/sn_flag.png
  AssetGenImage get snFlag => const AssetGenImage('assets/flags/sn_flag.png');

  /// File path: assets/flags/so_flag.png
  AssetGenImage get soFlag => const AssetGenImage('assets/flags/so_flag.png');

  /// File path: assets/flags/sr_flag.png
  AssetGenImage get srFlag => const AssetGenImage('assets/flags/sr_flag.png');

  /// File path: assets/flags/ss_flag.png
  AssetGenImage get ssFlag => const AssetGenImage('assets/flags/ss_flag.png');

  /// File path: assets/flags/st_flag.png
  AssetGenImage get stFlag => const AssetGenImage('assets/flags/st_flag.png');

  /// File path: assets/flags/sv_flag.png
  AssetGenImage get svFlag => const AssetGenImage('assets/flags/sv_flag.png');

  /// File path: assets/flags/sx_flag.png
  AssetGenImage get sxFlag => const AssetGenImage('assets/flags/sx_flag.png');

  /// File path: assets/flags/sy_flag.png
  AssetGenImage get syFlag => const AssetGenImage('assets/flags/sy_flag.png');

  /// File path: assets/flags/sz_flag.png
  AssetGenImage get szFlag => const AssetGenImage('assets/flags/sz_flag.png');

  /// File path: assets/flags/tc_flag.png
  AssetGenImage get tcFlag => const AssetGenImage('assets/flags/tc_flag.png');

  /// File path: assets/flags/td_flag.png
  AssetGenImage get tdFlag => const AssetGenImage('assets/flags/td_flag.png');

  /// File path: assets/flags/tf_flag.png
  AssetGenImage get tfFlag => const AssetGenImage('assets/flags/tf_flag.png');

  /// File path: assets/flags/tg_flag.png
  AssetGenImage get tgFlag => const AssetGenImage('assets/flags/tg_flag.png');

  /// File path: assets/flags/th_flag.png
  AssetGenImage get thFlag => const AssetGenImage('assets/flags/th_flag.png');

  /// File path: assets/flags/tj_flag.png
  AssetGenImage get tjFlag => const AssetGenImage('assets/flags/tj_flag.png');

  /// File path: assets/flags/tk_flag.png
  AssetGenImage get tkFlag => const AssetGenImage('assets/flags/tk_flag.png');

  /// File path: assets/flags/tl_flag.png
  AssetGenImage get tlFlag => const AssetGenImage('assets/flags/tl_flag.png');

  /// File path: assets/flags/tm_flag.png
  AssetGenImage get tmFlag => const AssetGenImage('assets/flags/tm_flag.png');

  /// File path: assets/flags/tn_flag.png
  AssetGenImage get tnFlag => const AssetGenImage('assets/flags/tn_flag.png');

  /// File path: assets/flags/to_flag.png
  AssetGenImage get toFlag => const AssetGenImage('assets/flags/to_flag.png');

  /// File path: assets/flags/tr_flag.png
  AssetGenImage get trFlag => const AssetGenImage('assets/flags/tr_flag.png');

  /// File path: assets/flags/tt_flag.png
  AssetGenImage get ttFlag => const AssetGenImage('assets/flags/tt_flag.png');

  /// File path: assets/flags/tv_flag.png
  AssetGenImage get tvFlag => const AssetGenImage('assets/flags/tv_flag.png');

  /// File path: assets/flags/tw_flag.png
  AssetGenImage get twFlag => const AssetGenImage('assets/flags/tw_flag.png');

  /// File path: assets/flags/tz_flag.png
  AssetGenImage get tzFlag => const AssetGenImage('assets/flags/tz_flag.png');

  /// File path: assets/flags/ua_flag.png
  AssetGenImage get uaFlag => const AssetGenImage('assets/flags/ua_flag.png');

  /// File path: assets/flags/ug_flag.png
  AssetGenImage get ugFlag => const AssetGenImage('assets/flags/ug_flag.png');

  /// File path: assets/flags/um_flag.png
  AssetGenImage get umFlag => const AssetGenImage('assets/flags/um_flag.png');

  /// File path: assets/flags/us_flag.png
  AssetGenImage get usFlag => const AssetGenImage('assets/flags/us_flag.png');

  /// File path: assets/flags/uy_flag.png
  AssetGenImage get uyFlag => const AssetGenImage('assets/flags/uy_flag.png');

  /// File path: assets/flags/uz_flag.png
  AssetGenImage get uzFlag => const AssetGenImage('assets/flags/uz_flag.png');

  /// File path: assets/flags/va_flag.png
  AssetGenImage get vaFlag => const AssetGenImage('assets/flags/va_flag.png');

  /// File path: assets/flags/vc_flag.png
  AssetGenImage get vcFlag => const AssetGenImage('assets/flags/vc_flag.png');

  /// File path: assets/flags/ve_flag.png
  AssetGenImage get veFlag => const AssetGenImage('assets/flags/ve_flag.png');

  /// File path: assets/flags/vg_flag.png
  AssetGenImage get vgFlag => const AssetGenImage('assets/flags/vg_flag.png');

  /// File path: assets/flags/vi_flag.png
  AssetGenImage get viFlag => const AssetGenImage('assets/flags/vi_flag.png');

  /// File path: assets/flags/vn_flag.png
  AssetGenImage get vnFlag => const AssetGenImage('assets/flags/vn_flag.png');

  /// File path: assets/flags/vu_flag.png
  AssetGenImage get vuFlag => const AssetGenImage('assets/flags/vu_flag.png');

  /// File path: assets/flags/wf_flag.png
  AssetGenImage get wfFlag => const AssetGenImage('assets/flags/wf_flag.png');

  /// File path: assets/flags/ws_flag.png
  AssetGenImage get wsFlag => const AssetGenImage('assets/flags/ws_flag.png');

  /// File path: assets/flags/xk_flag.png
  AssetGenImage get xkFlag => const AssetGenImage('assets/flags/xk_flag.png');

  /// File path: assets/flags/ye_flag.png
  AssetGenImage get yeFlag => const AssetGenImage('assets/flags/ye_flag.png');

  /// File path: assets/flags/yt_flag.png
  AssetGenImage get ytFlag => const AssetGenImage('assets/flags/yt_flag.png');

  /// File path: assets/flags/za_flag.png
  AssetGenImage get zaFlag => const AssetGenImage('assets/flags/za_flag.png');

  /// File path: assets/flags/zm_flag.png
  AssetGenImage get zmFlag => const AssetGenImage('assets/flags/zm_flag.png');

  /// File path: assets/flags/zw_flag.png
  AssetGenImage get zwFlag => const AssetGenImage('assets/flags/zw_flag.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        adFlag,
        aeFlag,
        afFlag,
        agFlag,
        aiFlag,
        alFlag,
        amFlag,
        anFlag,
        aoFlag,
        aqFlag,
        arFlag,
        asFlag,
        atFlag,
        auFlag,
        awFlag,
        axFlag,
        azFlag,
        baFlag,
        bbFlag,
        bdFlag,
        beFlag,
        bfFlag,
        bgFlag,
        bhFlag,
        biFlag,
        bjFlag,
        blFlag,
        bmFlag,
        bnFlag,
        boFlag,
        bqFlag,
        brFlag,
        bsFlag,
        btFlag,
        bvFlag,
        bwFlag,
        byFlag,
        bzFlag,
        caFlag,
        ccFlag,
        cdFlag,
        cfFlag,
        cgFlag,
        chFlag,
        ciFlag,
        ckFlag,
        clFlag,
        cmFlag,
        cnFlag,
        coFlag,
        crFlag,
        cuFlag,
        cvFlag,
        cwFlag,
        cxFlag,
        cyFlag,
        czFlag,
        deFlag,
        djFlag,
        dkFlag,
        dmFlag,
        doFlag,
        dzFlag,
        ecFlag,
        eeFlag,
        egFlag,
        ehFlag,
        erFlag,
        esFlag,
        etFlag,
        euFlag,
        fiFlag,
        fjFlag,
        fkFlag,
        fmFlag,
        foFlag,
        frFlag,
        gaFlag,
        gbEngFlag,
        gbNirFlag,
        gbSctFlag,
        gbWlsFlag,
        gbFlag,
        gdFlag,
        geFlag,
        gfFlag,
        ggFlag,
        ghFlag,
        giFlag,
        glFlag,
        gmFlag,
        gnFlag,
        gpFlag,
        gqFlag,
        grFlag,
        gsFlag,
        gtFlag,
        guFlag,
        gwFlag,
        gyFlag,
        hkFlag,
        hmFlag,
        hnFlag,
        hrFlag,
        htFlag,
        huFlag,
        idFlag,
        ieFlag,
        ilFlag,
        imFlag,
        inFlag,
        ioFlag,
        iqFlag,
        irFlag,
        isFlag,
        itFlag,
        jeFlag,
        jmFlag,
        joFlag,
        jpFlag,
        keFlag,
        kgFlag,
        khFlag,
        kiFlag,
        kmFlag,
        knFlag,
        kpFlag,
        krFlag,
        kwFlag,
        kyFlag,
        kzFlag,
        laFlag,
        lbFlag,
        lcFlag,
        liFlag,
        lkFlag,
        lrFlag,
        lsFlag,
        ltFlag,
        luFlag,
        lvFlag,
        lyFlag,
        maFlag,
        mcFlag,
        mdFlag,
        meFlag,
        mfFlag,
        mgFlag,
        mhFlag,
        mkFlag,
        mlFlag,
        mmFlag,
        mnFlag,
        moFlag,
        mpFlag,
        mqFlag,
        mrFlag,
        msFlag,
        mtFlag,
        muFlag,
        mvFlag,
        mwFlag,
        mxFlag,
        myFlag,
        mzFlag,
        naFlag,
        ncFlag,
        neFlag,
        nfFlag,
        ngFlag,
        niFlag,
        nlFlag,
        noFlag,
        npFlag,
        nrFlag,
        nuFlag,
        nzFlag,
        omFlag,
        paFlag,
        peFlag,
        pfFlag,
        pgFlag,
        phFlag,
        pkFlag,
        plFlag,
        pmFlag,
        pnFlag,
        prFlag,
        psFlag,
        ptFlag,
        pwFlag,
        pyFlag,
        qaFlag,
        reFlag,
        roFlag,
        rsFlag,
        ruFlag,
        rwFlag,
        saFlag,
        sbFlag,
        scFlag,
        sdFlag,
        seFlag,
        sgFlag,
        shFlag,
        siFlag,
        sjFlag,
        skFlag,
        slFlag,
        smFlag,
        snFlag,
        soFlag,
        srFlag,
        ssFlag,
        stFlag,
        svFlag,
        sxFlag,
        syFlag,
        szFlag,
        tcFlag,
        tdFlag,
        tfFlag,
        tgFlag,
        thFlag,
        tjFlag,
        tkFlag,
        tlFlag,
        tmFlag,
        tnFlag,
        toFlag,
        trFlag,
        ttFlag,
        tvFlag,
        twFlag,
        tzFlag,
        uaFlag,
        ugFlag,
        umFlag,
        usFlag,
        uyFlag,
        uzFlag,
        vaFlag,
        vcFlag,
        veFlag,
        vgFlag,
        viFlag,
        vnFlag,
        vuFlag,
        wfFlag,
        wsFlag,
        xkFlag,
        yeFlag,
        ytFlag,
        zaFlag,
        zmFlag,
        zwFlag
      ];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/account.png
  AssetGenImage get account => const AssetGenImage('assets/icons/account.png');

  /// File path: assets/icons/bag.png
  AssetGenImage get bag => const AssetGenImage('assets/icons/bag.png');

  /// File path: assets/icons/brands_labels_logo.png
  AssetGenImage get brandsLabelsLogo => const AssetGenImage('assets/icons/brands_labels_logo.png');

  /// File path: assets/icons/calendar.png
  AssetGenImage get calendar => const AssetGenImage('assets/icons/calendar.png');

  /// File path: assets/icons/cart.png
  AssetGenImage get cartPng => const AssetGenImage('assets/icons/cart.png');

  /// File path: assets/icons/cart.svg
  String get cartSvg => 'assets/icons/cart.svg';

  /// File path: assets/icons/check.png
  AssetGenImage get check => const AssetGenImage('assets/icons/check.png');

  /// File path: assets/icons/close.png
  AssetGenImage get close => const AssetGenImage('assets/icons/close.png');

  /// File path: assets/icons/delete.png
  AssetGenImage get delete => const AssetGenImage('assets/icons/delete.png');

  /// File path: assets/icons/designers.png
  AssetGenImage get designers => const AssetGenImage('assets/icons/designers.png');

  /// File path: assets/icons/down_arrow.png
  AssetGenImage get downArrow => const AssetGenImage('assets/icons/down_arrow.png');

  /// File path: assets/icons/drawer.png
  AssetGenImage get drawer => const AssetGenImage('assets/icons/drawer.png');

  /// File path: assets/icons/filter.svg
  String get filter => 'assets/icons/filter.svg';

  /// File path: assets/icons/flag.png
  AssetGenImage get flag => const AssetGenImage('assets/icons/flag.png');

  /// File path: assets/icons/heart.png
  AssetGenImage get heartPng => const AssetGenImage('assets/icons/heart.png');

  /// File path: assets/icons/heart.svg
  String get heartSvg => 'assets/icons/heart.svg';

  /// File path: assets/icons/home.png
  AssetGenImage get home => const AssetGenImage('assets/icons/home.png');

  /// File path: assets/icons/indian_flag.png
  AssetGenImage get indianFlag => const AssetGenImage('assets/icons/indian_flag.png');

  /// File path: assets/icons/lefticon.png
  AssetGenImage get lefticon => const AssetGenImage('assets/icons/lefticon.png');

  /// File path: assets/icons/menu.png
  AssetGenImage get menu => const AssetGenImage('assets/icons/menu.png');

  /// File path: assets/icons/minus.png
  AssetGenImage get minus => const AssetGenImage('assets/icons/minus.png');

  /// File path: assets/icons/plus.png
  AssetGenImage get plus => const AssetGenImage('assets/icons/plus.png');

  /// File path: assets/icons/righticon.png
  AssetGenImage get righticon => const AssetGenImage('assets/icons/righticon.png');

  /// File path: assets/icons/search.png
  AssetGenImage get searchPng => const AssetGenImage('assets/icons/search.png');

  /// File path: assets/icons/search.svg
  String get searchSvg => 'assets/icons/search.svg';

  /// File path: assets/icons/sort.png
  AssetGenImage get sort => const AssetGenImage('assets/icons/sort.png');

  /// File path: assets/icons/unchecked.png
  AssetGenImage get unchecked => const AssetGenImage('assets/icons/unchecked.png');

  /// File path: assets/icons/up_arrow.png
  AssetGenImage get upArrow => const AssetGenImage('assets/icons/up_arrow.png');

  /// File path: assets/icons/user.png
  AssetGenImage get user => const AssetGenImage('assets/icons/user.png');

  /// File path: assets/icons/user_profile.png
  AssetGenImage get userProfile => const AssetGenImage('assets/icons/user_profile.png');

  /// List of all assets
  List<dynamic> get values => [
        account,
        bag,
        brandsLabelsLogo,
        calendar,
        cartPng,
        cartSvg,
        check,
        close,
        delete,
        designers,
        downArrow,
        drawer,
        filter,
        flag,
        heartPng,
        heartSvg,
        home,
        indianFlag,
        lefticon,
        menu,
        minus,
        plus,
        righticon,
        searchPng,
        searchSvg,
        sort,
        unchecked,
        upArrow,
        user,
        userProfile
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/Ralph-Lauren-Symbol 1.png
  AssetGenImage get ralphLaurenSymbol1 => const AssetGenImage('assets/images/Ralph-Lauren-Symbol 1.png');

  /// File path: assets/images/Tory_Burch_logo 1.png
  AssetGenImage get toryBurchLogo1 => const AssetGenImage('assets/images/Tory_Burch_logo 1.png');

  /// File path: assets/images/bag.png
  AssetGenImage get bag => const AssetGenImage('assets/images/bag.png');

  /// File path: assets/images/brand_list.png
  AssetGenImage get brandList => const AssetGenImage('assets/images/brand_list.png');

  /// File path: assets/images/brandandlabel.png
  AssetGenImage get brandandlabel => const AssetGenImage('assets/images/brandandlabel.png');

  /// File path: assets/images/brandlabel.png
  AssetGenImage get brandlabel => const AssetGenImage('assets/images/brandlabel.png');

  /// File path: assets/images/brands_labels.png
  AssetGenImage get brandsLabels => const AssetGenImage('assets/images/brands_labels.png');

  /// File path: assets/images/charity1.png
  AssetGenImage get charity1 => const AssetGenImage('assets/images/charity1.png');

  /// File path: assets/images/charity2.png
  AssetGenImage get charity2 => const AssetGenImage('assets/images/charity2.png');

  /// File path: assets/images/charity3.png
  AssetGenImage get charity3 => const AssetGenImage('assets/images/charity3.png');

  /// File path: assets/images/charity4.png
  AssetGenImage get charity4 => const AssetGenImage('assets/images/charity4.png');

  /// File path: assets/images/delete.png
  AssetGenImage get delete => const AssetGenImage('assets/images/delete.png');

  /// File path: assets/images/edit.png
  AssetGenImage get edit => const AssetGenImage('assets/images/edit.png');

  /// File path: assets/images/flag.png
  AssetGenImage get flag => const AssetGenImage('assets/images/flag.png');

  /// File path: assets/images/he.png
  AssetGenImage get he => const AssetGenImage('assets/images/he.png');

  /// File path: assets/images/header_logo.png
  AssetGenImage get headerLogo => const AssetGenImage('assets/images/header_logo.png');

  $AssetsImagesHomeGen get home => const $AssetsImagesHomeGen();

  /// File path: assets/images/img_shadow.png
  AssetGenImage get imgShadow => const AssetGenImage('assets/images/img_shadow.png');

  /// File path: assets/images/india.png
  AssetGenImage get india => const AssetGenImage('assets/images/india.png');

  /// File path: assets/images/india_flag.png
  AssetGenImage get indiaFlag => const AssetGenImage('assets/images/india_flag.png');

  /// File path: assets/images/jeans.png
  AssetGenImage get jeans => const AssetGenImage('assets/images/jeans.png');

  /// File path: assets/images/m_logo.png
  AssetGenImage get mLogo => const AssetGenImage('assets/images/m_logo.png');

  /// File path: assets/images/magnifying2.png
  AssetGenImage get magnifying2 => const AssetGenImage('assets/images/magnifying2.png');

  /// File path: assets/images/magnifying_glass.png
  AssetGenImage get magnifyingGlass => const AssetGenImage('assets/images/magnifying_glass.png');

  /// File path: assets/images/menu.png
  AssetGenImage get menu => const AssetGenImage('assets/images/menu.png');

  /// File path: assets/images/menu1.png
  AssetGenImage get menu1 => const AssetGenImage('assets/images/menu1.png');

  /// File path: assets/images/shoppingBasket.png
  AssetGenImage get shoppingBasket => const AssetGenImage('assets/images/shoppingBasket.png');

  /// File path: assets/images/user.png
  AssetGenImage get user => const AssetGenImage('assets/images/user.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        ralphLaurenSymbol1,
        toryBurchLogo1,
        bag,
        brandList,
        brandandlabel,
        brandlabel,
        brandsLabels,
        charity1,
        charity2,
        charity3,
        charity4,
        delete,
        edit,
        flag,
        he,
        headerLogo,
        imgShadow,
        india,
        indiaFlag,
        jeans,
        mLogo,
        magnifying2,
        magnifyingGlass,
        menu,
        menu1,
        shoppingBasket,
        user
      ];
}

class $AssetsJsonGen {
  const $AssetsJsonGen();

  /// File path: assets/json/get-location.json
  String get getLocation => 'assets/json/get-location.json';

  /// File path: assets/json/loader.json
  String get loader => 'assets/json/loader.json';

  /// File path: assets/json/nodata.json
  String get nodata => 'assets/json/nodata.json';

  /// List of all assets
  List<String> get values => [getLocation, loader, nodata];
}

class $AssetsSvgsGen {
  const $AssetsSvgsGen();

  /// File path: assets/svgs/addTocart.svg
  String get addTocart => 'assets/svgs/addTocart.svg';

  /// File path: assets/svgs/addTowhishlist.svg
  String get addTowhishlist => 'assets/svgs/addTowhishlist.svg';

  /// File path: assets/svgs/applogo.svg
  String get applogo => 'assets/svgs/applogo.svg';

  /// File path: assets/svgs/cart_selected.svg
  String get cartSelected => 'assets/svgs/cart_selected.svg';

  /// File path: assets/svgs/cart_unselected.svg
  String get cartUnselected => 'assets/svgs/cart_unselected.svg';

  /// File path: assets/svgs/designers_selected.svg
  String get designersSelected => 'assets/svgs/designers_selected.svg';

  /// File path: assets/svgs/designers_unselected.svg
  String get designersUnselected => 'assets/svgs/designers_unselected.svg';

  /// File path: assets/svgs/filterIcon.svg
  String get filterIcon => 'assets/svgs/filterIcon.svg';

  /// File path: assets/svgs/home_selected.svg
  String get homeSelected => 'assets/svgs/home_selected.svg';

  /// File path: assets/svgs/home_unselected.svg
  String get homeUnselected => 'assets/svgs/home_unselected.svg';

  /// File path: assets/svgs/logo.svg
  String get logo => 'assets/svgs/logo.svg';

  /// File path: assets/svgs/menu_icon.svg
  String get menuIcon => 'assets/svgs/menu_icon.svg';

  /// File path: assets/svgs/search.svg
  String get search => 'assets/svgs/search.svg';

  /// File path: assets/svgs/search_selected.svg
  String get searchSelected => 'assets/svgs/search_selected.svg';

  /// File path: assets/svgs/search_unselected.svg
  String get searchUnselected => 'assets/svgs/search_unselected.svg';

  /// File path: assets/svgs/sortIcon.svg
  String get sortIcon => 'assets/svgs/sortIcon.svg';

  /// File path: assets/svgs/user_selected.svg
  String get userSelected => 'assets/svgs/user_selected.svg';

  /// File path: assets/svgs/user_unselected.svg
  String get userUnselected => 'assets/svgs/user_unselected.svg';

  /// File path: assets/svg/notification.svg
  String get notification => 'assets/svgs/notification.svg';

  /// List of all assets
  List<String> get values => [
        addTocart,
        addTowhishlist,
        applogo,
        cartSelected,
        cartUnselected,
        designersSelected,
        designersUnselected,
        filterIcon,
        homeSelected,
        homeUnselected,
        logo,
        menuIcon,
        search,
        searchSelected,
        searchUnselected,
        sortIcon,
        userSelected,
        userUnselected
      ];
}

class $AssetsImagesHomeGen {
  const $AssetsImagesHomeGen();

  /// File path: assets/images/home/<USER>
  AssetGenImage get homeBanner => const AssetGenImage('assets/images/home/<USER>');

  /// File path: assets/images/home/<USER>
  AssetGenImage get item0 => const AssetGenImage('assets/images/home/<USER>');

  /// File path: assets/images/home/<USER>
  AssetGenImage get item1 => const AssetGenImage('assets/images/home/<USER>');

  /// File path: assets/images/home/<USER>
  AssetGenImage get item2 => const AssetGenImage('assets/images/home/<USER>');

  /// File path: assets/images/home/<USER>
  AssetGenImage get item3 => const AssetGenImage('assets/images/home/<USER>');

  /// File path: assets/images/home/<USER>
  AssetGenImage get item4 => const AssetGenImage('assets/images/home/<USER>');

  /// File path: assets/images/home/<USER>
  AssetGenImage get item5 => const AssetGenImage('assets/images/home/<USER>');

  /// File path: assets/images/homeImages/accessories.png
  AssetGenImage get accessories => const AssetGenImage('assets/images/homeImages/accessories.png');

  /// File path: assets/images/homeImages/kids.png
  AssetGenImage get kids => const AssetGenImage('assets/images/homeImages/kids.png');

  /// File path: assets/images/homeImages/men.png
  AssetGenImage get men => const AssetGenImage('assets/images/homeImages/men.png');

  /// File path: assets/images/homeImages/shoes.png
  AssetGenImage get shoes => const AssetGenImage('assets/images/homeImages/shoes.png');

  /// File path: assets/images/homeImages/women.png
  AssetGenImage get women => const AssetGenImage('assets/images/homeImages/women.png');

  /// File path: assets/images/homeImages/slide1.png
  AssetGenImage get homeSlide1 => const AssetGenImage('assets/images/homeImages/slide1.png');

  /// File path: assets/images/homeImages/slide2.png
  AssetGenImage get homeSlide2 => const AssetGenImage('assets/images/homeImages/slide2.png');

  /// File path: assets/images/homeImages/slide3.png
  AssetGenImage get homeSlide3 => const AssetGenImage('assets/images/homeImages/slide3.jpg');

  /// File path: assets/images/homeImages/bestSeller1.png
  AssetGenImage get bestSeller1 => const AssetGenImage('assets/images/homeImages/bestSeller1.png');

  /// File path: assets/images/homeImages/bestSeller2.png
  AssetGenImage get bestSeller2 => const AssetGenImage('assets/images/homeImages/bestSeller2.png');

  /// File path: assets/images/homeImages/bestSeller3.png
  AssetGenImage get bestSeller3 => const AssetGenImage('assets/images/homeImages/bestSeller3.png');

  /// File path: assets/images/homeImages/bestSeller4.png
  AssetGenImage get bestSeller4 => const AssetGenImage('assets/images/homeImages/bestSeller4.png');

  /// File path: assets/images/homeImages/newsLetterBanner.png
  AssetGenImage get newsLetterBanner => const AssetGenImage('assets/images/homeImages/newsLetterBanner.png');

  /// File path: assets/images/homeImages/productsBanner.png
  AssetGenImage get productsBanner => const AssetGenImage('assets/images/homeImages/productsBanner.png');

  /// File path: assets/images/homeImages/freeShipping.png
  AssetGenImage get freeShippingBanner => const AssetGenImage('assets/images/homeImages/freeShipping.png');

  /// File path: assets/images/homeImages/taxInfoBanner.png
  AssetGenImage get taxInfoBanner => const AssetGenImage('assets/images/homeImages/taxInfoBanner.png');

  /// File path: assets/images/homeImages/baggy.png
  AssetGenImage get baggy => const AssetGenImage('assets/images/homeImages/baggy.png');

  /// File path: assets/images/homeImages/hiphop.png
  AssetGenImage get hiphop => const AssetGenImage('assets/images/homeImages/hiphop.png');

  /// File path: assets/images/homeImages/trendy.png
  AssetGenImage get trendy => const AssetGenImage('assets/images/homeImages/trendy.png');

  /// File path: assets/images/homeImages/funky.png
  AssetGenImage get funky => const AssetGenImage('assets/images/homeImages/funky.png');

  /// List of all assets
  List<AssetGenImage> get values =>
      [accessories, homeBanner, item0, item1, item2, item3, item4, item5, kids, men, shoes, women];
}

class Assets {
  Assets._();

  static const $AssetsAnimationsGen animations = $AssetsAnimationsGen();
  static const $AssetsFlagsGen flags = $AssetsFlagsGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsJsonGen json = $AssetsJsonGen();
  static const $AssetsSvgsGen svgs = $AssetsSvgsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName);

  final String _assetName;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = false,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider() => AssetImage(_assetName);

  String get path => _assetName;

  String get keyName => _assetName;
}
