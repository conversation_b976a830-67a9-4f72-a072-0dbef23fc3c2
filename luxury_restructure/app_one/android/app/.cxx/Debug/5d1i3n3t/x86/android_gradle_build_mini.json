{"buildFiles": ["/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/AndroidStudioProjects/master-/luxury_restructure/app_one/android/app/.cxx/Debug/5d1i3n3t/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/AndroidStudioProjects/master-/luxury_restructure/app_one/android/app/.cxx/Debug/5d1i3n3t/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}