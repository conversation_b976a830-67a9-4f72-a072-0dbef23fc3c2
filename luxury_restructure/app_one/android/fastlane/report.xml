<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: default_platform" time="0.000245513">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: google_play_track_version_codes" time="1.675547487">
        
          <failure message="/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/actions/actions_helper.rb:67:in `execute_action&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/runner.rb:255:in `block in execute_action&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/runner.rb:229:in `chdir&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/runner.rb:229:in `execute_action&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/runner.rb:157:in `trigger_action_by_name&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/fast_file.rb:159:in `method_missing&apos;&#10;Fastfile:24:in `block (2 levels) in parsing_binding&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/lane.rb:33:in `call&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/runner.rb:49:in `block in execute&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/runner.rb:45:in `chdir&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/runner.rb:45:in `execute&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/lane_manager.rb:47:in `cruise_lane&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/command_line_handler.rb:36:in `handle&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/commands_generator.rb:110:in `block (2 levels) in run&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/commander-4.6.0/lib/commander/command.rb:187:in `call&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/commander-4.6.0/lib/commander/command.rb:157:in `run&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/commander-4.6.0/lib/commander/runner.rb:444:in `run_active_command&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane_core/lib/fastlane_core/ui/fastlane_runner.rb:124:in `run!&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/commander-4.6.0/lib/commander/delegates.rb:18:in `run!&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/commands_generator.rb:354:in `run&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/commands_generator.rb:43:in `start&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/fastlane/lib/fastlane/cli_tools_distributor.rb:123:in `take_off&apos;&#10;/usr/lib/ruby/gems/3.0.0/gems/fastlane-2.211.0/bin/fastlane:23:in `&lt;top (required)&gt;&apos;&#10;/usr/bin/fastlane:25:in `load&apos;&#10;/usr/bin/fastlane:25:in `&lt;top (required)&gt;&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/lib/bundler/cli/exec.rb:58:in `load&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/lib/bundler/cli/exec.rb:58:in `kernel_load&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/lib/bundler/cli/exec.rb:23:in `run&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/lib/bundler/cli.rb:486:in `exec&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/lib/bundler/vendor/thor/lib/thor/command.rb:27:in `run&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/lib/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/lib/bundler/vendor/thor/lib/thor.rb:392:in `dispatch&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/lib/bundler/cli.rb:31:in `dispatch&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/lib/bundler/vendor/thor/lib/thor/base.rb:485:in `start&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/lib/bundler/cli.rb:25:in `start&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/exe/bundle:48:in `block in &lt;top (required)&gt;&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/lib/bundler/friendly_errors.rb:120:in `with_friendly_errors&apos;&#10;/home/<USER>/.local/share/gem/ruby/3.0.0/gems/bundler-2.3.26/exe/bundle:36:in `&lt;top (required)&gt;&apos;&#10;/usr/bin/bundle:25:in `load&apos;&#10;/usr/bin/bundle:25:in `&lt;main&gt;&apos;&#10;&#10;Invalid request" />
        
      </testcase>
    
  </testsuite>
</testsuites>
