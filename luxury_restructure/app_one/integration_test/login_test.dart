import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:app_one/main/main.prod.dart' as app;
import 'package:presentation_common_ui/integration_test/login_app_test_helper.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets("Login Integration Test", (WidgetTester tester) async {
    final originalOnError = FlutterError.onError;
    FlutterError.onError = (FlutterErrorDetails details) {
      debugPrint(details.toString());
    };
    try {
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      await runCommonLoginTest(tester);
    } catch (e) {
      print('Test failed with error: $e');
      fail('Test failed with error: $e');
    } finally {
      FlutterError.onError = originalOnError;
    }
  });
}
