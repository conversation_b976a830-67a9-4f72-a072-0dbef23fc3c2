import 'package:app_one/main/main.prod.dart' as app_one;
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:presentation_common_ui/integration_test/test_flow.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('App Two Basic Navigation Test', (WidgetTester tester) async {
    final originalOnError = FlutterError.onError;

    FlutterError.onError = (FlutterErrorDetails details) {
      print(details.toString());
      if (details.exception is AssertionError) {
        throw details.exception;
      }
    };


    try {
      app_one.main();

      await tester.pumpAndSettle(const Duration(seconds: 2));


      await runCommonLoginTest(tester);
    } catch (e) {
      print('Test failed with error: $e');
      fail('Test failed with error: $e');
    } finally {
      FlutterError.onError = originalOnError;
    }
  });

}
