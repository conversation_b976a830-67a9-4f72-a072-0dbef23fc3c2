<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- App Information -->
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>

    <key>CFBundleDisplayName</key>
    <string>Brand Label</string>

    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>

    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>

    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>

    <key>CFBundleName</key>
    <string>app_one</string>

    <key>CFBundlePackageType</key>
    <string>APPL</string>

    <key>CFBundleShortVersionString</key>
    <string>$(FLUTTER_BUILD_NAME)</string>

    <key>CFBundleVersion</key>
    <string>$(FLUTTER_BUILD_NUMBER)</string>

    <!-- iOS Specific Configurations -->
    <key>LSRequiresIPhoneOS</key>
    <true/>

    <key>UIApplicationSupportsIndirectInputEvents</key>
    <true/>

    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>

    <key>UIMainStoryboardFile</key>
    <string>Main</string>

    <!-- Supported Interface Orientations -->
    <key>UISupportedInterfaceOrientations</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>

    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationPortraitUpsideDown</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>

    <!-- Status Bar Settings -->
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <false/>

    <!-- Firebase Configurations -->
    <key>FirebaseAutomaticScreenReportingEnabled</key>
    <false/>

    <!-- App Permissions -->
    <key>NSPhotoLibraryUsageDescription</key>
    <string>This app requires access to the photo library.</string>

    <key>NSMicrophoneUsageDescription</key>
    <string>This app requires access to the microphone.</string>

    <key>NSCameraUsageDescription</key>
    <string>This app requires access to the camera.</string>

    <!-- App Minimum Version -->
    <key>MinimumOSVersion</key>
    <string>13.0</string>

    <!-- Disable Unnecessary Features -->
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>
    <key>GIDClientID</key>
    <!-- TODO Replace this value: -->
    <!-- Copied from GoogleService-Info.plist key CLIENT_ID -->
    <string>[YOUR IOS CLIENT ID]</string>
    <!-- Put me in the [my_project]/ios/Runner/Info.plist file -->
    <!-- Google Sign-in Section -->
    <key>CFBundleURLTypes</key>
    <array>
    	<dict>
    		<key>CFBundleTypeRole</key>
    		<string>Editor</string>
    		<key>CFBundleURLSchemes</key>
    		<array>
    			<!-- TODO Replace this value: -->
    			<!-- Copied from GoogleService-Info.plist key REVERSED_CLIENT_ID -->
    			<string>com.googleusercontent.apps.861823949799-vc35cprkp249096uujjn0vvnmcvjppkn</string>
    		</array>
    	</dict>
    </array>
    <!-- End of the Google Sign-in Section -->
</dict>
</plist>
