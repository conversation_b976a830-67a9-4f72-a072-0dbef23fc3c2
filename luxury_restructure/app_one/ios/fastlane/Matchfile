git_url("https://github.com/suvandnatflutter1/ios-code-signing.git")

storage_mode("git")

type("development") # The default type, can be: appstore, adhoc, enterprise or development
app_identifier("com.app.appone")

type("appstore") # The default type, can be: appstore, adhoc, enterprise or development
app_identifier("com.app.appone")

type("adhoc") # The default type, can be: appstore, adhoc, enterprise or development
app_identifier("com.app.appone")

username("<EMAIL>") # Your Apple Developer Portal username

# app_identifier(["tools.fastlane.app", "tools.fastlane.app2"])
# username("<EMAIL>") # Your Apple Developer Portal username

# For all available options run `fastlane match --help`
# Remove the # in the beginning of the line to enable the other options

# The docs are available on https://docs.fastlane.tools/actions/match
